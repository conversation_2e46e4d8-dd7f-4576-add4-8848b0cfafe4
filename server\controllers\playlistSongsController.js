const PlaylistSongsModel = require("../models/playlistSongsModel");

const PlaylistSongsController = {
  create: async (req, res) => {
    try {
      const id = await PlaylistSongsModel.create(req.body);
      res.status(201).json(id);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getAll: async (req, res) => {
    try {
      const result = await PlaylistSongsModel.getAll();
      res.json(result);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getById: async (req, res) => {
    try {
      const result = await PlaylistSongsModel.getById(req.params.id);
      if (!result) return res.status(404).json({ error: "Not found" });
      res.json(result);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  update: async (req, res) => {
    try {
      const updated = await PlaylistSongsModel.update(req.params.id, req.body);
      res.json(updated);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  delete: async (req, res) => {
    try {
      const deleted = await PlaylistSongsModel.delete(req.params.id);
      res.json(deleted);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  deleteByPlaylistAndSong: async (req, res) => {
    try {
      const { playlist_master_id, songs_master_id } = req.params;
      const result = await PlaylistSongsModel.deleteByPlaylistAndSong(
        playlist_master_id,
        songs_master_id
      );
      res.json(result);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getSongsByPlaylistId: async (req, res) => {
    try {
      const { playlist_master_id } = req.params;
      const songs = await PlaylistSongsModel.getSongsByPlaylistId(
        playlist_master_id
      );
      res.json(songs);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getPlaylistsBySongId: async (req, res) => {
    try {
      const { songs_master_id } = req.params;
      const playlists = await PlaylistSongsModel.getPlaylistsBySongId(
        songs_master_id
      );
      res.json(playlists);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },
};

module.exports = PlaylistSongsController;
