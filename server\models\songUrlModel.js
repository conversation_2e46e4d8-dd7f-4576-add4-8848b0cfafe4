const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SongUrlModel = {
  create: ({ songs_master_id, song_url, url_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO song_urls (songs_master_id, song_url, url_type_lookup_id)
        VALUES (?, ?, ?)
      `;
      db.run(
        query,
        [songs_master_id, song_url, url_type_lookup_id],
        function (err) {
          if (err) return reject(err);
          resolve({ id: this.lastID });
        }
      );
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM song_urls`, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM song_urls WHERE song_urls_id = ?`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, { songs_master_id, song_url, url_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE song_urls
        SET songs_master_id = ?, song_url = ?, url_type_lookup_id = ?
        WHERE song_urls_id = ?
      `;
      db.run(
        query,
        [songs_master_id, song_url, url_type_lookup_id, id],
        function (err) {
          if (err) return reject(err);
          resolve({ updated: this.changes });
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM song_urls WHERE song_urls_id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  getBySongId: (songs_master_id) => {
    return new Promise((resolve, reject) => {
      const query = `
  SELECT song_urls.*, url_type_lookup.url_type_name 
  FROM song_urls 
  JOIN url_type_lookup ON song_urls.url_type_lookup_id = url_type_lookup.url_type_lookup_id 
  WHERE songs_master_id = ?
`;
      db.all(query, [songs_master_id], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },
};

module.exports = SongUrlModel;
