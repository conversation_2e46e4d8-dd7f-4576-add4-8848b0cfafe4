import React, { useEffect, useState } from 'react';
import { Box, Typography, Avatar, Grid } from '@mui/material';

const ArtistsSection = ({ onArtistClick }) => {
    const [artists, setArtists] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Query to fetch all artists with their name and imageUrl
    const query = `
query {
  getAllArtists {
    name
    imageUrl
  }
}
`;

    useEffect(() => {
        const fetchArtists = async () => {
            setLoading(true);
            try {
                const response = await fetch(`${process.env.REACT_APP_API_URL}/graphql`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query
                    }),
                });

                const result = await response.json();
                setArtists(result.data.getAllArtists);
                setLoading(false);
            } catch (err) {
                setError('Error fetching artists');
                setLoading(false);
            }
        };

        fetchArtists();
    }, []);


    if (loading) return <p>Loading...</p>;
    if (error) return <p>{error}</p>;

    return (
        <Box sx={{ padding: 2 }}>
            <Typography variant="h4" gutterBottom sx={{ color: "#fff" }}>
                All Artists
            </Typography>
            <Grid container spacing={2}>
                {artists.map((artist, index) => (
                    <Grid item xs={6} sm={4} md={3} key={index}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                cursor: 'pointer',
                            }}
                            onClick={() => onArtistClick(artist)}
                        >
                            <Avatar
                                sx={{
                                    width: 80,
                                    height: 80,
                                    marginBottom: 1,
                                    backgroundColor: "#966300",
                                }}
                                src={artist.imageUrl || 'default_artist_image.png'}
                                alt={artist.name}
                            />
                            <Typography
                                variant="body1"
                                sx={{ color: '#fff', textAlign: 'center' }}
                            >
                                {artist.name}
                            </Typography>
                        </Box>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};

export default ArtistsSection;