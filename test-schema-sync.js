#!/usr/bin/env node

// Quick test script for schema synchronization
const path = require('path');
const { DatabaseManager } = require('./server/database');

async function testSchemaSync() {
  console.log('🧪 Testing Schema Synchronization System');
  console.log('=' .repeat(50));

  const dbManager = new DatabaseManager({
    dbPath: path.join(__dirname, 'app.db'),
    schemaPath: path.join(__dirname, 'server/database/schema.sql'),
    enableWatcher: false // Disable watcher for testing
  });

  try {
    console.log('1️⃣ Initializing database manager...');
    await dbManager.initialize();

    console.log('2️⃣ Checking schema status...');
    const status = await dbManager.getSchemaStatus();
    console.log(`   Database: ${status.dbPath}`);
    console.log(`   Schema: ${status.schemaPath}`);
    console.log(`   In Sync: ${status.inSync ? '✅' : '❌'}`);

    if (!status.inSync) {
      console.log('3️⃣ Performing schema synchronization...');
      const result = await dbManager.forceSchemaSync();
      
      if (result.migrationNeeded) {
        console.log(`   ✅ Migration completed!`);
        console.log(`   📊 Changes: ${result.changes.length}`);
        
        // Show first few log entries
        if (result.changes.length > 0) {
          console.log('   📝 Recent changes:');
          result.changes.slice(0, 5).forEach(change => {
            console.log(`      ${change}`);
          });
          if (result.changes.length > 5) {
            console.log(`      ... and ${result.changes.length - 5} more`);
          }
        }
      } else {
        console.log('   ℹ️  No migration needed');
      }
    } else {
      console.log('3️⃣ Schema already synchronized ✅');
    }

    console.log('4️⃣ Getting database info...');
    const db = dbManager.getDatabase();
    
    // Get table count
    const tables = await new Promise((resolve, reject) => {
      db.all(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows.map(row => row.name));
      });
    });

    console.log(`   📊 Total tables: ${tables.length}`);
    console.log(`   📋 Tables: ${tables.slice(0, 5).join(', ')}${tables.length > 5 ? '...' : ''}`);

    console.log('5️⃣ Testing migration history...');
    const history = await dbManager.getMigrationHistory();
    console.log(`   📚 Migration records: ${history.length}`);
    
    if (history.length > 0) {
      const latest = history[0];
      console.log(`   📅 Latest: ${latest.schema_version} (${latest.status})`);
    }

    console.log('\n🎉 Schema synchronization test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await dbManager.close();
  }
}

// Run test if called directly
if (require.main === module) {
  testSchemaSync().catch(console.error);
}

module.exports = testSchemaSync;
