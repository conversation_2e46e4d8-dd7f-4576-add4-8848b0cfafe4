const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./database/app.db');

// Mapping table and column names for each type
const lookupConfig = {
  genre: {
    table: 'genre_type_lookup',
    id: 'genre_type_lookup_id',
    name: 'genre_type_name',
    description: 'genre_type_description'
  },
  url: {
    table: 'url_type_lookup',
    id: 'url_type_lookup_id',
    name: 'url_type_name',
    description: 'url_type_description'
  },
  file: {
    table: 'file_type_lookup',
    id: 'file_type_lookup_id',
    name: 'file_type_name',
    description: 'file_type_description'
  },
  playlist: {
    table: 'playlist_type_lookup',
    id: 'playlist_type_lookup_id',
    name: 'playlist_type_name',
    description: 'playlist_type_description'
  },
  difficulty: {
    table: 'difficulty_level_lookup',
    id: 'difficulty_id',
    name: 'level_name',
    description: 'display_image'
  },
  'question-type': {
    table: 'question_type_lookup',
    id: 'question_type_id',
    name: 'type_name',
    description: 'display_name'
  },
  'answer-type': {
    table: 'answer_type_lookup',
    id: 'answer_type_id',
    name: 'type_name',
    description: 'display_name'
  }
};

function getConfig(type) {
  const config = lookupConfig[type];
  if (!config) throw new Error('Invalid lookup type');
  return config;
}

const LookupModel = {
  create: (type, data) => {
    const { name, description, status = 1, bonus_points, time_multiplier } = data;
    const { table, name: nameCol, description: descCol } = getConfig(type);

    return new Promise((resolve, reject) => {
      let query, params;

      if (type === 'difficulty') {
        // Special handling for difficulty with extra fields
        query = `INSERT INTO ${table} (${nameCol}, ${descCol}, bonus_points, time_multiplier, status, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`;
        params = [name, description, bonus_points || 10, time_multiplier || 1.0, status];
      } else {
        // Standard handling for other lookup types
        query = `INSERT INTO ${table} (${nameCol}, ${descCol}, status, created_at) VALUES (?, ?, ?, datetime('now'))`;
        params = [name, description, status];
      }

      db.run(query, params, function (err) {
        if (err) return reject(err);
        resolve({ id: this.lastID, name, description, status, bonus_points, time_multiplier });
      });
    });
  },

  getAll: (type) => {
    const { table } = getConfig(type);
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM ${table}`, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getById: (type, id) => {
    const { table, id: idCol } = getConfig(type);
    return new Promise((resolve, reject) => {
      db.get(`SELECT * FROM ${table} WHERE ${idCol} = ?`, [id], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });
  },

  update: (type, id, data) => {
    const { name, description, status, bonus_points, time_multiplier } = data;
    const { table, id: idCol, name: nameCol, description: descCol } = getConfig(type);

    return new Promise((resolve, reject) => {
      let query, params;

      if (type === 'difficulty') {
        // Special handling for difficulty with extra fields
        query = `UPDATE ${table} SET ${nameCol} = ?, ${descCol} = ?, bonus_points = ?, time_multiplier = ?, status = ? WHERE ${idCol} = ?`;
        params = [name, description, bonus_points, time_multiplier, status, id];
      } else {
        // Standard handling for other lookup types
        query = `UPDATE ${table} SET ${nameCol} = ?, ${descCol} = ?, status = ? WHERE ${idCol} = ?`;
        params = [name, description, status, id];
      }

      db.run(query, params, function (err) {
        if (err) return reject(err);
        resolve({ updated: this.changes });
      });
    });
  },

  delete: (type, id) => {
    const { table, id: idCol } = getConfig(type);
    return new Promise((resolve, reject) => {
      db.run(`DELETE FROM ${table} WHERE ${idCol} = ?`, [id], function (err) {
        if (err) return reject(err);
        resolve({ deleted: this.changes });
      });
    });
  }
};

module.exports = LookupModel;