import React, { useState } from "react";
import {
  Avatar,
  Button,
  TextField,
  Paper,
  Box,
  Typography,
  Container,
} from "@mui/material";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import { useNavigate } from "react-router-dom";

export default function LoginPage() {
  const [form, setForm] = useState({ username: "", password: "" });
  const navigate = useNavigate();

  const handleChange = (e) =>
    setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL}/api/auth/login`,
        form,
        { withCredentials: true }
      );
      toast.success("Login successful!", { position: "top-center" });
      localStorage.setItem("isAuthenticated", "true");
      navigate("/AdminHomePage");
    } catch (err) {
      toast.error(err.response?.data?.message || "Login failed", {
        position: "top-center",
      });
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper
        elevation={6}
        sx={{
          mt: 8,
          p: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Avatar sx={{ m: 1, bgcolor: "#966300" }}>
          <LockOutlinedIcon />
        </Avatar>
        <Typography component="h1" variant="h5">
          Admin Login
        </Typography>
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
          <TextField
            fullWidth
            id="username"
            name="username"
            label="Username"
            value={form.username}
            onChange={handleChange}
            variant="outlined"
            // autoFocus
            required
          />
          <TextField
            margin="normal"
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            value={form.password}
            onChange={handleChange}
            required
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            // color="primary"
            sx={{ mt: 2, mb: 2, backgroundColor: "#966300", color: "#fff" }}
          >
            Log In
          </Button>
        </Box>
      </Paper>
      <ToastContainer autoClose={3000} hideProgressBar position="top-center" />
    </Container>
  );
}
