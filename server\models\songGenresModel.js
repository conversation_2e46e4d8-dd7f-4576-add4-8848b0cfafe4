const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SongGenresModel = {
  create: ({ songs_master_id, genre_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT OR IGNORE INTO song_genres (songs_master_id, genre_type_lookup_id)
        VALUES (?, ?)
      `;
      db.run(query, [songs_master_id, genre_type_lookup_id], function (err) {
        if (err) return reject(err);
        resolve({ id: this.lastID });
      });
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM song_genres`, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM song_genres WHERE song_genres_id = ?`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, { songs_master_id, genre_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE song_genres
        SET songs_master_id = ?, genre_type_lookup_id = ?
        WHERE song_genres_id = ?
      `;
      db.run(
        query,
        [songs_master_id, genre_type_lookup_id, id],
        function (err) {
          if (err) return reject(err);
          resolve({ updated: this.changes });
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM song_genres WHERE song_genres_id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  getBySongId: (songId) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT sg.*, g.genre_type_name 
      FROM song_genres sg
      JOIN genre_type_lookup g ON sg.genre_type_lookup_id = g.genre_type_lookup_id
      WHERE sg.songs_master_id = ?
    `;
      db.all(query, [songId], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  deleteBySongId: (songId) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM song_genres WHERE songs_master_id = ?`,
        [songId],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },
};

module.exports = SongGenresModel;
