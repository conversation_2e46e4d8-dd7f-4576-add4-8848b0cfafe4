// utils/requestedSongsUtils.js
import Cookies from "js-cookie";

export const getRequestedSongsFromCookie = (deviceId) => {
  const cookieValue = Cookies.get(`requestedSongs_${deviceId}`);
  if (!cookieValue) return {};

  try {
    const parsed = JSON.parse(cookieValue);
    // Remove expired ones:
    const now = new Date();
    Object.keys(parsed).forEach((songId) => {
      if (new Date(parsed[songId]) < now) {
        delete parsed[songId];
      }
    });
    return parsed;
  } catch (err) {
    console.error("Failed to parse requestedSongs cookie:", err);
    return {};
  }
};

export const setRequestedSongsToCookie = (deviceId, requestedSongs) => {
  Cookies.set(`requestedSongs_${deviceId}`, JSON.stringify(requestedSongs), {
    expires: 1, // expires in 1 day
  });
};

export const clearRequestedSongsCookie = (deviceId) => {
  Cookies.remove(`requestedSongs_${deviceId}`);
};