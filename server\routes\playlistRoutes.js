const express = require("express");
const router = express.Router();
const playlistController = require("../controllers/playlistController");
const upload = require("../middleware/playlistUploadMiddleware");

router.post("/", upload.single("image"), playlistController.create);
router.put("/:id", upload.single("image"), playlistController.update);
router.get("/", playlistController.getAll);
router.get("/:id", playlistController.getById);
router.delete("/:id", playlistController.remove);

module.exports = router;
