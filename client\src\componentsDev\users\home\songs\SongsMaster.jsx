import React, { useState } from "react";
import { Box, Typography, IconButton, Collapse } from "@mui/material";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SongsListSection from "./SongsListSection";
import { useSongs } from "../../../hooks/useSongs";

const SongsMaster = () => {
  const [showAllSongs, setShowAllSongs] = useState(true);
  const { data: songs = [], isLoading, error } = useSongs();

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography color="error">
          Error loading songs. Please try again later.
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      id="songs"
      sx={{
        px: 2,
        mb: 3,
        marginTop: showAllSongs ? "2rem" : "0rem",
        display: "flex",
        flexDirection: "column",
        marginBottom: "5rem",
      }}
    >
      {/* Sticky Header */}
      <Box
        sx={{
          position: "sticky",
          top: 90,
          zIndex: 1,
          backgroundColor: "background.default",
          py: 2,
          marginInline: "auto",
          width: "98% !important",
          px: 2,
          display: showAllSongs ? "block" : "none",
        }}
      />

      {/* Toggle Header */}
      <Box
        onClick={() => setShowAllSongs((prev) => !prev)}
        sx={{
          position: "sticky",
          top: 110,
          zIndex: 1,
          backgroundColor: "background.default",
          py: showAllSongs ? 1 : 0.5,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          gap: "24px",
          marginBottom: showAllSongs ? "32px" : "35px",
          width: "100% !important",
          border: "1px solid #966300",
          paddingLeft: "15px",
          borderRadius: "5px",
          marginInline: "auto",
          cursor: "pointer",
          transition: "all 0.3s ease",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "24px",
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            style={{ flexShrink: 0 }}
          >
            <path
              fill="none"
              stroke="#966300"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
            />
          </svg>
          <Typography
            variant="h6"
            sx={{
              fontSize: "20px",
              fontWeight: "bold",
              color: "#FFF",
              lineHeight: "1.2",
            }}
          >
            All Songs {songs.length > 0 ? `(${songs.length})` : ""}
          </Typography>
        </Box>
        <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
          {showAllSongs ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>

      {/* Collapsible Songs List */}
      <Collapse in={showAllSongs} timeout="auto" unmountOnExit>
        <SongsListSection songs={songs} isLoading={isLoading} />
      </Collapse>
    </Box>
  );
};

export default SongsMaster;