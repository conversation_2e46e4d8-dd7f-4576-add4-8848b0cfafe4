import React, { useState, useEffect } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  TextField,
  Divider,
  CircularProgress,
  Chip,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import axios from "axios";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

const COOLDOWN_MS = 60 * 60 * 1000; // 1 hour

const formatTime = (ms) => {
  const minutes = Math.floor(ms / 60000);
  const seconds = Math.floor((ms % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
};

const ShowdownTab = () => {
  const [registrations, setRegistrations] = useState([]);
  const [search, setSearch] = useState("");
  const [timeNow, setTimeNow] = useState(Date.now()); // For live updates

  // Update time every second for live countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeNow(Date.now());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/showdown/registrations`
        );
        setRegistrations(res.data);
      } catch (error) {
        console.error("Failed to fetch registrations:", error);
      }
    };
    fetchData();
  }, []);

  // Group users by date with live cooldown calculation
  const groupedByDate = registrations.reduce((acc, user) => {
    const date = new Date(user.created_at).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });

    const timePassed = timeNow - user.last_registration_time;
    const isCooldown = timePassed < COOLDOWN_MS;
    const timeRemaining = isCooldown ? formatTime(COOLDOWN_MS - timePassed) : "Available";

    const newUser = {
      ...user,
      isCooldown,
      timeRemaining,
      cooldownProgress: isCooldown ? (timePassed / COOLDOWN_MS) * 100 : 100
    };

    if (!acc[date]) acc[date] = [];
    acc[date].push(newUser);
    return acc;
  }, {});

  // Filter by search
  const filteredGrouped = {};
  Object.entries(groupedByDate).forEach(([date, users]) => {
    const filteredUsers = users.filter(
      (u) =>
        u.name.toLowerCase().includes(search.toLowerCase()) ||
        u.email.toLowerCase().includes(search.toLowerCase())
    );
    if (filteredUsers.length) filteredGrouped[date] = filteredUsers;
  });

  return (
    <Box sx={{ mt: 2 }}>
      <TextField
        label="Search by name or email"
        variant="outlined"
        fullWidth
        margin="normal"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      {Object.keys(filteredGrouped).length === 0 && (
        <Typography sx={{ color: "#aaa", mt: 2 }}>No registrations found</Typography>
      )}

      {Object.entries(filteredGrouped).map(([date, users]) => (
        <Accordion key={date} sx={{ bgcolor: "#1a1a1a", color: "#fff", mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}>
            <Typography fontWeight="bold">
              {date} ({users.length} {users.length === 1 ? "user" : "users"})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {users.map((user) => (
              <Box
                key={user.id}
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                py={1}
                px={1}
                sx={{ borderBottom: "1px solid #333" }}
              >
                <Box>
                  <Typography fontWeight="bold">{user.name}</Typography>
                  <Typography variant="body2" color="#aaa">
                    {user.email}
                  </Typography>
                  <Typography variant="body2" color="#aaa">
                    IP: {user.ip_address}
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={2}>
                  {user.isCooldown ? (
                    <>
                      <CircularProgress
                        variant="determinate"
                        value={user.cooldownProgress}
                        size={24}
                        thickness={4}
                        sx={{ color: "orange" }}
                      />
                      <Chip
                        icon={<AccessTimeIcon />}
                        label={`Cooldown: ${user.timeRemaining}`}
                        color="warning"
                        variant="outlined"
                      />
                    </>
                  ) : (
                    <Chip
                      icon={<CheckCircleOutlineIcon />}
                      label="Available"
                      color="success"
                      variant="outlined"
                    />
                  )}
                </Box>
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default ShowdownTab;