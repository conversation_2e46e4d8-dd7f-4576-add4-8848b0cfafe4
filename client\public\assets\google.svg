<svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2308_1016)">
<rect x="2" y="1.5" width="44" height="44" rx="8" fill="white"/>
<rect x="2.5" y="2" width="43" height="43" rx="7.5" stroke="#D0D5DD"/>
<g clip-path="url(#clip0_2308_1016)">
<path d="M35.7663 23.7764C35.7663 22.9607 35.7001 22.1406 35.559 21.3381H24.2402V25.9591H30.722C30.453 27.4494 29.5888 28.7678 28.3233 29.6056V32.6039H32.1903C34.4611 30.5139 35.7663 27.4274 35.7663 23.7764Z" fill="#4285F4"/>
<path d="M24.2401 35.5008C27.4766 35.5008 30.2059 34.4382 32.1945 32.6039L28.3276 29.6055C27.2517 30.3375 25.8627 30.752 24.2445 30.752C21.1139 30.752 18.4595 28.6399 17.507 25.8003H13.5166V28.8912C15.5537 32.9434 19.7029 35.5008 24.2401 35.5008Z" fill="#34A853"/>
<path d="M17.5028 25.8003C17.0001 24.3099 17.0001 22.6961 17.5028 21.2057V18.1148H13.5167C11.8147 21.5056 11.8147 25.5004 13.5167 28.8912L17.5028 25.8003Z" fill="#FBBC04"/>
<path d="M24.2401 16.2497C25.9509 16.2232 27.6044 16.867 28.8434 18.0487L32.2695 14.6226C30.1001 12.5855 27.2208 11.4655 24.2401 11.5008C19.7029 11.5008 15.5537 14.0582 13.5166 18.1148L17.5026 21.2058C18.4506 18.3617 21.1095 16.2497 24.2401 16.2497Z" fill="#EA4335"/>
</g>
</g>
<defs>
<filter id="filter0_d_2308_1016" x="0" y="0.5" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2308_1016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2308_1016" result="shape"/>
</filter>
<clipPath id="clip0_2308_1016">
<rect width="24" height="24" fill="white" transform="translate(12 11.5)"/>
</clipPath>
</defs>
</svg>
