import React from "react";
import {
  App<PERSON><PERSON>,
  Too<PERSON><PERSON>,
  Typo<PERSON>,
  Container,
  IconButton,
} from "@mui/material";
import { styled } from "@mui/system";
import { Link, useNavigate } from "react-router-dom"; // Import the Link component
import LogoutIcon from "@mui/icons-material/Logout";
import axios from "axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const StyledAppBar = styled(AppBar)({
  backgroundColor: "#1DB850", // Spotify green
});

const Header = () => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/logout`);
      localStorage.removeItem("isAuthenticated");
      toast.info("Logged out successfully");
      navigate("/AdminLogin");
    } catch (err) {
      toast.error("Logout failed");
      console.error("Logout error:", err);
    }
  };

  return (
    <>
      <StyledAppBar position="static">
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            <Link
              to="/AdminHomePage"
              style={{ textDecoration: "none", color: "inherit", flexGrow: 1 }}
            >
              <Typography variant="h6">Admin Dashboard</Typography>
            </Link>
            <IconButton color="inherit" onClick={handleLogout} edge="end">
              <LogoutIcon /> Logout
            </IconButton>
          </Toolbar>
        </Container>
      </StyledAppBar>
      <ToastContainer position="top-right" autoClose={3000} />
    </>
  );
};

export default Header;
