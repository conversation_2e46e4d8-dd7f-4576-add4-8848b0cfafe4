import { useState, useEffect, useCallback, useRef } from "react";
import {
    Box,
    Typography,
    Tabs,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    Chip,
    Button,
    CircularProgress,
    useMediaQuery,
    useTheme,
    Tooltip,
    TextField,
    Stack,
} from "@mui/material";
import {
    CheckCircle,
    Close,
    MusicNote,
    Person,
    Message,
    AccessTime,
    RadioButtonUnchecked,
    TimerOff,
    YouTube,
    FilterList,
} from "@mui/icons-material"; 
// import { DatePicker } from "@mui/x-date-pickers/DatePicker";
// import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { timeSince } from "../../../../utils/timeSince"; 
// import Header from "./Header"; 
import io from "socket.io-client";

// Status colors and config
const statusConfig = {
    "Song-Q": {
        color: "#FFA726", // Orange
        icon: <AccessTime color="warning" />,
    },
    Played: {
        color: "#4CAF50", // Green
        icon: <CheckCircle color="success" />,
    },
    "Not Played": {
        color: "#F44336", // Red
        icon: <TimerOff color="error" />,
    },
};

// Action type colors
const actionTypeColors = {
    singForMe: "#2196F3", // Blue
    singWithYou: "#4CAF50", // Green
    singAlone: "#9C27B0", // Purple
};

const COOLDOWN_MS = 60 * 60 * 1000; // 1 hour

const AdminRequests = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
    const [selectedTab, setSelectedTab] = useState("Song-Q");
    const [requests, setRequests] = useState([]);
    const [selectedSong, setSelectedSong] = useState(null);
    const [openModal, setOpenModal] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentTime, setCurrentTime] = useState(Date.now());
    
    // Filter states for Played/Not Played tabs
    // const [showFilters, setShowFilters] = useState(false);
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

    const socketRef = useRef(null);
    const isInitialMount = useRef(true);

    const getRequestStatus = useCallback((request) => {
        const now = new Date();
        const expiresAt = new Date(request.expires_at);
        if (request.status === "Fulfilled") return "Played";
        if (now >= expiresAt) return "Not Played";
        return "Song-Q";
    }, []);

    const fetchRequests = useCallback(async () => {
        setLoading(true);
        try {
            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests`,
                {
                    headers: {
                        'Cache-Control': 'no-cache'
                    }
                }
            );
            const data = await response.json();

            if (response.ok) {
                const requestsWithSongs = await Promise.all(
                    data.map(async (req) => {
                        try {
                            const songRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/songs/${req.songs_master_id}`
                            );
                            const songData = await songRes.json();

                            // Fetch URLs and files count
                            const urlsRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${req.songs_master_id}`
                            );
                            const urlsData = await urlsRes.json();

                            const filesRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/song-files/song/${req.songs_master_id}`
                            );
                            const filesData = await filesRes.json();

                            // Filter for YouTube URLs specifically
                            const youtubeLinks = urlsData.filter((url) =>
                                url.song_url && (
                                    url.song_url.includes('youtube.com') ||
                                    url.song_url.includes('youtu.be') ||
                                    (url.url_type_name && url.url_type_name.toLowerCase().includes('youtube'))
                                ));

                            const validFiles = filesData.filter((file) => file.file);

                            return {
                                ...req,
                                song_name: songData.name || "Unknown Song",
                                artist: songData.artist || "Unknown Artist",
                                displayStatus: getRequestStatus(req),
                                hasYoutubeLinks: youtubeLinks.length > 0,
                                youtubeCount: youtubeLinks.length,
                                hasFiles: validFiles.length > 0,
                                fileCount: validFiles.length,
                            };
                        } catch {
                            return {
                                ...req,
                                song_name: "Unknown Song",
                                artist: "Unknown Artist",
                                displayStatus: getRequestStatus(req),
                                hasYoutubeLinks: false,
                                youtubeCount: 0,
                                hasFiles: false,
                                fileCount: 0,
                            };
                        }
                    })
                );

                setRequests(requestsWithSongs);
            }
        } catch (error) {
            console.error("Error fetching requests:", error);
        } finally {
            setLoading(false);
        }
    }, [getRequestStatus]);

    useEffect(() => {
        // Initialize socket connection only once
        if (!socketRef.current) {
            socketRef.current = io(process.env.REACT_APP_API_URL, {
                reconnectionAttempts: 5,
                reconnectionDelay: 1000,
            });

            socketRef.current.on("new-song-request", (newRequest) => {
                setRequests(prev => {
                    const exists = prev.some(req => req.song_request_id === newRequest.song_request_id);
                    if (!exists) {
                        return [{
                            ...newRequest,
                            displayStatus: getRequestStatus(newRequest)
                        }, ...prev];
                    }
                    return prev;
                });
            });

            socketRef.current.on("song-request-updated", (updatedRequest) => {
                setRequests(prev => prev.map(req =>
                    req.song_request_id === updatedRequest.song_request_id ? {
                        ...updatedRequest,
                        displayStatus: getRequestStatus(updatedRequest)
                    } : req
                ));
            });
        }

        // Initial data fetch
        if (isInitialMount.current) {
            fetchRequests();
            isInitialMount.current = false;
        }

        // Update current time every minute for expiration checks
        const timeInterval = setInterval(() => {
            setCurrentTime(Date.now());
        }, 60000);

        // Update request statuses periodically (every 5 minutes)
        const statusUpdateInterval = setInterval(() => {
            setRequests(prev => prev.map(req => ({
                ...req,
                displayStatus: getRequestStatus(req)
            })));
        }, 5 * 60 * 1000);

        return () => {
            clearInterval(timeInterval);
            clearInterval(statusUpdateInterval);
            if (socketRef.current) {
                socketRef.current.disconnect();
                socketRef.current = null;
            }
        };
    }, [fetchRequests, getRequestStatus]);

    const handleStatusChange = async (requestId, songMasterId, currentStatus, actionType) => {
        const newStatus = currentStatus === "Song-Q" ? "Played" : "Song-Q";
        
        let requestsToUpdate = [];
        
        if (actionType === "singForMe") {
            // Batch update: Get all singForMe requests for this song
            requestsToUpdate = requests.filter(req => 
                req.songs_master_id === songMasterId && req.action_type === "singForMe"
            );
        } else {
            // Individual update: Only update the specific request
            requestsToUpdate = requests.filter(req => req.song_request_id === requestId);
        }
        
        try {
            // Update all relevant requests
            await Promise.all(requestsToUpdate.map(async (req) => {
                const response = await fetch(
                    `${process.env.REACT_APP_API_URL}/api/song-requests/${req.song_request_id}/song/${songMasterId}`,
                    {
                        method: "PATCH",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                            status: newStatus === "Played" ? "Fulfilled" : "Pending"
                        }),
                    }
                );

                if (!response.ok) {
                    throw new Error(await response.text());
                }

                return response.json();
            }));

            // Update local state for all relevant requests
            setRequests(prev => prev.map(req => {
                const shouldUpdate = requestsToUpdate.some(r => r.song_request_id === req.song_request_id);
                return shouldUpdate ? {
                    ...req,
                    status: newStatus === "Played" ? "Fulfilled" : "Pending",
                    displayStatus: newStatus,
                    fulfilled_at: newStatus === "Played" ? new Date().toISOString() : null
                } : req;
            }));

            // Notify other clients via socket for all updated requests
            if (socketRef.current) {
                requestsToUpdate.forEach(req => {
                    socketRef.current.emit("song-request-updated", {
                        ...req,
                        status: newStatus === "Played" ? "Fulfilled" : "Pending",
                        displayStatus: newStatus,
                        fulfilled_at: newStatus === "Played" ? new Date().toISOString() : null
                    });
                });
            }

        } catch (error) {
            console.error("Error updating status:", error);
        }
    };

    const handleOpenDetails = async (songId, request) => {
        try {
            const songRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/songs/${songId}`
            );
            const songData = await songRes.json();

            const urlsRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songId}`
            );
            const urlsData = await urlsRes.json();

            const filesRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-files/song/${songId}`
            );
            const filesData = await filesRes.json();

            // Get requests for this song based on current tab
            let songRequests;
            if (selectedTab === "Song-Q") {
                // Only show current Song-Q requests
                songRequests = requests.filter(req => 
                    req.songs_master_id === songId && req.displayStatus === "Song-Q"
                );
            } else {
                // Show all requests for this song (including past ones)
                songRequests = requests.filter(req => req.songs_master_id === songId);
            }

            setSelectedSong({
                ...songData,
                request: {
                    requests: songRequests,
                    ...request
                },
                urls: urlsData,
                files: filesData
            });
            setOpenModal(true);
        } catch (error) {
            console.error("Error loading song info:", error);
        }
    };

    // Filter requests by selected tab and date range
    const getFilteredRequests = () => {
        let filtered = requests.filter((req) => {
            // Tab-based filtering
            if (selectedTab === "Song-Q") {
                // Only show current Song-Q requests (no past requests)
                return req.displayStatus === "Song-Q";
            } else {
                // For Played/Not Played tabs, show all requests including past ones
                return selectedTab === "All" || req.displayStatus === selectedTab;
            }
        });

        // Date range filtering for Played/Not Played tabs
        if ((selectedTab === "Played" || selectedTab === "Not Played") && (startDate || endDate)) {
            filtered = filtered.filter(req => {
                const reqDate = new Date(req.created_at);
                if (startDate && reqDate < startDate) return false;
                if (endDate && reqDate > endDate) return false;
                return true;
            });
        }

        return filtered;
    };

    const filteredRequests = getFilteredRequests();

    // Sort by most recent first (individual requests)
    const sortedRequests = [...filteredRequests].sort((a, b) =>
        new Date(b.created_at) - new Date(a.created_at)
    );

    // Calculate request counts for each song by action type
    const getRequestCountForSong = (songId, actionType) => {
        let baseRequests;
        
        if (selectedTab === "Song-Q") {
            // Only count current Song-Q requests
            baseRequests = requests.filter(req => 
                req.songs_master_id === songId && 
                req.action_type === actionType &&
                req.displayStatus === "Song-Q"
            );
        } else {
            // Count all requests for this song and action type
            baseRequests = requests.filter(req => 
                req.songs_master_id === songId && 
                req.action_type === actionType
            );
        }
        
        return baseRequests.length;
    };

    const resetFilters = () => {
        setStartDate(null);
        setEndDate(null);
    };

    const shouldShowFilters = selectedTab === "Played" || selectedTab === "Not Played";

    return (
        // <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ p: isMobile ? 1 : 3, bgcolor: "#121212", minHeight: "100vh" }}>
                <Typography variant="h5" gutterBottom sx={{ color: "white", mb: 2 }}>
                    Song Requests
                </Typography>

                <Tabs
                    value={selectedTab}
                    onChange={(e, newValue) => setSelectedTab(newValue)}
                    sx={{
                        mb: 2,
                        "& .MuiTabs-indicator": {
                            backgroundColor: statusConfig[selectedTab]?.color || "#1db954",
                        }
                    }}
                >
                    <Tab label="Song-Q" value="Song-Q" sx={{ color: "white" }} />
                    <Tab label="Played" value="Played" sx={{ color: "white" }} />
                    <Tab label="Not Played" value="Not Played" sx={{ color: "white" }} />
                </Tabs>

                {/* Date Range Filter for Played/Not Played tabs */}
                {/* {shouldShowFilters && (
                    <Box sx={{ mb: 2 }}>
                        <Button
                            startIcon={<FilterList />}
                            onClick={() => setShowFilters(!showFilters)}
                            sx={{ color: "white", mb: 1 }}
                        >
                            {showFilters ? "Hide Filters" : "Show Filters"}
                        </Button>
                        
                        {showFilters && (
                            <Stack direction={isMobile ? "column" : "row"} spacing={2} sx={{ mt: 2 }}>
                                <DatePicker
                                    label="Start Date"
                                    value={startDate}
                                    onChange={setStartDate}
                                    renderInput={(params) => (
                                        <TextField 
                                            {...params} 
                                            size="small"
                                            sx={{ 
                                                '& .MuiOutlinedInput-root': { color: 'white' },
                                                '& .MuiInputLabel-root': { color: 'white' },
                                                '& .MuiOutlinedInput-notchedOutline': { borderColor: 'gray' },
                                            }}
                                        />
                                    )}
                                />
                                <DatePicker
                                    label="End Date"
                                    value={endDate}
                                    onChange={setEndDate}
                                    renderInput={(params) => (
                                        <TextField 
                                            {...params} 
                                            size="small"
                                            sx={{ 
                                                '& .MuiOutlinedInput-root': { color: 'white' },
                                                '& .MuiInputLabel-root': { color: 'white' },
                                                '& .MuiOutlinedInput-notchedOutline': { borderColor: 'gray' },
                                            }}
                                        />
                                    )}
                                />
                                <Button 
                                    onClick={resetFilters}
                                    variant="outlined"
                                    sx={{ color: "white", borderColor: "gray" }}
                                >
                                    Reset
                                </Button>
                            </Stack>
                        )}
                    </Box>
                )} */}

                {loading ? (
                    <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
                        <CircularProgress />
                    </Box>
                ) : sortedRequests.length === 0 ? (
                    <Typography sx={{ color: "white", textAlign: "center", mt: 4 }}>
                        No requests found
                    </Typography>
                ) : (
                    <TableContainer component={Paper} sx={{ bgcolor: "#1e1e1e" }}>
                        <Table>
                            <TableHead>
                                <TableRow sx={{ bgcolor: "#2c2c2c" }}>
                                    <TableCell sx={{ color: "white", fontWeight: "bold" }}>Action</TableCell>
                                    <TableCell sx={{ color: "white", fontWeight: "bold" }}>Requester Name</TableCell>
                                    <TableCell sx={{ color: "white", fontWeight: "bold", paddingLeft: "20px", minWidth: isMobile ? "200px" : "none" }}>Song</TableCell>
                                    <TableCell sx={{ color: "white", fontWeight: "bold" }}>Action Type</TableCell>
                                    <TableCell sx={{ color: "white", fontWeight: "bold" }}>Requests</TableCell>
                                    <TableCell sx={{ color: "white", fontWeight: "bold" }}>Status</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {sortedRequests.map((request, index) => {
                                    const timePassed = currentTime - new Date(request.created_at).getTime();
                                    const timeLeft = COOLDOWN_MS - timePassed;
                                    const isExpiringSoon = timeLeft > 0 && timeLeft < 30 * 60 * 1000;
                                    const requestCount = getRequestCountForSong(request.songs_master_id, request.action_type);

                                    return (
                                        <TableRow
                                            key={request.song_request_id}
                                            hover
                                            onClick={() => handleOpenDetails(request.songs_master_id, request)}
                                            sx={{
                                                cursor: "pointer",
                                                "&:hover": { bgcolor: "#282828" }
                                            }}
                                        >
                                            <TableCell>
                                                <IconButton
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleStatusChange(
                                                            request.song_request_id,
                                                            request.songs_master_id,
                                                            request.displayStatus,
                                                            request.action_type
                                                        );
                                                    }}
                                                    size="small"
                                                    sx={{ color: statusConfig[request.displayStatus]?.color }}
                                                >
                                                    {request.displayStatus === "Played" ? (
                                                        <CheckCircle />
                                                    ) : (
                                                        <RadioButtonUnchecked />
                                                    )}
                                                </IconButton>
                                            </TableCell>
                                            <TableCell sx={{ color: "white" }}>
                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                    <Person sx={{ fontSize: 20 }} />
                                                    <Typography>
                                                        {request.requester_name || "Anonymous"}
                                                    </Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell sx={{ color: "white", maxWidth: isMobile ? "300px" : "none" }}>
                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                    <MusicNote sx={{ fontSize: 20 }} />
                                                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                                                        {request.song_name} <br />
                                                        <Typography variant="caption" color="#9e9e9eff">
                                                            {request.artist}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={request.action_type || "N/A"}
                                                    size="small"
                                                    sx={{
                                                        bgcolor: actionTypeColors[request.action_type] || "#9e9e9e",
                                                        color: "white"
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={requestCount}
                                                    color="primary"
                                                    size="small"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                    <Chip
                                                        label={request.displayStatus}
                                                        size="small"
                                                        sx={{
                                                            bgcolor: statusConfig[request.displayStatus]?.color || "#9e9e9e",
                                                            color: "white"
                                                        }}
                                                    />
                                                    {request.displayStatus === "Song-Q" && timeLeft > 0 && (
                                                        <Tooltip title={`Expires in ${Math.ceil(timeLeft / (60 * 1000))} minutes`}>
                                                            <Chip
                                                                label={`${Math.ceil(timeLeft / (60 * 1000))}m`}
                                                                size="small"
                                                                color={isExpiringSoon ? "warning" : "default"}
                                                                variant="outlined"
                                                            />
                                                        </Tooltip>
                                                    )}
                                                </Box>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                )}

                {/* Details Dialog */}
                <Dialog
                    open={openModal}
                    onClose={() => setOpenModal(false)}
                    maxWidth="md"
                    fullWidth
                    PaperProps={{
                        sx: {
                            bgcolor: "#1e1e1e",
                            color: "white",
                        },
                    }}
                >
                    <DialogTitle sx={{ bgcolor: "#2c2c2c", display: "flex", justifyContent: "space-between" }}>
                        <Typography variant="h6">
                            {selectedSong?.name} {selectedSong?.release_year && `(${selectedSong.release_year})`}
                        </Typography>
                        <IconButton onClick={() => setOpenModal(false)} sx={{ color: "white" }}>
                            <Close />
                        </IconButton>
                    </DialogTitle>

                    <DialogContent dividers sx={{ bgcolor: "#121212" }}>
                        <Box mb={2}>
                            <Typography variant="subtitle1" fontWeight="bold">
                                Artist: <Typography component="span" color="text.secondary">{selectedSong?.artist || "N/A"}</Typography>
                            </Typography>
                        </Box>

                        <Divider sx={{ borderColor: "#333", my: 2 }} />

                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                            Requests ({selectedSong?.request?.requests?.length || 1})
                            {selectedTab === "Song-Q" && (
                                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                                    (Current requests only)
                                </Typography>
                            )}
                        </Typography>

                        {selectedSong?.request?.requests?.map((req, i) => (
                            <Box key={i} sx={{ mb: 2, p: 2, bgcolor: "#1a1a1a", borderRadius: 1 }}>
                                <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                        <Person fontSize="small" />
                                        <Typography>{req.requester_name}</Typography>
                                    </Box>
                                    <Box sx={{ display: "flex", gap: 1 }}>
                                        <Chip
                                            label={req.action_type || "N/A"}
                                            size="small"
                                            sx={{
                                                bgcolor: actionTypeColors[req.action_type] || "#9e9e9e",
                                                color: "white"
                                            }}
                                        />
                                        <Chip
                                            label={req.displayStatus}
                                            size="small"
                                            sx={{
                                                bgcolor: statusConfig[req.displayStatus]?.color || "#9e9e9e",
                                                color: "white"
                                            }}
                                        />
                                    </Box>
                                </Box>

                                {req.dedication_msg && (
                                    <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
                                        <Message fontSize="small" />
                                        <Typography sx={{ fontStyle: "italic" }}>
                                            "{req.dedication_msg}"
                                        </Typography>
                                    </Box>
                                )}

                                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                    <Typography variant="caption" color="text.secondary">
                                        Requested: {timeSince(req.created_at)}
                                    </Typography>
                                    {req.fulfilled_at && (
                                        <Typography variant="caption" color="#4caf50">
                                            Fulfilled: {timeSince(req.fulfilled_at)}
                                        </Typography>
                                    )}
                                </Box>
                            </Box>
                        ))}

                        <Divider sx={{ borderColor: "#333", my: 2 }} />

                        {/* YouTube Links */}
                        <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                YouTube Links ({selectedSong?.urls?.length || 0})
                            </Typography>
                            {selectedSong?.urls?.length > 0 ? (
                                selectedSong.urls.map((url, i) => (
                                    <Box key={i} sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                        <YouTube sx={{ color: "red" }} />
                                        <a
                                            href={url.song_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            style={{ color: "#1db954", wordBreak: "break-all" }}
                                        >
                                            {url.url_type_name || "YouTube Link"}
                                        </a>
                                    </Box>
                                ))
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No YouTube links available
                                </Typography>
                            )}
                        </Box>

                        {/* Song Files */}
                        <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                Song Files ({selectedSong?.files?.length || 0})
                            </Typography>
                            {selectedSong?.files?.length > 0 ? (
                                selectedSong.files.map((file, i) => (
                                    <Box key={i} sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                        <MusicNote sx={{ color: "skyblue" }} />
                                        <a
                                            href={`${process.env.REACT_APP_API_URL}/${file.file}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            style={{ color: "#1db954", wordBreak: "break-all" }}
                                        >
                                            {file.file_type_name || file.file.split("\\").pop()}
                                        </a>
                                    </Box>
                                ))
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No song files available
                                </Typography>
                            )}
                        </Box>
                    </DialogContent>

                    <DialogActions sx={{ bgcolor: "#2c2c2c" }}>
                        <Button
                            onClick={() => setOpenModal(false)}
                            variant="contained"
                            sx={{ bgcolor: "#1db954", "&:hover": { bgcolor: "#1ed760" } }}
                        >
                            Close
                        </Button>
                    </DialogActions>
                </Dialog>
            </Box> 
    );
};

export default AdminRequests;