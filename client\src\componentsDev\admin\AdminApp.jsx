import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AdminAuthProvider } from './auth/AdminAuthContext';
import AdminLogin from './auth/AdminLogin';
import AdminMaster from './layout/AdminMaster';

const AdminApp = () => {
  return (
    <AdminAuthProvider>
      <Routes>
        {/* Admin Login Route */}
        <Route path="login" element={<AdminLogin />} />
        
        {/* Admin Dashboard Routes - All under /admin/* */}
        <Route path="/*" element={<AdminMaster />} />
        
        {/* Default redirect */}
        <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </AdminAuthProvider>
  );
};

export default AdminApp;
