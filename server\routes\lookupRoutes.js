const express = require('express');
const router = express.Router();
const lookupController = require('../controllers/lookupController');

// Create
router.post('/genre', lookupController.create('genre'));
router.post('/url', lookupController.create('url'));
router.post('/file', lookupController.create('file'));
router.post('/playlist', lookupController.create('playlist'));
router.post('/difficulty', lookupController.create('difficulty'));
router.post('/question-type', lookupController.create('question-type'));
router.post('/answer-type', lookupController.create('answer-type'));

// Read All
router.get('/genre', lookupController.getAll('genre'));
router.get('/url', lookupController.getAll('url'));
router.get('/file', lookupController.getAll('file'));
router.get('/playlist', lookupController.getAll('playlist'));
router.get('/difficulty', lookupController.getAll('difficulty'));
router.get('/question-type', lookupController.getAll('question-type'));
router.get('/answer-type', lookupController.getAll('answer-type'));

// Read by ID
router.get('/genre/:id', lookupController.getById('genre'));
router.get('/url/:id', lookupController.getById('url'));
router.get('/file/:id', lookupController.getById('file'));
router.get('/playlist/:id', lookupController.getById('playlist'));
router.get('/difficulty/:id', lookupController.getById('difficulty'));
router.get('/question-type/:id', lookupController.getById('question-type'));
router.get('/answer-type/:id', lookupController.getById('answer-type'));

// Update
router.put('/genre/:id', lookupController.update('genre'));
router.put('/url/:id', lookupController.update('url'));
router.put('/file/:id', lookupController.update('file'));
router.put('/playlist/:id', lookupController.update('playlist'));
router.put('/difficulty/:id', lookupController.update('difficulty'));
router.put('/question-type/:id', lookupController.update('question-type'));
router.put('/answer-type/:id', lookupController.update('answer-type'));

// Delete
router.delete('/genre/:id', lookupController.remove('genre'));
router.delete('/url/:id', lookupController.remove('url'));
router.delete('/file/:id', lookupController.remove('file'));
router.delete('/playlist/:id', lookupController.remove('playlist'));
router.delete('/difficulty/:id', lookupController.remove('difficulty'));
router.delete('/question-type/:id', lookupController.remove('question-type'));
router.delete('/answer-type/:id', lookupController.remove('answer-type'));

module.exports = router;