import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../auth/AdminAuthContext';
import { useAdminLogout } from '../../hooks/useAdminAuth';
// import LogoutIcon from "@mui/icons-material/Logout";

const AdminHeader = () => {
  const navigate = useNavigate();
  const { adminUser, logout } = useAdminAuth();
  const logoutMutation = useAdminLogout();

  const handleLogout = async () => {
    try {
      await logoutMutation.mutateAsync();
      logout();
      navigate('/admin/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleLogoClick = () => {
    navigate('/admin/dashboard');
  };

  return (
    <header style={{
      backgroundColor: '#1DB850',
      color: 'white',
      padding: '1rem 2rem',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      <div 
        onClick={handleLogoClick}
        style={{ 
          cursor: 'pointer',
          fontSize: '1.5rem',
          fontWeight: 'bold'
        }}
      >
        Admin Dashboard
      </div>
      
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        {adminUser && (
          <span style={{ fontSize: '0.9rem' }}>
            Welcome, {adminUser.username || 'Admin'}
          </span>
        )}
        
        <button
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
          style={{
            backgroundColor: 'transparent',
            color: 'white',
            border: '1px solid white',
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '0.9rem',
            opacity: logoutMutation.isPending ? 0.7 : 1
          }}
        >
          {/* <LogoutIcon /> */}
          {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
        </button>
      </div>
    </header>
  );
};

export default AdminHeader;
