import { useMutation, useQueryClient } from '@tanstack/react-query';

const API_BASE = process.env.REACT_APP_API_URL;

// Admin login (uses cookie-based session)
export const useAdminLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (credentials) => {
      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(credentials),
      });
      
      const data = await response.json().catch(() => ({}));
      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }
      
      return data; // { message: 'Login successful' }
    },
    onSuccess: () => {
      // Cookie is set by server; use localStorage flag for client-side guard
      localStorage.setItem('isAuthenticated', 'true');
      // Clear/refresh queries if needed
      queryClient.clear();
    },
  });
};

// Admin logout
export const useAdminLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      await fetch(`${API_BASE}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
      });
      // No need to parse body
      return { success: true };
    },
    onSettled: () => {
      localStorage.removeItem('isAuthenticated');
      queryClient.clear();
    }
  });
};

// Check admin authentication status (client-side flag)
export const checkAdminAuth = () => {
  return localStorage.getItem('isAuthenticated') === 'true';
};

// Backwards-compatible helpers
export const getAdminToken = () => null;
export const getAdminUser = () => null;
