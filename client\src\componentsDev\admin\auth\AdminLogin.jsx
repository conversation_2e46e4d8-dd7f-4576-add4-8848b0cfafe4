import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAdminLogin } from '../../hooks/useAdminAuth';
import { useAdminAuth } from './AdminAuthContext';
import { Avatar, Button, TextField, Paper, Box, Typography, Container } from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';

const AdminLogin = () => {
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [error, setError] = useState('');

  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAdminAuth();

  const adminLoginMutation = useAdminLogin();

  const from = location.state?.from?.pathname || '/admin/dashboard';

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    try {
      await adminLoginMutation.mutateAsync(credentials);
      login();
      navigate(from, { replace: true });
    } catch (err) {
      setError(err.message || 'Login failed');
    }
  };

  const handleChange = (e) => {
    setCredentials({ ...credentials, [e.target.name]: e.target.value });
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper elevation={6} sx={{ mt: 8, p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Avatar sx={{ m: 1, bgcolor: '#966300' }}>
          <LockOutlinedIcon />
        </Avatar>
        <Typography component="h1" variant="h5">
          Admin Login
        </Typography>
        {error && (
          <Typography color="error" sx={{ mt: 1 }}>
            {error}
          </Typography>
        )}
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, width: '100%' }}>
          <TextField
            fullWidth
            id="username"
            name="username"
            label="Username"
            value={credentials.username}
            onChange={handleChange}
            variant="outlined"
            required
          />
          <TextField
            margin="normal"
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            value={credentials.password}
            onChange={handleChange}
            required
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, mb: 2, backgroundColor: '#966300', color: '#fff' }}
            disabled={adminLoginMutation.isPending}
          >
            {adminLoginMutation.isPending ? 'Logging in...' : 'Log In'}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default AdminLogin;
