import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  Button,
  Card,
  CardContent,
  Chip,
  Divider,
  LinearProgress,
  Radio,
  Checkbox,
  Stack,
  Typography,
} from "@mui/material";

const API = process.env.REACT_APP_API_URL || "http://localhost:3001";

const QuizPlay = () => {
  const [questions, setQuestions] = useState([]);
  const [started, setStarted] = useState(false);
  const [index, setIndex] = useState(0);
  const [selected, setSelected] = useState({}); // question_id => Set of answer_id
  const [complete, setComplete] = useState(false);
  const [score, setScore] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const load = async () => {
      setLoading(true);
      try {
        const list = await fetch(`${API}/api/admin/questions`).then(r => r.json());
        // fetch details for each to get answers and answer_type_name
        const detailed = await Promise.all(list.map(async (q) => {
          const full = await fetch(`${API}/api/admin/questions/${q.question_id}`).then(r => r.json());
          return { ...q, ...full };
        }));
        setQuestions(detailed);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  const current = questions[index];

  const toggleSelect = (answerId, multiple) => {
    setSelected(prev => {
      const prevSet = new Set(prev[current.question_id] || []);
      if (multiple) {
        if (prevSet.has(answerId)) prevSet.delete(answerId); else prevSet.add(answerId);
        return { ...prev, [current.question_id]: Array.from(prevSet) };
      } else {
        return { ...prev, [current.question_id]: [answerId] };
      }
    });
  };

  const next = () => {
    if (index + 1 >= questions.length) {
      submit();
    } else {
      setIndex(index + 1);
    }
  };

  const submit = () => {
    let s = 0;
    for (const q of questions) {
      const picked = new Set(selected[q.question_id] || []);
      const correctIds = new Set(q.answers.filter(a => a.is_correct).map(a => a.answer_id));
      if (picked.size === correctIds.size) {
        let allMatch = true;
        for (const id of picked) if (!correctIds.has(id)) { allMatch = false; break; }
        if (allMatch) s += 1;
      }
    }
    setScore(s);
    setComplete(true);
  };

  const renderAnswers = () => {
    if (!current) return null;
    const multiple = current.answer_type_name === 'multiple_choice';
    const selectedIds = new Set(selected[current.question_id] || []);
    return (
      <Stack spacing={1}>
        {current.answers.map(a => (
          <Stack key={a.answer_id} direction="row" alignItems="center" spacing={1}>
            {multiple ? (
              <Checkbox checked={selectedIds.has(a.answer_id)} onChange={() => toggleSelect(a.answer_id, true)} />
            ) : (
              <Radio checked={selectedIds.has(a.answer_id)} onChange={() => toggleSelect(a.answer_id, false)} />
            )}
            <Typography>{a.answer_text}</Typography>
          </Stack>
        ))}
      </Stack>
    );
  };

  if (loading) return <div>Loading…</div>;

  if (!started) {
    return (
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Music Quiz</Typography>
        <Card>
          <CardContent>
            <Typography sx={{ mb: 1 }}>Total Questions: {questions.length}</Typography>
            <Button variant="contained" sx={{ bgcolor: '#1DB850' }} onClick={() => setStarted(true)}>Start</Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (complete) {
    return (
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Your Score</Typography>
        <Card>
          <CardContent>
            <Typography variant="h4">{score} / {questions.length}</Typography>
            <Button sx={{ mt: 2 }} onClick={() => { setStarted(false); setIndex(0); setSelected({}); setComplete(false); setScore(0); }}>Restart</Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 1 }}>
        <Typography variant="h6">Question {index + 1} / {questions.length}</Typography>
        <Box sx={{ minWidth: 200 }}>
          <LinearProgress variant="determinate" value={(index / questions.length) * 100} />
        </Box>
      </Stack>

      <Card>
        <CardContent>
          {current?.song_name && (
            <Chip label={current.song_name} color="success" sx={{ mb: 1 }} />
          )}
          <Typography sx={{ mb: 2 }}>{current?.question_text}</Typography>
          {renderAnswers()}
          <Divider sx={{ my: 2 }} />
          <Stack direction="row" justifyContent="flex-end" spacing={1}>
            <Button variant="contained" onClick={next} sx={{ bgcolor: '#1DB850' }}>{index + 1 === questions.length ? 'Finish' : 'Next'}</Button>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default QuizPlay;


