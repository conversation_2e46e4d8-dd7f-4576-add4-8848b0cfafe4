const express = require('express');
const {
  createQuestion,
  listQuestions,
  getQuestion,
  updateQuestion,
  deleteQuestion,
  getQuestionStats,
  duplicateQuestion,
  bulkDeleteQuestions,
  getQuizSettings,
  saveQuizSettings
} = require('../controllers/questionsController');

const router = express.Router();

// Basic CRUD operations
router.get('/', listQuestions);
router.get('/stats', getQuestionStats);
router.get('/:id', getQuestion);
router.post('/', createQuestion);
router.put('/:id', updateQuestion);
router.delete('/:id', deleteQuestion);

// Advanced operations
router.post('/:id/duplicate', duplicateQuestion);
router.post('/bulk-delete', bulkDeleteQuestions);

// Settings routes
router.get('/settings', getQuizSettings);
router.post('/settings', saveQuizSettings);

module.exports = router;
