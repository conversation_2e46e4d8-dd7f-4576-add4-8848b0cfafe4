import React from 'react'
import HeroMaster from './hero/HeroMaster'
import { useMode } from "../../../utils/useMode";
import { useSongs } from '../../hooks/useSongs';
import PlaylistMaster from './playlists/PlaylistMaster'; 
import PopularSongMaster from './popularSongs/PopularSongMaster';
import ArtistMaster from './artists/ArtistsMaster';
import SongsMaster from './songs/SongsMaster';

const HomeMaster = () => {
    const mode = useMode();
 
    const {
        data: songs = [], 
    } = useSongs();

    return (
        <>
            <HeroMaster mode={mode} songsLength={songs ? songs.length : null} />
            <PlaylistMaster />
            <PopularSongMaster />
            <ArtistMaster />
            <SongsMaster />
        </>
    )
}

export default HomeMaster