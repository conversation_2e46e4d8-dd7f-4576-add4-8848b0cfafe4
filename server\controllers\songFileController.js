const SongFileModel = require("../models/songFileModel");

const SongFileController = {
  create: async (req, res) => {
    try {
      const id = await SongFileModel.create(req.body);
      res.status(201).json(id);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getAll: async (req, res) => {
    try {
      const files = await SongFileModel.getAll();
      res.json(files);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getById: async (req, res) => {
    try {
      const file = await SongFileModel.getById(req.params.id);
      if (!file) return res.status(404).json({ error: "Not found" });
      res.json(file);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  update: async (req, res) => {
    try {
      const updated = await SongFileModel.update(req.params.id, req.body);
      res.json(updated);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  delete: async (req, res) => {
    try {
      const deleted = await SongFileModel.delete(req.params.id);
      res.json(deleted);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getBySongId: async (req, res) => {
    try {
      const files = await SongFileModel.getBySongId(req.params.songId);
      res.json(files);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },
};

module.exports = SongFileController;
