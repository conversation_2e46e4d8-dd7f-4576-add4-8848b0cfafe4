const db = require('../database/db');

exports.listDifficulties = (req, res) => {
  db.all(
    `SELECT difficulty_id, level_name, bonus_points, time_multiplier, status
     FROM difficulty_level_lookup
     WHERE status = 1
     ORDER BY difficulty_id ASC`,
    [],
    (err, rows) => {
      if (err) return res.status(500).json({ error: 'DB error', details: err.message });
      res.json(rows);
    }
  );
};

exports.listQuestionTypes = (req, res) => {
  db.all(
    `SELECT question_type_id, type_name, display_name
     FROM question_type_lookup
     WHERE status = 1
     ORDER BY question_type_id ASC`,
    [],
    (err, rows) => {
      if (err) return res.status(500).json({ error: 'DB error', details: err.message });
      res.json(rows);
    }
  );
};

exports.listAnswerTypes = (req, res) => {
  db.all(
    `SELECT answer_type_id, type_name, display_name
     FROM answer_type_lookup
     WHERE status = 1
     ORDER BY answer_type_id ASC`,
    [],
    (err, rows) => {
      if (err) return res.status(500).json({ error: 'DB error', details: err.message });
      res.json(rows);
    }
  );
};
