import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  IconButton,
  DialogTitle,
  Divider,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { SOCIAL_MEDIA_LINKS } from "../../../lib/constants";

const ThankYouDialog = ({ open, onClose }) => {
  const [showPayment, setShowPayment] = useState(false);

  const handleShowPayment = () => {
    setShowPayment(true);
  };

  const handleBackToContent = () => {
    setShowPayment(false);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      aria-labelledby="song-request-dialog-title"
      PaperProps={{
        sx: {
          border: "2px solid",
          borderRadius: 3,
          backgroundColor: "#000",
          maxHeight: "90vh",
        },
      }}
      slotProps={{
        backdrop: {
          sx: {
            backdropFilter: "blur(8px)",
            backgroundColor: "rgba(0, 0, 0, 0.4)",
          },
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          backgroundColor: "#000",
          position: "sticky",
          top: 0,
          zIndex: 1,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: "Nunito",
            fontSize: "32px",
            fontWeight: 700,
            lineHeight: "40px",
            color: "#fff",
          }}
        >
          {showPayment ? "Make a Donation" : "Thanks for your request."}
        </Typography>
        <IconButton
          aria-label="close"
          onClick={showPayment ? handleBackToContent : onClose}
          sx={{
            position: "absolute",
            top: "16px",
            right: "16px",
            color: "#fff",
          }}
        >
          {showPayment ? <CloseIcon /> : <CloseIcon />}
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          backgroundColor: "#000",
          color: "#fff",
          padding: 3,
          display: "flex",
          flexDirection: "column",
          overflowY: "auto",
          "&::-webkit-scrollbar": { display: "none" },
        }}
      >
        {showPayment ? (
          // Payment iframe view
          <Box
            sx={{
              width: "100%",
              height: "600px",
              border: "1px solid #333",
              borderRadius: 2,
              overflow: "hidden",
            }}
          >
            <iframe
              src="https://checkout.square.site/merchant/MLQ64YC4TG7EN/checkout/P2KDZXB7EJ32RK37T6WJXPLG"
              title="Donation"
              width="100%"
              height="100%"
              style={{ border: "none" }}
              allow="payment"
            />
          </Box>
        ) : (
          // Original content view
          <>
            <Typography sx={{ fontSize: 16, mb: 2 }}>
              <p>
                We will do our best to play your songs for you. Feel free to make
                additional requests and enjoy the music. Love & Regards, Russel
                Diniz
              </p>
              <p>
                If you would like to make a donation to support our music, click on
                the button below.
              </p>
            </Typography>

            <Typography sx={{ fontSize: 18, fontWeight: 600, mt: 4, mb: 2 }}>
              Send a Gift 💝
            </Typography>

            <Grid container spacing={2} sx={{ mb: 4 }}>
              {[
                { name: "Rose", price: 1, emoji: "🌹" },
                { name: "Coffee", price: 3, emoji: "☕" },
                { name: "Pint", price: 5, emoji: "🍺" },
                { name: "Magic Mic", price: 10, emoji: "🎤" },
                { name: "Golden Buzzer", price: 20, emoji: "🔔" },
                { name: "Galaxy", price: 50, emoji: "🌌" },
                { name: "Lion", price: 100, emoji: "🦁" },
              ].map((gift, index) => (
                <Grid item xs={6} sm={4} md={3} key={index}>
                  <Box
                    sx={{
                      border: "1px solid #666",
                      borderRadius: 2,
                      p: 2,
                      textAlign: "center",
                      backgroundColor: "#111",
                      color: "#fff",
                      transition: "0.3s",
                      "&:hover": {
                        backgroundColor: "#1e1e1e",
                        transform: "scale(1.03)",
                      },
                    }}
                    onClick={handleShowPayment}
                  >
                    <Typography fontSize="2rem">{gift.emoji}</Typography>
                    <Typography fontWeight="bold" fontSize="14px" sx={{ mt: 1 }}>
                      {gift.name}
                    </Typography>
                    <Typography fontSize="14px">£{gift.price}</Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleShowPayment}
                      sx={{
                        mt: 1,
                        borderColor: "#976609",
                        color: "#fff",
                        fontSize: "12px",
                        textTransform: "uppercase",
                        borderRadius: "16px",
                        "&:hover": {
                          backgroundColor: "#2b2b2b",
                        },
                      }}
                    >
                      Send Gift
                    </Button>
                  </Box>
                </Grid>
              ))}
            </Grid>

            <Box
              onClick={handleShowPayment}
              sx={{
                cursor: "pointer",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                mt: -2,
                mb: 2,
              }}
            >
              <IconButton color="inherit">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="36"
                  height="36"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  >
                    <path d="M17 4v1.882c0 .685.387 1.312 1 1.618s1 .933 1 1.618V18a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9.118c0-.685.387-1.312 1-1.618s1-.933 1-1.618V4M6 4h12z" />
                    <path d="M14 10h-1a2 2 0 0 0-2 2v2c0 1.105-.395 2-1.5 2H14m-4-3h3" />
                  </g>
                </svg>
                <Typography sx={{ ml: 1, color: "#EEEEEE" }}>Tip Jar</Typography>
              </IconButton>
            </Box>

            <Button
              variant="outlined"
              onClick={handleShowPayment}
              sx={{
                backgroundColor: "inherit",
                color: "#ffff",
                textTransform: "uppercase",
                borderRadius: 3,
                borderColor: "#976609",
                width: "100%",
                mb: 3,
                py: 1.5,
                fontSize: "1rem",
              }}
            >
              Make a donation
            </Button>

            <Typography sx={{ my: 2 }}>Or, scan QR code below:</Typography>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/assets/QR.png" alt="QR Code" width="200" />
            </Box>

            <Typography sx={{ my: 2 }}>We accept payments by:</Typography>
            <img
              src="/assets/Payment.png"
              alt="Payment options"
              style={{ width: "100%", maxWidth: "400px", margin: "0 auto" }}
            />

            <Divider sx={{ my: 3, borderColor: "#222" }} />

            <Typography sx={{ mb: 2 }}>Our Social Media Channels</Typography>
            <Box display="flex" justifyContent="center" gap={2}>
              {Object.entries(SOCIAL_MEDIA_LINKS).map(([key, link]) => (
                <a
                  key={key}
                  href={link}
                  target="_blank"
                  rel="noreferrer"
                  style={{ display: "flex" }}
                >
                  <IconButton
                    sx={{
                      width: 40,
                      height: 40,
                      border: "2px solid #fff",
                      backgroundColor: "#fff",
                      "&:hover": {
                        backgroundColor: "#f0f0f0",
                      },
                    }}
                  >
                    <img
                      src={`/assets/${key.toLowerCase()}.svg`}
                      alt={key}
                      width={24}
                      height={24}
                    />
                  </IconButton>
                </a>
              ))}
            </Box>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ThankYouDialog;