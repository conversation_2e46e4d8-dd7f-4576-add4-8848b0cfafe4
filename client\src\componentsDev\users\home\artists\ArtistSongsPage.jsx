// pages/ArtistSongsPage.jsx
import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  IconButton,
  Typography,
  Avatar,
  CircularProgress,
  Alert,
  Container,
} from '@mui/material';
import ArrowCircleUpOutlinedIcon from '@mui/icons-material/ArrowCircleUpOutlined';
import SongCard from '../../common/SongCard';
// import RequestHandler from '../../common/RequestHandler';
import NavigationBar from '../../navigation/Navbar';
import TabNavigation from '../../navigation/TabNavigation';
import { useSongCart } from '../../../../context/SongCartContext';
import { useArtistDetails, useArtistSongs } from '../../../hooks/useArtists';
import { useActiveRequests } from '../../../hooks/useActiveRequests';
import { useSnackbar } from 'notistack';
import DedicationMaster from '../../modals/DedicationModal/DedicationMaster';
import SuccessModal from '../../modals/SuccessModal';
import ThankYouDialog from '../../modals/ThankDialog';
import { getDeviceId } from '../../../../utils/cookieUtils';
import { useQueryClient } from '@tanstack/react-query';

const ArtistSongsPage = () => {
  const navigate = useNavigate();
  const { artistID } = useParams();
  const { cart, clearCart } = useSongCart();
  const [selectedSong, setSelectedSong] = useState(null);
  const [showDedicationModal, setShowDedicationModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showThankYouDialog, setShowThankYouDialog] = useState(false);
  const [requesterName, setRequesterName] = useState("");
  const { enqueueSnackbar } = useSnackbar();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('artists');

  // Using your existing hooks
  const {
    data: songs = [],
    isLoading: songsLoading,
    error: songsError,
  } = useArtistSongs(artistID);

  const {
    data: artist = {},
    isLoading: artistLoading,
    error: artistError,
  } = useArtistDetails(artistID);

  const {
    data: activeRequests = {},
  } = useActiveRequests();


  const handleRequestSuccess = () => {
    // Invalidate and refetch active requests
    queryClient.invalidateQueries({ queryKey: ['activeRequests'] });
  };

  const handleRequestOpen = (song) => {
    setSelectedSong(song);
    setShowDedicationModal(true);
  };

  const handleSubmitRequest = async (dedicationData) => {
    try {
      const deviceId = getDeviceId();
      const { userName, songs } = dedicationData;

      // Validate the data structure
      if (!songs || !Array.isArray(songs) || songs.length === 0) {
        console.error("Invalid songs data received:", dedicationData);
        enqueueSnackbar("Invalid request data. Please try again.", {
          variant: "error",
        });
        return;
      }

      const finalUserName = userName || "Anonymous";
      setRequesterName(finalUserName);

      // Check for existing active requests for all songs
      for (const song of songs) {
        const canRequestResponse = await fetch(
          `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${song.songs_master_id}`
        );

        const canRequestData = await canRequestResponse.json();

        if (canRequestData.hasActiveRequest) {
          enqueueSnackbar(`You already have an active request for song ID ${song.songs_master_id}`, {
            variant: "warning",
          });
          return;
        }
      }

      // Submit the request for all songs
      const requestBody = {
        requester_name: finalUserName,
        device_id: deviceId,
        songs: songs.map(song => ({
          songs_master_id: song.songs_master_id,
          dedication_msg: song.dedication || "",
          action_type: song.actionType,
        })),
      };

      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit request");
      }

      await response.json();
      setShowDedicationModal(false);
      setShowSuccessModal(true);
      handleRequestSuccess();

    } catch (error) {
      console.error("Failed to make request:", error);
      enqueueSnackbar("Failed to submit request. Please try again.", {
        variant: "error",
      });
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    setShowThankYouDialog(true);
  };

  const handleThankYouClose = () => {
    setShowThankYouDialog(false);
    clearCart();
  };



  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    navigate(`/?activeTab=${tab}`);
  };

  // Loading states
  if (songsLoading || artistLoading) {
    return (
      <Box sx={{ bgcolor: 'background.default', color: 'text.primary', pb: 4 }}>
        <NavigationBar />
        <Container sx={{ mt: 12, textAlign: 'center' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading artist and songs...
          </Typography>
        </Container>
      </Box>
    );
  }

  // Error states
  if (songsError || artistError) {
    return (
      <Box sx={{ bgcolor: 'background.default', color: 'text.primary', pb: 4 }}>
        <NavigationBar />
        <Container sx={{ mt: 12 }}>
          <Alert severity="error">
            Failed to load artist information. Please try again later.
          </Alert>
        </Container>
      </Box>
    );
  }

  // No songs state
  if (songs.length === 0) {
    return (
      <Box sx={{ bgcolor: 'background.default', color: 'text.primary', pb: 4 }}>
        <NavigationBar />
        {artist.name && (
          <Box sx={{ textAlign: 'center', py: 4, mt: 8 }}>
            <Avatar
              alt={artist.name}
              src={artist.image}
              sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
            />
            <Typography variant="h4" fontWeight="bold">
              Songs by {artist.name}
            </Typography>
          </Box>
        )}
        <Container sx={{ textAlign: 'center', mt: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No songs available for this artist.
          </Typography>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ bgcolor: 'background.default', color: 'text.primary', pb: 4 }}>
      <NavigationBar />

      {artist.name && (
        <Box sx={{ textAlign: 'center', py: 4, mt: 16 }}>
          <Avatar
            alt={artist.name}
            src={artist.image}
            sx={{
              width: 120,
              height: 120,
              mx: 'auto',
              mb: 2,
              border: '3px solid #966300',
            }}
          />
          <Typography variant="h4" fontWeight="bold">
            Songs by {artist.name}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            {songs.length} {songs.length === 1 ? 'song' : 'songs'} available
          </Typography>
        </Box>
      )}

      <Container maxWidth="xl" sx={{ px: 2 }}>
        <Grid container spacing={3}>
          {songs.map((song) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={song.songs_master_id}>
              <SongCard
                song={song}
                activeRequests={activeRequests}
                onRequestOpen={handleRequestOpen}
                cart={cart}
              />
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Use your existing modal components directly */}
      <DedicationMaster
        open={showDedicationModal}
        onClose={() => setShowDedicationModal(false)}
        song={selectedSong}
        onSubmit={handleSubmitRequest}
      />

      <SuccessModal
        open={showSuccessModal}
        onClose={handleSuccessModalClose}
        requesterName={requesterName}
      />

      <ThankYouDialog
        open={showThankYouDialog}
        onClose={handleThankYouClose}
      />


      <IconButton
        onClick={scrollToTop}
        sx={{
          position: 'fixed',
          bottom: '80px',
          right: '20px',
          zIndex: 9999,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid #966300',
          color: '#966300',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: '#966300',
            color: '#fff',
            transform: 'scale(1.1)',
          },
        }}
      >
        <ArrowCircleUpOutlinedIcon />
      </IconButton>

      <TabNavigation activeTab={activeTab} handleTabClick={handleTabClick} />
    </Box>
  );
};

export default ArtistSongsPage;