import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Box, Grid, IconButton } from "@mui/material";
import PlaylistHeader from "./PlaylistHeader";
import SongCard from "./SongCard";
import RequestHand<PERSON> from "./RequestHandler";
import ArrowCircleUpOutlinedIcon from "@mui/icons-material/ArrowCircleUpOutlined";
import NavigationBar from "./NavigationBar";
import TabNavigation from "./TabNavigation";
import { getDeviceId } from "../../utils/cookieUtils";
import { useSongCart } from "../../context/SongCartContext";

const PlaylistPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { cart } = useSongCart();
  const [playlistDetails, setPlaylistDetails] = useState(null);
  const [songs, setSongs] = useState([]);
  const [selectedSong, setSelectedSong] = useState({});
  const [openRequestModal, setOpenRequestModal] = useState(false);
  const [activeTab, setActiveTab] = useState("playlists");
  const [activeRequests, setActiveRequests] = useState({});
  const [step, setStep] = useState(1);
  const deviceId = getDeviceId();

  const fetchActiveRequests = useCallback( async () => {
    try {
      const activeReqResponse = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`
      );
      const activeReqData = await activeReqResponse.json();
      setActiveRequests(activeReqData);
    } catch (error) {
      console.error("Error fetching active requests:", error);
    }
  }, [deviceId]);

  useEffect(() => {
    const fetchPlaylistDetails = async () => {
      try {
        const playlistResponse = await fetch(
          `${process.env.REACT_APP_API_URL}/api/playlist/${id}`
        );
        const playlistData = await playlistResponse.json();
        setPlaylistDetails(playlistData);

        const songsResponse = await fetch(
          `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistData.playlist_master_id}/songs`
        );
        const songsData = await songsResponse.json();
        setSongs(songsData);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (id) {
      fetchPlaylistDetails();
      fetchActiveRequests();
    }
  }, [id, deviceId, fetchActiveRequests]);

  const handleRequestOpen = (song) => {
    if (cart.length >= 3) {
      setStep(3);
    } else {
      setSelectedSong(song);
      setStep(1);
    }
    setOpenRequestModal(true);
  };

  const handleRequestSuccess = () => {
    fetchActiveRequests();
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    navigate(`/?activeTab=${tab}`);
  };

  return (
    <Box sx={{ bgcolor: "background.default", color: "text.primary", pb: 4 }}>
      <NavigationBar />
      {playlistDetails && <PlaylistHeader details={playlistDetails} />}
      <Box sx={{ padding: "16px", marginBottom: "4rem !important" }}>
        <Grid container spacing={3}>
          {songs.map((song) => (
            <Grid item xs={12} sm={6} md={4} key={song.songs_master_id}>
              <SongCard
                song={song}
                activeRequests={activeRequests}
                onRequestOpen={handleRequestOpen}
                cart={cart}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      <RequestHandler
        selectedSong={selectedSong}
        setSelectedSong={setSelectedSong}
        openRequestModal={openRequestModal}
        setOpenRequestModal={setOpenRequestModal}
        onRequestSuccess={handleRequestSuccess}
        step={step}
        setStep={setStep}
      />

      <IconButton
        onClick={scrollToTop}
        sx={{
          position: "fixed",
          bottom: "20px",
          right: "20px",
          zIndex: 9999,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
        }}
      >
        <ArrowCircleUpOutlinedIcon />
      </IconButton>
      <TabNavigation activeTab={activeTab} handleTabClick={handleTabClick} />
    </Box>
  );
};

export default PlaylistPage;
