import { createTheme, ThemeProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import { SnackbarProvider } from "notistack";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { RequestModeProvider } from "./utils/useMode";
import { SongCartProvider } from "./context/SongCartContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// import PlaylistPage from "./Components/UserSide/PlaylistPage";
// import PlaylistPage from "./componentsDev/users/home/<USER>/PlaylistPage";
// Legacy Admin UI imports (restored)
// import AdminHomePage from "./Components/AdminSide/AdminHomePage";
// import AdminPlaylist from "./Components/AdminSide/AdminPlaylist";
// import AdminRequests from "./Components/AdminSide/AdminRequests"; 
// import AllPlaylistScreen from "./Components/AdminSide/AllPlaylistScreen"; 
// import Login from "./Components/AdminSide/Login"; 
// import TrackListPage from "./Components/AdminSide/TrackListPage"; 
// import AllSongsScreen from "./Components/AdminSide/AllSongScreen";
// import HomePage2 from "./Components/UserSide/HomePage2";
// import ProtectedRoute from "./Components/ProtectedRoute"; 
// import LookupTabs from "./Components/AdminSide/LookUpTabs";
// import LoginPage from "./Components/AdminSide/LoginPage";
// import ArtistSongsPage from "./Components/UserSide/ArtistSongsPage";
// import ImportedSongs from "./Components/AdminSide/ImportedSongs/ImportedSongs";
// import AdminRequestCompact from "./Components/AdminSide/AdminRequestCompact";
// import SongqRequests from "./Components/AdminSide/SongqRequests";
// import ShowdownForm from "./Components/UserSide/ShowdownForm";
// import PlaylistMaster from "./Components/AdminSide/Playlist/PlaylistMaster";
// import QuizMaster from "./Components/AdminSide/Quiz/QuizMaster";

// User
import Home from "./componentsDev/users/pages/Home";
import PlaylistPage from "./componentsDev/users/home/<USER>/PlaylistPage";
import ArtistSongsPage from "./componentsDev/users/home/<USER>/ArtistSongsPage";

// User Quiz
import QuizPlay from "./componentsDev/users/pages/QuizPlay";

// User Showdown
import ShowdownForm from "./componentsDev/users/pages/ShowdownForm";

// Admin
import AdminApp from "./componentsDev/admin/AdminApp";
import Unauthorized from "./componentsDev/admin/auth/Unauthorized";


const darkTheme = createTheme({
  palette: {
    mode: "dark",
    background: {
      default: "#121210",
      paper: "#1A1A1A",
    },
    text: {
      primary: "#fff",
      secondary: "rgba(255,255,255,0.7)",
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: "#1A1A1A",
        },
      },
    },
  },
});

const queryClient = new QueryClient();

const App = () => {
  return (
    <SnackbarProvider>
      <SongCartProvider>
        <ThemeProvider theme={darkTheme}>
          <QueryClientProvider client={queryClient}>
            <CssBaseline />
            <Router>
              <RequestModeProvider>
                <Routes>
                  {/* <Route path="/" element={<HomePage2 />} /> */}
                  {/* Public Routes */}
                  <Route path="/" element={<Home />} />
                  <Route path="/playlist/:id" element={<PlaylistPage />} />
                  <Route path="/artist/:artistID" element={<ArtistSongsPage />} />

                  <Route path="/showdown" element={<ShowdownForm />} />
                  <Route path="/quiz" element={<QuizPlay />} />

                  {/* New Admin  */}
                  <Route path="/admin/*" element={<AdminApp />} />
                  <Route path="/unauthorized" element={<Unauthorized />} />

                  {/* <Route path="/AdminLogin" element={<LoginPage />} /> */}
                  {/* Admin Panel under /admin/* */}
                  {/* Protected Admin Routes (legacy UI) */}
                  {/* <Route element={<ProtectedRoute />}>
                    <Route path="/AdminHomePage" element={<AdminHomePage />} />
                    <Route path="/AdminRequestCompact" element={<AdminRequestCompact />} />
                    <Route path="/AdminPlaylist" element={<AdminPlaylist />} />
                    <Route path="/login" element={<Login />} />

                    <Route path="/Playlist" element={<PlaylistMaster />} />
                    <Route path="/AdminRequests" element={<AdminRequests />} />
                    <Route path="/SongqRequests" element={<SongqRequests />} />

                    <Route path="/AllPlaylistScreen" element={<AllPlaylistScreen />} />
                    <Route path="/AllSongScreen" element={<AllSongsScreen />} />
                    <Route path="/LookUpTabs" element={<LookupTabs />} />
                    <Route path="/playlists/:playlistId" element={<TrackListPage />} />
                    <Route path="/ImportedSongs" element={<ImportedSongs />} />
                    <Route path="/AdminQuiz" element={<QuizMaster />} />
                  </Route> */}

                </Routes>
              </RequestModeProvider>
            </Router>
          </QueryClientProvider>
        </ThemeProvider>
      </SongCartProvider>
    </SnackbarProvider>
  );
};

export default App;
