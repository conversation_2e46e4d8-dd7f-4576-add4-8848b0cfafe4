const Song = require("../models/songModel");

exports.create = async (req, res) => {
  try {
    const result = await Song.create(req.body);
    if (result.songs_master_id) {
      // New song created
      res.status(201).json(result);
    } else {
      // Song already exists
      res.status(409).json({
        message: "This song already exists in your database.",
        song: result,
      });
    }
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getAll = async (req, res) => {
  try {
    const data = await Song.getAll();
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getById = async (req, res) => {
  try {
    const data = await Song.getById(req.params.id);
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const result = await Song.update(req.params.id, req.body);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const result = await Song.delete(req.params.id);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.searchSongs = async (req, res) => {
  try {
    const { name, artist, genre } = req.query;
    const results = await SongModel.search({ name, artist, genre });
    res.json(results);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getAllArtists = async (req, res) => {
  try {
    const artists = await Song.getAllArtists();
    res.json(artists);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.saveArtist = async (req, res) => {
  try {
    const { id, name, image } = req.body;

    if (!id || !name) {
      return res
        .status(400)
        .json({ error: "Artist ID and name are required." });
    }

    await Song.saveArtist({ id, name, image });
    res.status(201).json({ message: "Artist saved successfully." });
  } catch (err) {
    console.error("Error saving artist:", err);
    res.status(500).json({ error: "Failed to save artist." });
  }
};

exports.getByArtistId = async (req, res) => {
  try {
    const artistId = req.params.artistID; // now this is the real Spotify artist ID
    const data = await Song.getSongsByArtistId(artistId);
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getPopularSongs = async (req, res) => {
  try {
    // Assuming you have a way to determine popular songs, e.g., by play count
    const popularSongs = await Song.getPopularSongs(); // Implement this method in SongModel
    res.json(popularSongs);
  } catch (error) {
    res.status(500).json({ message: "Error retrieving popular songs" });
  }
};

exports.getArtistById = async (req, res) => {
  try {
    const artistId = req.params.artistID;
    const artist = await Song.getArtistById(artistId);
    if (!artist) {
      return res.status(404).json({ message: "Artist not found" });
    }
    res.json(artist);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
