#!/usr/bin/env node

const path = require('path');
const fs = require('fs');
const { DatabaseManager } = require('../database');

class SchemaCLI {
  constructor() {
    this.dbPath = path.join(__dirname, '../../app.db');
    this.schemaPath = path.join(__dirname, '../database/schema.sql');
    this.dbManager = null;
  }

  async initialize() {
    this.dbManager = new DatabaseManager({
      dbPath: this.dbPath,
      schemaPath: this.schemaPath,
      enableWatcher: false // Disable watcher for CLI operations
    });
    await this.dbManager.initialize();
  }

  async status() {
    console.log('📊 Schema Status Report');
    console.log('=' .repeat(50));
    
    try {
      const status = await this.dbManager.getSchemaStatus();
      
      console.log(`Database Path: ${status.dbPath}`);
      console.log(`Schema Path: ${status.schemaPath}`);
      console.log(`Current Hash: ${status.currentHash || 'none'}`);
      console.log(`File Hash: ${status.fileHash}`);
      console.log(`In Sync: ${status.inSync ? '✅ Yes' : '❌ No'}`);
      console.log(`Last Modified: ${status.lastModified}`);
      
      if (!status.inSync) {
        console.log('\n⚠️  Schema synchronization needed!');
        console.log('Run: npm run schema:sync');
      }
      
    } catch (error) {
      console.error('❌ Error getting status:', error.message);
    }
  }

  async sync() {
    console.log('🔄 Starting Schema Synchronization');
    console.log('=' .repeat(50));
    
    try {
      const result = await this.dbManager.forceSchemaSync();
      
      if (result.migrationNeeded) {
        console.log('✅ Migration completed successfully!');
        console.log(`📊 Changes applied: ${result.changes.length}`);
        
        if (result.changes.length > 0) {
          console.log('\n📝 Migration Log:');
          result.changes.forEach(change => {
            console.log(`  ${change}`);
          });
        }
      } else {
        console.log('ℹ️  No migration needed - schema is already synchronized');
      }
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  async history() {
    console.log('📚 Migration History');
    console.log('=' .repeat(50));
    
    try {
      const history = await this.dbManager.getMigrationHistory();
      
      if (history.length === 0) {
        console.log('No migrations found');
        return;
      }
      
      history.forEach((migration, index) => {
        console.log(`\n${index + 1}. Migration ${migration.schema_version}`);
        console.log(`   Date: ${migration.migration_date}`);
        console.log(`   Status: ${migration.status === 'completed' ? '✅' : '❌'} ${migration.status}`);
        console.log(`   Hash: ${migration.schema_hash.substring(0, 16)}...`);
        
        if (migration.migration_log) {
          const logLines = migration.migration_log.split('\n').slice(0, 3);
          console.log(`   Log: ${logLines[0]}`);
          if (logLines.length > 1) {
            console.log(`        ${logLines[1]}`);
          }
          if (logLines.length > 2) {
            console.log(`        ... (${migration.migration_log.split('\n').length} total lines)`);
          }
        }
      });
      
    } catch (error) {
      console.error('❌ Error getting history:', error.message);
    }
  }

  async info() {
    console.log('📋 Database Information');
    console.log('=' .repeat(50));
    
    try {
      const db = this.dbManager.getDatabase();
      
      // Get all tables
      const tables = await new Promise((resolve, reject) => {
        db.all(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'`, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.name));
        });
      });

      console.log(`Total Tables: ${tables.length}`);
      console.log('\nTable Details:');
      
      for (const tableName of tables) {
        const columns = await new Promise((resolve, reject) => {
          db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        });
        
        const rowCount = await new Promise((resolve, reject) => {
          db.get(`SELECT COUNT(*) as count FROM ${tableName}`, (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          });
        });

        console.log(`  📊 ${tableName}: ${columns.length} columns, ${rowCount} rows`);
      }
      
    } catch (error) {
      console.error('❌ Error getting database info:', error.message);
    }
  }

  async backup() {
    console.log('💾 Creating Database Backup');
    console.log('=' .repeat(50));
    
    try {
      const backupDir = path.join(__dirname, '../../backups');
      
      // Ensure backup directory exists
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        console.log(`📁 Created backup directory: ${backupDir}`);
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(backupDir, `app_backup_${timestamp}.db`);
      
      // Copy database file
      fs.copyFileSync(this.dbPath, backupPath);
      
      const stats = fs.statSync(backupPath);
      
      console.log('✅ Backup created successfully!');
      console.log(`📁 Location: ${backupPath}`);
      console.log(`📊 Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`📅 Created: ${stats.birthtime}`);
      
    } catch (error) {
      console.error('❌ Backup failed:', error.message);
    }
  }

  async validate() {
    console.log('🔍 Validating Schema File');
    console.log('=' .repeat(50));

    try {
      if (!fs.existsSync(this.schemaPath)) {
        console.error('❌ Schema file not found:', this.schemaPath);
        return;
      }

      const schemaContent = fs.readFileSync(this.schemaPath, 'utf8');
      const issues = [];

      // Basic validation
      if (!schemaContent.trim()) {
        issues.push('Schema file is empty');
      }

      if (!schemaContent.includes('CREATE TABLE')) {
        issues.push('No CREATE TABLE statements found');
      }

      // Check for common issues
      const lines = schemaContent.split('\n');
      lines.forEach((line, index) => {
        const trimmed = line.trim();
        if (trimmed.includes('CREATE TABLE') && !trimmed.includes('(')) {
          const nextLine = lines[index + 1];
          if (!nextLine || !nextLine.trim().startsWith('(')) {
            issues.push(`Line ${index + 1}: CREATE TABLE statement may be malformed`);
          }
        }
      });

      console.log(`📄 File: ${this.schemaPath}`);
      console.log(`📊 Size: ${schemaContent.length} bytes`);
      console.log(`📝 Lines: ${lines.length}`);
      console.log(`📅 Modified: ${fs.statSync(this.schemaPath).mtime}`);

      if (issues.length === 0) {
        console.log('✅ Schema file validation passed');
      } else {
        console.log('❌ Schema file validation failed:');
        issues.forEach(issue => {
          console.log(`  • ${issue}`);
        });
      }

    } catch (error) {
      console.error('❌ Validation failed:', error.message);
    }
  }

  async recover() {
    console.log('🔧 Recovering from Migration Issues');
    console.log('=' .repeat(50));

    try {
      // Initialize without auto-sync to avoid triggering the error
      const dbManager = new DatabaseManager({
        dbPath: this.dbPath,
        schemaPath: this.schemaPath,
        enableAutoSync: false,
        enableWatcher: false
      });

      await dbManager.initialize();

      console.log('1️⃣ Clearing duplicate migration records...');
      await dbManager.migrationManager.clearDuplicateMigrations();

      console.log('2️⃣ Dropping testing_db table if exists...');
      const db = dbManager.getDatabase();
      await new Promise((resolve, reject) => {
        db.run('DROP TABLE IF EXISTS testing_db', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      console.log('3️⃣ Testing schema sync...');
      const result = await dbManager.forceSchemaSync();

      if (result.migrationNeeded) {
        console.log('✅ Recovery migration completed!');
      } else {
        console.log('✅ Schema is now synchronized!');
      }

      await dbManager.close();
      console.log('🎉 Recovery completed successfully!');

    } catch (error) {
      console.error('❌ Recovery failed:', error.message);
      console.log('\n🔧 Manual recovery steps:');
      console.log('1. sqlite3 ../app.db "DELETE FROM schema_migrations WHERE id NOT IN (SELECT MAX(id) FROM schema_migrations GROUP BY schema_hash);"');
      console.log('2. sqlite3 ../app.db "DROP TABLE IF EXISTS testing_db;"');
      console.log('3. npm run schema:sync');
    }
  }

  showHelp() {
    console.log('🛠️  Schema Management CLI');
    console.log('=' .repeat(50));
    console.log('Usage: node schema-cli.js <command>');
    console.log('');
    console.log('Commands:');
    console.log('  status    Show current schema synchronization status');
    console.log('  sync      Force schema synchronization');
    console.log('  history   Show migration history');
    console.log('  info      Show database information');
    console.log('  backup    Create database backup');
    console.log('  validate  Validate schema file');
    console.log('  recover   Recover from migration issues');
    console.log('  help      Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  node schema-cli.js status');
    console.log('  node schema-cli.js sync');
    console.log('  node schema-cli.js recover');
    console.log('  npm run schema:status');
    console.log('  npm run schema:sync');
  }

  async run() {
    const command = process.argv[2];
    
    if (!command || command === 'help') {
      this.showHelp();
      return;
    }

    try {
      await this.initialize();
      
      switch (command) {
        case 'status':
          await this.status();
          break;
        case 'sync':
          await this.sync();
          break;
        case 'history':
          await this.history();
          break;
        case 'info':
          await this.info();
          break;
        case 'backup':
          await this.backup();
          break;
        case 'validate':
          await this.validate();
          break;
        case 'recover':
          await this.recover();
          break;
        default:
          console.error(`❌ Unknown command: ${command}`);
          this.showHelp();
          process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ CLI Error:', error.message);
      process.exit(1);
    } finally {
      if (this.dbManager) {
        await this.dbManager.close();
      }
    }
  }
}

// Run CLI if called directly
if (require.main === module) {
  const cli = new SchemaCLI();
  cli.run().catch(console.error);
}

module.exports = SchemaCLI;
