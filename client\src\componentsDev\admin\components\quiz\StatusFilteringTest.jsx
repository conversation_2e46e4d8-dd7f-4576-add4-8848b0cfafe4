import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import axios from 'axios';

// Styled components
const TestContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#121212',
  minHeight: '100vh',
  padding: '24px',
}));

const TestCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  marginBottom: '16px',
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  '& .MuiTableHead-root': {
    backgroundColor: '#121212',
  },
  '& .MuiTableCell-root': {
    borderColor: '#333',
    color: '#fff',
  },
  '& .MuiTableCell-head': {
    backgroundColor: '#121212',
    fontWeight: 600,
    color: '#4CAF50',
  },
}));

const StatusFilteringTest = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [lookupData, setLookupData] = useState({
    difficulties: [],
    questionTypes: [],
    answerTypes: []
  });

  useEffect(() => {
    runStatusFilteringTest();
  }, []);

  const runStatusFilteringTest = async () => {
    setLoading(true);
    try {
      // Fetch all lookup data
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`)
      ]);

      const allData = {
        difficulties: difficulties.data || [],
        questionTypes: questionTypes.data || [],
        answerTypes: answerTypes.data || []
      };

      // Filter to only active items (status = 1)
      const activeData = {
        difficulties: allData.difficulties.filter(item => item.status === 1),
        questionTypes: allData.questionTypes.filter(item => item.status === 1),
        answerTypes: allData.answerTypes.filter(item => item.status === 1)
      };

      setLookupData(activeData);

      // Run comprehensive tests
      const results = {};

      // Test 1: Status filtering
      results.statusFiltering = {
        passed: true,
        message: `Filtered ${activeData.difficulties.length}/${allData.difficulties.length} difficulties, ${activeData.questionTypes.length}/${allData.questionTypes.length} question types, ${activeData.answerTypes.length}/${allData.answerTypes.length} answer types`,
        details: {
          difficulties: { total: allData.difficulties.length, active: activeData.difficulties.length },
          questionTypes: { total: allData.questionTypes.length, active: activeData.questionTypes.length },
          answerTypes: { total: allData.answerTypes.length, active: activeData.answerTypes.length }
        }
      };

      // Test 2: Answer type independence
      const expectedAnswerTypes = ['multiple_choice', 'single_choice', 'true_false'];
      const availableAnswerTypes = activeData.answerTypes.map(at => at.type_name);
      const hasRequiredAnswerTypes = expectedAnswerTypes.every(type => availableAnswerTypes.includes(type));

      results.answerTypeIndependence = {
        passed: hasRequiredAnswerTypes && activeData.answerTypes.length >= 3,
        message: hasRequiredAnswerTypes
          ? `Answer types are independent: ${activeData.answerTypes.length} types available including all required types`
          : `Missing required answer types. Available: ${availableAnswerTypes.join(', ')}`,
        details: {
          availableAnswerTypes,
          expectedAnswerTypes,
          hasRequiredTypes: hasRequiredAnswerTypes,
          totalAnswerTypes: activeData.answerTypes.length
        }
      };

      // Test 3: Data completeness
      const hasRequiredData = activeData.difficulties.length > 0 && 
                             activeData.questionTypes.length > 0 && 
                             activeData.answerTypes.length > 0;

      results.dataCompleteness = {
        passed: hasRequiredData,
        message: hasRequiredData ? 'All required lookup data is available' : 'Missing required lookup data',
        details: {
          hasDifficulties: activeData.difficulties.length > 0,
          hasQuestionTypes: activeData.questionTypes.length > 0,
          hasAnswerTypes: activeData.answerTypes.length > 0
        }
      };

      // Test 4: Difficulty bonus points
      const difficultiesWithBonusPoints = activeData.difficulties.filter(d => d.bonus_points && d.bonus_points > 0);
      results.difficultyBonusPoints = {
        passed: true,
        message: `${difficultiesWithBonusPoints.length}/${activeData.difficulties.length} difficulties have bonus points configured`,
        details: difficultiesWithBonusPoints.map(d => ({
          name: d.level_name,
          bonusPoints: d.bonus_points,
          timeMultiplier: d.time_multiplier
        }))
      };

      // Test 5: Form validation readiness
      const formReady = hasRequiredData && hasRequiredAnswerTypes;
      results.formValidation = {
        passed: formReady,
        message: formReady ? 'Form is ready for question creation with independent answer types' : 'Form not ready - missing required data',
        details: {
          hasActiveData: hasRequiredData,
          hasRequiredAnswerTypes: hasRequiredAnswerTypes,
          canCreateQuestions: formReady
        }
      };

      setTestResults(results);

      // Show summary notification
      const passedTests = Object.values(results).filter(r => r.passed).length;
      const totalTests = Object.keys(results).length;
      
      if (passedTests === totalTests) {
        enqueueSnackbar(`✅ All ${totalTests} status filtering tests passed!`, { variant: 'success' });
      } else {
        enqueueSnackbar(`⚠️ ${passedTests}/${totalTests} tests passed`, { variant: 'warning' });
      }

    } catch (error) {
      console.error('Status filtering test failed:', error);
      enqueueSnackbar(`❌ Status filtering test failed: ${error.message}`, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const getTestIcon = (result) => {
    if (!result) return <InfoIcon sx={{ color: '#2196F3' }} />;
    return result.passed ? <CheckIcon sx={{ color: '#4CAF50' }} /> : <ErrorIcon sx={{ color: '#F44336' }} />;
  };

  const getTestColor = (result) => {
    if (!result) return '#2196F3';
    return result.passed ? '#4CAF50' : '#F44336';
  };

  if (loading) {
    return (
      <TestContainer>
        <Box textAlign="center" py={4}>
          <CircularProgress sx={{ color: '#4CAF50', mb: 2 }} />
          <Typography variant="h6" sx={{ color: '#fff' }}>
            Running Status Filtering Tests...
          </Typography>
        </Box>
      </TestContainer>
    );
  }

  return (
    <TestContainer>
      <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 3 }}>
        Status Filtering Test Results
      </Typography>

      <Grid container spacing={3}>
        {/* Test Results Summary */}
        <Grid item xs={12} md={6}>
          <TestCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                Test Results Summary
              </Typography>
              
              {Object.entries(testResults).map(([testName, result]) => (
                <Box
                  key={testName}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    mb: 2,
                    p: 2,
                    backgroundColor: '#121212',
                    border: `1px solid ${getTestColor(result)}`,
                    borderRadius: '8px',
                  }}
                >
                  {getTestIcon(result)}
                  <Box flex={1}>
                    <Typography variant="body1" sx={{ color: '#fff', fontWeight: 600 }}>
                      {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#ccc' }}>
                      {result.message}
                    </Typography>
                  </Box>
                  <Chip
                    label={result.passed ? 'PASS' : 'FAIL'}
                    size="small"
                    sx={{
                      backgroundColor: getTestColor(result),
                      color: '#fff',
                    }}
                  />
                </Box>
              ))}
            </CardContent>
          </TestCard>
        </Grid>

        {/* Active Lookup Data */}
        <Grid item xs={12} md={6}>
          <TestCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                Active Lookup Data
              </Typography>
              
              <StyledTableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Type</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Details</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {lookupData.difficulties.map((item) => (
                      <TableRow key={`diff-${item.difficulty_id}`}>
                        <TableCell>Difficulty</TableCell>
                        <TableCell>{item.level_name}</TableCell>
                        <TableCell>
                          {item.bonus_points && (
                            <Chip label={`+${item.bonus_points}pts`} size="small" sx={{ backgroundColor: '#4CAF50', color: '#fff', mr: 1 }} />
                          )}
                          {item.time_multiplier && item.time_multiplier !== 1 && (
                            <Chip label={`${item.time_multiplier}x`} size="small" sx={{ backgroundColor: '#2196F3', color: '#fff' }} />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    {lookupData.questionTypes.map((item) => (
                      <TableRow key={`qt-${item.question_type_id}`}>
                        <TableCell>Question Type</TableCell>
                        <TableCell>{item.display_name}</TableCell>
                        <TableCell>
                          <Chip label={item.type_name} size="small" sx={{ backgroundColor: '#FF9800', color: '#fff' }} />
                        </TableCell>
                      </TableRow>
                    ))}
                    {lookupData.answerTypes.map((item) => (
                      <TableRow key={`at-${item.answer_type_id}`}>
                        <TableCell>Answer Type</TableCell>
                        <TableCell>{item.display_name}</TableCell>
                        <TableCell>
                          <Chip label={item.type_name} size="small" sx={{ backgroundColor: '#9C27B0', color: '#fff' }} />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>
            </CardContent>
          </TestCard>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box mt={3} display="flex" gap={2}>
        <Button
          variant="contained"
          onClick={runStatusFilteringTest}
          disabled={loading}
          sx={{
            backgroundColor: '#4CAF50',
            color: '#fff',
            '&:hover': { backgroundColor: '#45a049' }
          }}
        >
          Re-run Tests
        </Button>
        
        <Button
          variant="outlined"
          onClick={() => window.location.reload()}
          sx={{
            borderColor: '#333',
            color: '#ccc',
            '&:hover': {
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
            },
          }}
        >
          Refresh Page
        </Button>
      </Box>
    </TestContainer>
  );
};

export default StatusFilteringTest;
