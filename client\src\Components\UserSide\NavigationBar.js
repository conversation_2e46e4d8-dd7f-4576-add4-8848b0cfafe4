import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Icon<PERSON>utt<PERSON>,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Typography,
  Divider,
  Modal,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { StyledAppBar } from "./styles";
import { useNavigate, useLocation } from "react-router-dom";
import { SQUARE_PAYMENT_LINK, WEBSITE_URL } from "../../lib/constants";
import ThankYouDialog from "./ThankDialog";
import SearchModal from "./SearchModal";
import { useMode } from "../../utils/useMode"

const NavigationBar = ({ handleTabClick, isShowdown = true }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showQrModal, setShowQrModal] = useState(false);
  const [openSearchModal, setOpenSearchModal] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const mode = useMode();

  React.useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : "auto";
  }, [isOpen]);

  const handleToggle = () => setIsOpen(!isOpen);
  const handleModalToggle = () => setShowModal(!showModal);
  const handleQrModalToggle = () => setShowQrModal(!showQrModal);
  // const handleLogoClick = () => {
    // if (mode === "jukebox" || mode === "openmic" || isShowdown === false) {
    //   window.location.href = "https://mindyourmusic.live";
    //   return;
    // }

    // if (location.pathname === "/") {
    //   const homeElement = document.getElementById("home");
    //   if (homeElement) {
    //     homeElement.scrollIntoView({ behavior: "smooth" });
    //   }
    // } else {
    //   navigate("/"); // Redirect to the homepage
    // }
  // };

  const handleNavigateToAdminLogin = () => {
    navigate("/AdminLogin");
  };

  return (
    <StyledAppBar position="fixed">
      <Toolbar
        sx={{
          height: "120px",
          justifyContent: "space-between",
          alignItems: "center",
          background: "rgba(41, 32, 14, 0.8)",
        }}
      >
        {/* Logo */}
        <Box
          sx={{ display: "flex", alignItems: "center", cursor: "pointer" }}
          // onClick={handleLogoClick}
        >
          {
            mode === "jukebox" || mode === "openmic" || isShowdown === false ? <img src="/mind_your_music.png" alt="mym Logo" style={{ height: "110px" }} /> :
              <img src="/white_logo.png" alt="IM Logo" style={{ height: "40px" }} />
          }
        </Box>

        {/* Icons */}
        {
          isShowdown && (

            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <IconButton color="inherit" onClick={() => setOpenSearchModal(true)}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617m-2.006-.742A6.98 6.98 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.98 6.98 0 0 0 4.875-1.975z"
                  />
                </svg>
              </IconButton>
              <SearchModal
                Modal
                open={openSearchModal}
                onClose={() => setOpenSearchModal(false)}
              />
              {/* <a
            href={SQUARE_PAYMENT_LINK}
            style={{
              textDecoration: "none",
              color: "inherit",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <IconButton color="inherit">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
              >
                <g
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                >
                  <path d="M17 4v1.882c0 .685.387 1.312 1 1.618s1 .933 1 1.618V18a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9.118c0-.685.387-1.312 1-1.618s1-.933 1-1.618V4M6 4h12z" />
                  <path d="M14 10h-1a2 2 0 0 0-2 2v2c0 1.105-.395 2-1.5 2H14m-4-3h3" />
                </g>
              </svg>
            </IconButton>
          </a> */}
              <IconButton color="inherit" onClick={() => setShowModal(true)}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  >
                    <path d="M17 4v1.882c0 .685.387 1.312 1 1.618s1 .933 1 1.618V18a3 3 0 0 1-3 3H8a3 3 0 0 1-3-3V9.118c0-.685.387-1.312 1-1.618s1-.933 1-1.618V4M6 4h12z" />
                    <path d="M14 10h-1a2 2 0 0 0-2 2v2c0 1.105-.395 2-1.5 2H14m-4-3h3" />
                  </g>
                </svg>
              </IconButton>

              <IconButton color="inherit" onClick={handleToggle}>
                <MenuIcon fontSize="1em" />
              </IconButton>
            </Box>

          )
        }
      </Toolbar>
      <Drawer
        anchor="right"
        open={isOpen}
        onClose={handleToggle}
        sx={{
          "& .MuiDrawer-paper": {
            width: 250,
            padding: 2,
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          {
            mode === "jukebox" || mode === "openmic" ? <img src="/mind_your_music.png" alt="mym Logo" style={{ height: "90px" }} /> :
              <img src="/white_logo.png" alt="IM Logo" style={{ height: "40px" }} />
          }
          <IconButton onClick={handleToggle}>
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Sidebar Options */}
        <List>
          <ListItem
            onClick={() => {
              handleTabClick("playlists");
              handleToggle();
            }}
          >
            <ListItemText primary="Playlists" />
          </ListItem>

          <a
            href={SQUARE_PAYMENT_LINK}
            style={{
              textDecoration: "none",
              color: "inherit",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <ListItem>
              <ListItemText primary="Tip Jar" />
            </ListItem>
          </a>
          <a
            href={SQUARE_PAYMENT_LINK}
            style={{
              textDecoration: "none",
              color: "inherit",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <ListItem>
              <ListItemText primary="Donation" />
            </ListItem>
          </a>

          <ListItem button onClick={handleQrModalToggle}>
            <ListItemText primary="Payment Code" />
          </ListItem>

          {
            (mode !== "jukebox" && mode !== "openmic") && (
              <a
                href={WEBSITE_URL}
                target="_blank"
                rel="noreferrer"
                style={{
                  textDecoration: "none",
                  color: "inherit",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <ListItem>
                  <ListItemText primary="Website" />
                </ListItem>
              </a>
            )
          }

          <ListItem button onClick={handleModalToggle}>
            <ListItemText primary="Contact" />
          </ListItem>

          {
            (mode !== "jukebox" && mode !== "openmic") && (
              <ListItem
                onClick={() => handleNavigateToAdminLogin()}
                sx={{ cursor: "pointer" }}
              >
                <ListItemText primary="Admin Login" />
              </ListItem>
            )}
        </List>
      </Drawer>

      {/* Backdrop */}
      {isOpen && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: "rgba(0, 0, 0, 0.5)",
            zIndex: 1200,
          }}
        />
      )}
      <Modal open={showQrModal} onClose={handleQrModalToggle}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            bgcolor: "#1E1E1E",
            py: 4,
            px: 2,
            borderRadius: 2,
            boxShadow: 24,
            textAlign: "center",
            maxWidth: "80%",
            width: "100%",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mt: -2,
            }}
          >
            <Typography variant="h6" noWrap>
              Payment Code
            </Typography>
            <IconButton onClick={handleQrModalToggle}>
              <CloseIcon />
            </IconButton>
          </Box>
          <img
            src="/assets/QR.png"
            alt="QR Code"
            style={{ margin: "20px 0", width: "150px", height: "150px" }}
          />
          <Typography>Scan this code to make a payment</Typography>
        </Box>
      </Modal>
      <ThankYouDialog
        from="navBar"
        open={showModal}
        onClose={() => setShowModal(false)}
      />
    </StyledAppBar>
  );
};

export default NavigationBar;
