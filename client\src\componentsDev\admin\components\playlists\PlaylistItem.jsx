import React from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { getImageUrl } from './ImageHelper';

const PlaylistItem = ({ playlist, onEdit, onDelete }) => {
  const getTotalDuration = (songs = []) => {
    let totalSeconds = 0;

    songs.forEach((song) => {
      const [min, sec] = song.duration.split(':').map(Number);
      totalSeconds += min * 60 + sec;
    });

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours} hr ${minutes} min ${seconds} sec`;
    } else {
      return `${minutes} min ${seconds} sec`;
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mt: 3,
        p: 2,
        borderRadius: 2,
        border: '1px solid rgba(255, 255, 255, 0.1)',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 255, 0.08)',
          transform: 'translateY(-2px)',
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', columnGap: '1rem' }}>
        <Box
          component="img"
          src={getImageUrl(playlist.image)}
          alt={playlist.title}
          sx={{
            borderRadius: 2,
            border: '1px solid rgba(255, 255, 255, 0.2)',
            width: '60px',
            height: '60px',
            objectFit: 'cover',
          }}
        />
        <Box>
          <Typography 
            variant="h5" 
            sx={{ 
              fontSize: '18px', 
              fontWeight: 'bold',
              color: 'white',
              mb: 0.5,
            }}
          >
            {playlist.title}
          </Typography>
          <Typography 
            variant="h6" 
            sx={{ 
              fontSize: '14px',
              color: 'rgba(255, 255, 255, 0.7)',
            }}
          >
            {playlist.songs?.length || 0} songs • {getTotalDuration(playlist.songs)}
          </Typography>
          {playlist.description && (
            <Typography 
              variant="body2" 
              sx={{ 
                fontSize: '12px',
                color: 'rgba(255, 255, 255, 0.5)',
                mt: 0.5,
                maxWidth: '300px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {playlist.description}
            </Typography>
          )}
        </Box>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', columnGap: '0.5rem' }}>
        <IconButton
          onClick={() => onEdit(playlist)}
          sx={{
            color: 'rgba(255, 255, 255, 0.7)',
            '&:hover': {
              color: '#4caf50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
            },
          }}
        >
          <EditIcon />
        </IconButton>
        <IconButton
          onClick={() => onDelete(playlist.playlist_master_id)}
          sx={{
            color: 'rgba(255, 255, 255, 0.7)',
            '&:hover': {
              color: '#f44336',
              backgroundColor: 'rgba(244, 67, 54, 0.1)',
            },
          }}
        >
          <DeleteIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

export default PlaylistItem;
