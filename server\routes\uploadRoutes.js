const express = require("express");
const router = express.Router();
const quizUpload = require("../middleware/quizUploadMiddleware");

// Quiz image upload endpoint
router.post("/quiz", quizUpload.single("image"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No image file provided" });
    }

    const imageUrl = `/uploads/quiz/${req.file.filename}`;
    
    res.status(200).json({
      message: "Image uploaded successfully",
      imageUrl: imageUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    console.error("Error uploading quiz image:", error);
    res.status(500).json({ error: "Failed to upload image" });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error.code === "LIMIT_FILE_SIZE") {
    return res.status(400).json({ error: "File too large. Maximum size is 5MB." });
  }
  
  if (error.message === "Only image files (JPEG, PNG, GIF, WebP) are allowed") {
    return res.status(400).json({ error: error.message });
  }
  
  res.status(500).json({ error: "File upload error: " + error.message });
});

module.exports = router;
