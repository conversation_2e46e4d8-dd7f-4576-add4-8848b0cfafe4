import React, { useEffect, useMemo, useState } from "react";
import Header from "../Header";
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  Radio,
  Checkbox,
} from "@mui/material";

const API = process.env.REACT_APP_API_URL || "http://localhost:3001";

const QuizMaster = () => {
  const [songs, setSongs] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [answerTypes, setAnswerTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const [form, setForm] = useState({
    songs_master_id: "",
    difficulty_id: "",
    question_text: "",
    time_limit_seconds: 30,
    answer_type: "multiple_choice",
    answers: [
      { answer_text: "", is_correct: true, answer_order: 1 },
      { answer_text: "", is_correct: false, answer_order: 2 },
    ],
  });

  const loadAll = async () => {
    setLoading(true);
    try {
      const [songsRes, diffRes, listRes, aTypes] = await Promise.all([
        fetch(`${API}/api/songs`).then((r) => r.json()),
        fetch(`${API}/api/meta/difficulties`).then((r) => r.json()),
        fetch(`${API}/api/admin/questions`).then((r) => r.json()),
        fetch(`${API}/api/meta/answer-types`).then((r) => r.json()),
      ]);
      setSongs(songsRes || []);
      setDifficulties(diffRes || []);
      setQuestions(listRes || []);
      setAnswerTypes((aTypes || []).filter(t => ["multiple_choice","single_choice","true_false"].includes(t.type_name)));
    } catch (e) {
      setError("Failed to load initial data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAll();
  }, []);

  const handleAnswerChange = (index, key, value) => {
    setForm((prev) => {
      const next = { ...prev };
      next.answers = prev.answers.map((a, i) => (i === index ? { ...a, [key]: value } : a));
      return next;
    });
  };

  const setCorrect = (index) => {
    setForm((prev) => ({
      ...prev,
      answers: prev.answers.map((a, i) => ({ ...a, is_correct: i === index })),
    }));
  };

  const toggleCorrectMulti = (index) => {
    setForm((prev) => ({
      ...prev,
      answers: prev.answers.map((a, i) => (i === index ? { ...a, is_correct: !a.is_correct } : a)),
    }));
  };

  const onChangeAnswerType = (value) => {
    setForm((prev) => {
      let next = { ...prev, answer_type: value };
      if (value === "true_false") {
        next.answers = [
          { answer_text: "True", is_correct: true, answer_order: 1 },
          { answer_text: "False", is_correct: false, answer_order: 2 },
        ];
      } else if (value === "single_choice") {
        // Ensure exactly one correct remains
        const firstCorrect = next.answers.findIndex((a) => a.is_correct);
        next.answers = next.answers.map((a, i) => ({ ...a, is_correct: i === (firstCorrect >= 0 ? firstCorrect : 0) }));
      }
      return next;
    });
  };

  const addAnswer = () => {
    setForm((prev) => ({
      ...prev,
      answers: [
        ...prev.answers,
        { answer_text: "", is_correct: false, answer_order: prev.answers.length + 1 },
      ],
    }));
  };

  const removeAnswer = (index) => {
    setForm((prev) => {
      const next = { ...prev };
      next.answers = prev.answers
        .filter((_, i) => i !== index)
        .map((a, i) => ({ ...a, answer_order: i + 1 }));
      if (!next.answers.some((a) => a.is_correct) && next.answers.length > 0) {
        next.answers[0].is_correct = true;
      }
      return next;
    });
  };

  const canSubmit = useMemo(() => {
    if (!form.songs_master_id || !form.difficulty_id) return false;
    if (!form.question_text.trim()) return false;
    if (form.answers.length < 2) return false;
    const correct = form.answers.filter((a) => a.is_correct).length;
    if (form.answer_type === "true_false" && correct !== 1) return false;
    if (form.answer_type === "single_choice" && correct !== 1) return false;
    if (form.answer_type === "multiple_choice" && correct < 1) return false;
    if (form.answers.some((a) => !a.answer_text.trim())) return false;
    return true;
  }, [form]);

  const submit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    try {
      const res = await fetch(`${API}/api/admin/questions`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          songs_master_id: Number(form.songs_master_id),
          question_text: form.question_text.trim(),
          difficulty_id: Number(form.difficulty_id),
          time_limit_seconds: Number(form.time_limit_seconds) || 30,
          answer_type: form.answer_type,
          answers: form.answers.map((a) => ({
            answer_text: a.answer_text.trim(),
            is_correct: !!a.is_correct,
            answer_order: a.answer_order,
          })),
        }),
      });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.error || "Failed to create question");
      }
      setSuccess("Question created");
      setForm({
        songs_master_id: "",
        difficulty_id: "",
        question_text: "",
        time_limit_seconds: 30,
        answer_type: "multiple_choice",
        answers: [
          { answer_text: "", is_correct: true, answer_order: 1 },
          { answer_text: "", is_correct: false, answer_order: 2 },
        ],
      });
      await loadAll();
    } catch (e) {
      setError(e.message);
    }
  };

  if (loading) return <div>Loading…</div>;

  return (
    <div>
      <Header />
      <Box sx={{ maxWidth: 1000, mx: "auto", p: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Quiz Questions</Typography>
          <Button
            onClick={() => document.getElementById("quiz-form").scrollIntoView({ behavior: "smooth" })}
            sx={{ bgcolor: "#1DB850", color: "white", '&:hover': { bgcolor: "#159c42" } }}
          >
            Add Question
          </Button>
        </Stack>

        {error && <Box sx={{ bgcolor: "#fde2e2", color: "#b91c1c", p: 1, mt: 1 }}>{error}</Box>}
        {success && <Box sx={{ bgcolor: "#dcfce7", color: "#166534", p: 1, mt: 1 }}>{success}</Box>}

        <Box id="quiz-form" sx={{ mt: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>Create Question</Typography>
          <Card>
            <CardContent>
              <Stack component="form" onSubmit={submit} spacing={2}>
                <FormControl fullWidth>
                  <InputLabel>Song</InputLabel>
                  <Select
                    label="Song"
                    value={form.songs_master_id}
                    onChange={(e) => setForm({ ...form, songs_master_id: e.target.value })}
                  >
                    <MenuItem value=""><em>Select a song</em></MenuItem>
                    {songs.map((s) => (
                      <MenuItem key={s.songs_master_id} value={s.songs_master_id}>
                        {s.name} {s.artist ? `- ${s.artist}` : ""}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Difficulty</InputLabel>
                  <Select
                    label="Difficulty"
                    value={form.difficulty_id}
                    onChange={(e) => setForm({ ...form, difficulty_id: e.target.value })}
                  >
                    <MenuItem value=""><em>Select difficulty</em></MenuItem>
                    {difficulties.map((d) => (
                      <MenuItem key={d.difficulty_id} value={d.difficulty_id}>{d.level_name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  label="Question"
                  multiline
                  minRows={3}
                  value={form.question_text}
                  onChange={(e) => setForm({ ...form, question_text: e.target.value })}
                />

                <TextField
                  type="number"
                  label="Time limit (seconds)"
                  inputProps={{ min: 5, max: 600 }}
                  value={form.time_limit_seconds}
                  onChange={(e) => setForm({ ...form, time_limit_seconds: e.target.value })}
                />

                <FormControl fullWidth>
                  <InputLabel>Answer Type</InputLabel>
                  <Select
                    label="Answer Type"
                    value={form.answer_type}
                    onChange={(e) => onChangeAnswerType(e.target.value)}
                  >
                    {answerTypes.map((t) => (
                      <MenuItem key={t.answer_type_id} value={t.type_name}>{t.display_name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Box>
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 1 }}>
                    <Typography variant="body2">Answers</Typography>
                    <Button size="small" variant="outlined" onClick={addAnswer} disabled={form.answer_type === 'true_false'}>Add</Button>
                  </Stack>

                  <Stack spacing={1}>
                    {form.answers.map((ans, i) => (
                      <Stack key={i} direction="row" spacing={1} alignItems="center">
                        {form.answer_type === 'multiple_choice' && (
                          <Checkbox checked={!!ans.is_correct} onChange={() => toggleCorrectMulti(i)} />
                        )}
                        {(form.answer_type === 'single_choice' || form.answer_type === 'true_false') && (
                          <Radio checked={!!ans.is_correct} onChange={() => setCorrect(i)} />
                        )}
                        <TextField
                          placeholder={`Answer ${i + 1}`}
                          value={ans.answer_text}
                          onChange={(e) => handleAnswerChange(i, "answer_text", e.target.value)}
                          fullWidth
                          disabled={form.answer_type === 'true_false'}
                        />
                        <Button size="small" variant="outlined" onClick={() => removeAnswer(i)} disabled={form.answers.length <= 2 || form.answer_type === 'true_false'}>Remove</Button>
                      </Stack>
                    ))}
                  </Stack>
                </Box>

                <Box sx={{ pt: 1 }}>
                  <Button type="submit" disabled={!canSubmit} sx={{ bgcolor: canSubmit ? "#1DB850" : "#9CA3AF", color: "white", '&:hover': { bgcolor: canSubmit ? "#159c42" : "#9CA3AF" } }}>
                    Create Question
                  </Button>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Box>

        <Card sx={{ mt: 4 }}>
          <CardContent>
            {questions.length === 0 ? (
              <Typography color="text.secondary">No questions yet.</Typography>
            ) : (
              <Stack divider={<Divider flexItem />}>
                {questions.map((q) => (
                  <Box key={q.question_id} sx={{ py: 1 }}>
                    <Typography fontWeight={600}>{q.question_text}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Song: {q.song_name} • Difficulty: {q.level_name} • Time: {q.time_limit_seconds}s
                    </Typography>
                  </Box>
                ))}
              </Stack>
            )}
          </CardContent>
        </Card>
      </Box>
    </div>
  );
};

export default QuizMaster;


