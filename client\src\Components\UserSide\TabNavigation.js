import React from "react";
import {
  BottomTabBar,
  CurvedBackground,
  Tab,
  TabIconWrapper,
  Tab<PERSON>abel,
} from "./styles";
import PersonIcon from "@mui/icons-material/Person";
import WhatshotIcon from "@mui/icons-material/Whatshot";
import { Box } from "@mui/material";

// TabBar component
const TabNavigation = ({ activeTab, handleTabClick }) => {
  return (
    <>
      <BottomTabBar>
        {/* Home tab button */}
        <Tab onClick={() => handleTabClick("home")}>
          {activeTab === "home" && <CurvedBackground activeTab={"home"} />}
          {activeTab === "home" ? (
            <TabIconWrapper isActive={activeTab === "home"} activeTab="home">
              <img
                src="/assets/home-color.svg"
                alt="Home Icon"
                style={{ height: "24px" }}
              />
            </TabIconWrapper>
          ) : (
            <TabIconWrapper isActive={false} activeTab="home">
              <img
                src="/assets/home.svg"
                alt="Home Icon"
                style={{ height: "24px" }}
              />
            </TabIconWrapper>
          )}
          <TabLabel isActive={activeTab === "home"}>Home</TabLabel>
        </Tab>

        {/* Playlists tab button */}
        <Tab onClick={() => handleTabClick("playlists")}>
          {activeTab === "playlists" && (
            <CurvedBackground activeTab={"playlists"} />
          )}
          {activeTab === "playlists" ? (
            <TabIconWrapper
              isActive={activeTab === "playlists"}
              activeTab="playlists"
            >
              <img
                src="/assets/playlist-color.svg"
                alt="Playlist Icon"
                style={{ height: "24px" }}
              />
            </TabIconWrapper>
          ) : (
            <TabIconWrapper isActive={false} activeTab="playlists">
              <img
                src="/assets/playlist.svg"
                alt="Playlist Icon"
                style={{ height: "20px" }}
              />
            </TabIconWrapper>
          )}
          <TabLabel isActive={activeTab === "playlists"}>Playlists</TabLabel>
        </Tab>

        {/* Popular Songs Tab */}
        <Tab onClick={() => handleTabClick("popularSongs")}>
          {activeTab === "popularSongs" && (
            <CurvedBackground activeTab="popularSongs" />
          )}
          <TabIconWrapper
            isActive={activeTab === "popularSongs"}
            activeTab="popularSongs"
          >
            <WhatshotIcon
              style={{
                height: "24px",
                color: activeTab === "popularSongs" ? "#966300" : "#aaa",
              }}
            />
          </TabIconWrapper>
          <TabLabel isActive={activeTab === "popularSongs"}>
            Popular Songs
          </TabLabel>
        </Tab>

        {/* Artists Tab */}
        <Tab onClick={() => handleTabClick("artists")}>
          {activeTab === "artists" && <CurvedBackground activeTab="artists" />}
          <TabIconWrapper
            isActive={activeTab === "artists"}
            activeTab="artists"
          >
            <PersonIcon
              style={{
                height: "24px",
                color: activeTab === "artists" ? "#966300" : "#aaa", // color change on active
              }}
            />
          </TabIconWrapper>
          <TabLabel isActive={activeTab === "artists"}>Artists</TabLabel>
        </Tab>

        {/* Songs tab button */}
        <Tab onClick={() => handleTabClick("songs")}>
          {activeTab === "songs" && <CurvedBackground activeTab={"songs"} />}
          {activeTab === "songs" ? (
            <TabIconWrapper isActive={activeTab === "songs"} activeTab="songs">
              <img
                src="/assets/songs-color.svg"
                alt="Songs Icon"
                style={{ height: "24px" }}
              />
            </TabIconWrapper>
          ) : (
            <TabIconWrapper isActive={false} activeTab="songs">
              <img
                src="/assets/songs.svg"
                alt="Songs Icon"
                style={{ height: "24px" }}
              />
            </TabIconWrapper>
          )}
          <TabLabel isActive={activeTab === "songs"}>Songs</TabLabel>
        </Tab>
      </BottomTabBar>
      <Box
        sx={{
          position: "fixed",
          bottom: "0px",
          left: "10px",
          right: "10px",
          backgroundColor: "#121210",
          display: "flex",
          justifyContent: "space-around",
          alignItems: "center",
          padding: "8px",
          zIndex: 5,
        }}
      ></Box>
    </>
  );
};

export default TabNavigation;
