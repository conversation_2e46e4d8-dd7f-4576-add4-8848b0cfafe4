import React from "react";
import {
    Typo<PERSON>,
    Button,
} from "@mui/material";
import imageCompression from "browser-image-compression";

const ImageUpload = ({ songData, setSongData }) => {
    const convertToBase64 = (file) =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = (err) => reject(err);
        });

    const handleImageUpload = async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        try {
            // Compress the image
            const compressedFile = await imageCompression(file, {
                maxSizeMB: 0.3, // Max size in MB
                maxWidthOrHeight: 600, // Resize the image
                useWebWorker: true,
            });

            // Convert to base64 after compression
            const base64 = await convertToBase64(compressedFile);
            setSongData((prev) => ({ ...prev, image: base64 }));
        } catch (error) {
            console.error("Image compression failed:", error);
        }
    };

    return (
        <>
            <Typography variant="subtitle1">Upload Song Image</Typography>
            <Button variant="outlined" component="label">
                Upload Image
                <input
                    type="file"
                    accept="image/*"
                    hidden
                    onChange={handleImageUpload}
                />
            </Button>
            {songData.image && (
                <img
                    src={songData.image}
                    alt="Preview"
                    style={{ width: "100%", borderRadius: 8 }}
                />
            )}
        </>
    );
}

export default ImageUpload;
