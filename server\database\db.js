// SQLite connection setup

const sqlite3 = require("sqlite3").verbose();
const bcrypt = require("bcryptjs");
const path = require("path");

// Connect to the SQLite database file
const dbPath = path.join(__dirname, "app.db");
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("Database connection failed:", err.message);
  } else {
    console.log("Connected to the SQLite database.");
  }
});

db.serialize(() => {
  // Enforce foreign keys
  db.run("PRAGMA foreign_keys = ON");

  // Apply schema from schema.sql (idempotent)
  try {
    const fs = require("fs");
    const schemaPath = path.join(__dirname, "schema.sql");
    if (fs.existsSync(schemaPath)) {
      const schemaSql = fs.readFileSync(schemaPath, "utf-8");
      const statements = schemaSql
        .split(/;\s*\n/)
        .map(s => s.trim())
        .filter(s => s.length > 0)
        .filter(s => !/sqlite_sequence/i.test(s)); // skip internal sqlite_sequence

      db.serialize(() => {
        for (const stmt of statements) {
          db.run(stmt, (err) => {
            if (err) {
              const msg = (err && err.message) || "";
              const ignorable =
                msg.includes("already exists") ||
                msg.includes("duplicate column name") ||
                msg.includes("no such table: sqlite_sequence") ||
                msg.includes("object name reserved for internal use");
              if (!ignorable) {
                console.error("Schema statement failed:", msg);
              }
            }
          });
        }
      });
      console.log("Schema ensured (schema.sql processed).");
    }
  } catch (schemaReadErr) {
    console.error("Failed to read schema.sql:", schemaReadErr.message);
  }

  // Create admin_users table if it doesn't exist
  db.run(`
    CREATE TABLE IF NOT EXISTS admin_users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE,
      password TEXT
    )
  `);

  // Seed default admin if not exists
  db.get(
    "SELECT * FROM admin_users WHERE username = ?",
    ["admin"],
    (err, row) => {
      if (err) {
        console.error("Error checking admin user:", err.message);
        return;
      }

      if (!row) {
        const hashedPassword = bcrypt.hashSync("admin123", 10);
        db.run(
          "INSERT INTO admin_users (username, password) VALUES (?, ?)",
          ["admin", hashedPassword],
          (err) => {
            if (err) {
              console.error("Error inserting default admin user:", err.message);
            } else {
              console.log("Default admin user created: admin / admin123");
            }
          }
        );
      } else {
        console.log("Admin user already exists");
      }
    }
  );
});

module.exports = db;
