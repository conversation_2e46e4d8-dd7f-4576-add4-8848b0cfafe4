// hooks/useSongs.js
import { useApi } from './useApi';

export const useSongs = (options = {}) => {
  return useApi(
    'songs',
    `${process.env.REACT_APP_API_URL}/api/songs`,
    {
      select: (data) => {
        return Array.from(
          new Map(
            data.map((song) => [
              song.songs_master_id,
              { ...song, id: song.songs_master_id },
            ])
          ).values()
        );
      },
      ...options,
    }
  );
};

export const usePopularSongs = (options = {}) => {
  return useApi(
    'popular-songs',
    `${process.env.REACT_APP_API_URL}/api/songs/popular`,
    options
  );
};

export const useSong = (id, options = {}) => {
  return useApi(
    ['song', id],
    `${process.env.REACT_APP_API_URL}/api/songs/${id}`,
    options
  );
};