import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import axios from 'axios';
import { getImageUrl } from './ImageHelper';
import SongSelector from './SongSelector';

const PlaylistFormModal = ({ 
  open, 
  onClose, 
  onSave, 
  editingPlaylist = null,
  availableSongs = [],
  playlistTypes = []
}) => {
  const [playlistData, setPlaylistData] = useState({
    title: '',
    image: '',
    description: '',
    playlist_type_lookup_id: '',
  });
  const [selectedSongs, setSelectedSongs] = useState([]);
  const [loading, setLoading] = useState(false);

  const isEditing = Boolean(editingPlaylist);

  useEffect(() => {
    if (editingPlaylist) {
      setPlaylistData({
        title: editingPlaylist.title || '',
        image: editingPlaylist.image || '',
        description: editingPlaylist.description || '',
        playlist_type_lookup_id: editingPlaylist.playlist_type_lookup_id || '',
      });
      
      // Load existing songs for editing
      fetchPlaylistSongs(editingPlaylist.playlist_master_id);
    } else {
      // Reset form for new playlist
      setPlaylistData({
        title: '',
        image: '',
        description: '',
        playlist_type_lookup_id: '',
      });
      setSelectedSongs([]);
    }
  }, [editingPlaylist, open]);

  const fetchPlaylistSongs = async (playlistId) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistId}/songs`
      );
      setSelectedSongs(response.data || []);
    } catch (error) {
      console.error('Error fetching playlist songs:', error);
      setSelectedSongs([]);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPlaylistData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPlaylistData(prev => ({ ...prev, image: file }));
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const formData = new FormData();
      formData.append('title', playlistData.title);
      formData.append('description', playlistData.description);
      formData.append('playlist_type_lookup_id', playlistData.playlist_type_lookup_id);
      formData.append('spotify_url', '');

      // Handle image upload
      if (playlistData.image instanceof File) {
        formData.append('image', playlistData.image);
      } else if (typeof playlistData.image === 'string' && playlistData.image !== '') {
        formData.append('image', playlistData.image);
      }

      let playlistId;

      if (isEditing) {
        // Update existing playlist
        const response = await axios.put(
          `${process.env.REACT_APP_API_URL}/api/playlist/${editingPlaylist.playlist_master_id}`,
          formData,
          { headers: { 'Content-Type': 'multipart/form-data' } }
        );
        playlistId = editingPlaylist.playlist_master_id;
      } else {
        // Create new playlist
        const response = await axios.post(
          `${process.env.REACT_APP_API_URL}/api/playlist`,
          formData,
          { headers: { 'Content-Type': 'multipart/form-data' } }
        );
        playlistId = response.data.playlist_master_id;
      }

      // Handle song associations
      if (selectedSongs.length > 0) {
        // Clear existing songs for editing
        if (isEditing) {
          await axios.delete(
            `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistId}`
          );
        }

        // Add selected songs
        await Promise.all(
          selectedSongs.map(song =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/playlist-songs`, {
              playlist_master_id: playlistId,
              songs_master_id: song.songs_master_id,
            })
          )
        );
      }

      onSave();
      handleClose();
    } catch (error) {
      console.error('Error saving playlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setPlaylistData({
      title: '',
      image: '',
      description: '',
      playlist_type_lookup_id: '',
    });
    setSelectedSongs([]);
    setLoading(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEditing ? 'Edit Playlist' : 'Create New Playlist'}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Playlist Title"
            name="title"
            value={playlistData.title}
            onChange={handleInputChange}
            fullWidth
            required
          />

          <Typography variant="subtitle1">Upload Playlist Image</Typography>
          <Button variant="outlined" component="label">
            Upload Image
            <input
              type="file"
              accept="image/*"
              hidden
              onChange={handleImageChange}
            />
          </Button>

          {playlistData.image && (
            <Box mt={2}>
              <img
                src={getImageUrl(playlistData.image)}
                alt="Preview"
                style={{ width: '100%', maxWidth: 200, borderRadius: 8 }}
              />
            </Box>
          )}

          <FormControl fullWidth>
            <InputLabel>Playlist Type</InputLabel>
            <Select
              name="playlist_type_lookup_id"
              value={playlistData.playlist_type_lookup_id}
              onChange={handleInputChange}
              label="Playlist Type"
            >
              {playlistTypes.map(type => (
                <MenuItem 
                  key={type.playlist_type_lookup_id} 
                  value={type.playlist_type_lookup_id}
                >
                  {type.playlist_type_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            label="Description"
            name="description"
            value={playlistData.description}
            onChange={handleInputChange}
            fullWidth
            multiline
            minRows={3}
          />

          <SongSelector
            availableSongs={availableSongs}
            selectedSongs={selectedSongs}
            onSongsChange={setSelectedSongs}
          />
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || !playlistData.title.trim()}
        >
          {loading ? 'Saving...' : (isEditing ? 'Update' : 'Create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PlaylistFormModal;
