const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const PlaylistSongsModel = {
  create: ({ playlist_master_id, songs_master_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT OR IGNORE INTO playlist_songs (playlist_master_id, songs_master_id)
        VALUES (?, ?)
      `;
      db.run(query, [playlist_master_id, songs_master_id], function (err) {
        if (err) return reject(err);
        resolve({ id: this.lastID });
      });
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM playlist_songs`, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM playlist_songs WHERE playlist_songs_id = ?`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, { playlist_master_id, songs_master_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE playlist_songs
        SET playlist_master_id = ?, songs_master_id = ?
        WHERE playlist_songs_id = ?
      `;
      db.run(query, [playlist_master_id, songs_master_id, id], function (err) {
        if (err) return reject(err);
        resolve({ updated: this.changes });
      });
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM playlist_songs WHERE playlist_songs_id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  deleteByPlaylistAndSong: (playlist_master_id, songs_master_id) => {
    return new Promise((resolve, reject) => {
      const query = `
        DELETE FROM playlist_songs 
        WHERE playlist_master_id = ? AND songs_master_id = ?
      `;
      db.run(query, [playlist_master_id, songs_master_id], function (err) {
        if (err) return reject(err);
        resolve({ deleted: this.changes });
      });
    });
  },

  getSongsByPlaylistId: (playlist_master_id) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT sm.*
      FROM playlist_songs ps
      JOIN songs_master sm ON sm.songs_master_id = ps.songs_master_id
      WHERE ps.playlist_master_id = ?
    `;
      db.all(query, [playlist_master_id], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getPlaylistsBySongId: (songs_master_id) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT pm.*
      FROM playlist_songs ps
      JOIN playlist_master pm ON pm.playlist_master_id = ps.playlist_master_id
      WHERE ps.songs_master_id = ?
    `;
      db.all(query, [songs_master_id], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },
};

module.exports = PlaylistSongsModel;
