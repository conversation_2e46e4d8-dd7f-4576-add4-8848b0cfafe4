import { useState } from "react";
import DedicationModal from "./DedicationModal2";
import { useSongCart } from "../../context/SongCartContext";
import ThankYouDialog from "./ThankDialog";
import SuccessModal from "./SuccessModal";
import { getDeviceId } from "../../utils/cookieUtils";
import { useSnackbar } from "notistack";

const RequestHandler = ({
  selectedSong,
  setSelectedSong,
  openRequestModal,
  setOpenRequestModal,
  onRequestSuccess,
  step,
  setStep,
}) => {
  const { cart, addToCart } = useSongCart();
  const [dedication, setDedication] = useState("");
  const [actionType, setActionType] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [userName, setUserName] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [requesterName, setRequesterName] = useState("");
  const { enqueueSnackbar } = useSnackbar();

  const handleRequestClose = () => {
    setOpenRequestModal(false);
    clearForm();
  };

  const handleDedicationChange = (event) => {
    setDedication(event.target.value);
  };

  const handleMakeRequest = (enteredUserName) => {
    setUserName(enteredUserName);

    if (cart.length === 0) {
      enqueueSnackbar("Please queue a song to send request.", {
        variant: "error",
      });
      return;
    }

    setOpenDialog(false);
    handleSendRequest(enteredUserName === "Anonymous");
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSendRequest = async (forceAnonymous = false) => {
    const deviceId = getDeviceId();

    const finalUserName = forceAnonymous && !userName ? "Anonymous" : userName;
    setRequesterName(finalUserName);

    try {
      // Check for existing active requests for each song
      const canRequestResults = await Promise.all(
        cart.map((song) =>
          fetch(
            `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${song.songs_master_id}`
          ).then((res) => res.json())
        )
      );

      const hasActiveRequests = canRequestResults.some(
        (result) => result.hasActiveRequest
      );

      if (hasActiveRequests) {
        return;
      }

      const requestBody = {
        requester_name: finalUserName,
        device_id: deviceId,
        songs: cart.map((item) => ({
          songs_master_id: item.songs_master_id,
          dedication_msg: item.dedication || "",
          action_type: item.actionType,
        })),
      };

      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      await response.json();
      setShowSuccessModal(true);
      setOpenDialog(false);

      if (onRequestSuccess) {
        onRequestSuccess();
      }
    } catch (error) {
      console.error("Failed to make request:", error);
    }
  };

  const handleRequestAction = (selectedActionType, selectedSong) => {
    setActionType(selectedActionType);
    setSelectedSong(selectedSong);
    setOpenRequestModal(true);

    if (selectedActionType === "singForMe") {
      // Will move to dedication input step
      setStep(2);
    } else {
      const isDuplicate = cart.some(
        (item) => item.songs_master_id === selectedSong.songs_master_id
      );

      if (isDuplicate) {
        setStep(3); // Just go to cart if already added
        return;
      }

      const songToAdd = {
        ...selectedSong,
        actionType: selectedActionType,
        dedication: "", // Ensure consistency with rest of logic
      };
      addToCart(songToAdd);
      setStep(3);
    }
  };

  const handleAddMoreSongs = () => {
    setOpenRequestModal(false);
  };

  const handleBack = (step) => {
    if (step === 1) {
      setOpenRequestModal(true);
    } else if (step === 2) {
      setStep(1);
    } else if (step === 3) {
      if (cart.some((item) => item.actionType === "singForMe")) {
        // If any song has dedication, go to step 2
        setStep(2);
      } else {
        // Otherwise go directly to step 1
        setStep(1);
      }
    } else if (step === 4) {
      setStep(3);
    }
  };

  const navigateToRequestModal = () => {
    setStep(1);
    setOpenRequestModal(true);
  };

  const clearForm = () => {
    setDedication("");
    setActionType("");
    setUserName("");
    setSelectedSong({});
  };

  return (
    <>
      <DedicationModal
        open={openRequestModal}
        onClose={handleRequestClose}
        song={selectedSong}
        dedication={dedication}
        onChange={handleDedicationChange}
        onMakeRequest={handleMakeRequest}
        onAddMoreSongs={handleAddMoreSongs}
        actionType={actionType}
        setActionType={setActionType}
        userName={userName}
        setUserName={setUserName}
        navigateToRequestModal={navigateToRequestModal}
        step={step}
        handleBack={handleBack}
        handleAction={handleRequestAction}
        setStep={setStep}
      />
      <ThankYouDialog open={openDialog} onClose={handleCloseDialog} />
      <SuccessModal
        open={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          clearForm();
          setOpenDialog(true);
        }}
        requesterName={requesterName}
      />
    </>
  );
};

export default RequestHandler;
