const nodemailer = require("nodemailer");


const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: false, // use TLS
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

/**
 * Sends a thank-you email to the user after registration
 * @param {string} toEmail - Recipient email
 * @param {string} userName - Name of the registered user
 */
const sendThankYouEmail = async (toEmail, userName) => {
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; padding: 20px;">
      <h2 style="color: #4CAF50;">Thank You for Registering!</h2>
      <p>Hi <strong>${userName}</strong>,</p>
      <p>We’re excited to have you join our event. Your registration has been successfully recorded.</p>
      <p>Stay tuned for updates and event details.</p>
      <br/>
      <p>Best regards,<br/>The Showdown Team</p>
    </div>
  `;

  try {
    await transporter.sendMail({
      from: process.env.FROM_EMAIL,
      to: toEmail,
      subject: "Thank You for Registering!",
      html: htmlContent,
    });
    console.log(`📧 Email sent to ${toEmail}`);
  } catch (error) {
    console.error("❌ Error sending email:", error);
  }
};

module.exports = sendThankYouEmail