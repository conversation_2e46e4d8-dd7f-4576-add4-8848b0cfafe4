import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>Field,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Chip,
} from '@mui/material';

const SongSelector = ({ availableSongs = [], selectedSongs = [], onSongsChange }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSongs = useMemo(() => {
    if (!searchTerm.trim()) return availableSongs;
    return availableSongs.filter(song =>
      song.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      song.artist.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, availableSongs]);

  const handleAddSong = (song) => {
    const isAlreadySelected = selectedSongs.some(
      s => s.songs_master_id === song.songs_master_id
    );
    
    if (!isAlreadySelected) {
      onSongsChange([...selectedSongs, song]);
    }
  };

  const handleRemoveSong = (song) => {
    onSongsChange(
      selectedSongs.filter(s => s.songs_master_id !== song.songs_master_id)
    );
  };

  const isSelected = (song) => {
    return selectedSongs.some(s => s.songs_master_id === song.songs_master_id);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Select Songs
      </Typography>

      {/* Selected Songs Display */}
      {selectedSongs.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Selected Songs ({selectedSongs.length})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedSongs.map(song => (
              <Chip
                key={song.songs_master_id}
                label={`${song.name} - ${song.artist}`}
                onDelete={() => handleRemoveSong(song)}
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Search Bar */}
      <TextField
        label="Search Songs"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        fullWidth
        margin="normal"
        placeholder="Search by song name or artist..."
      />

      {/* Available Songs List */}
      <Box
        sx={{
          maxHeight: 300,
          overflowY: 'auto',
          border: '1px solid #ddd',
          borderRadius: 1,
          mt: 1,
        }}
      >
        {filteredSongs.length > 0 ? (
          filteredSongs.map(song => {
            const songIsSelected = isSelected(song);
            return (
              <Box
                key={song.songs_master_id}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 1,
                  borderBottom: '1px solid #eee',
                  backgroundColor: songIsSelected ? '#4a4a4aeb' : 'transparent',
                  '&:hover': {
                    backgroundColor:   '#4b4b4bff' ,
                  },
                }}
              >
                <Box>
                  <Typography variant="body1" fontWeight={songIsSelected ? 'bold' : 'normal'}>
                    {song.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {song.artist} • {song.duration}
                  </Typography>
                </Box>
                <Button
                  size="small"
                  variant={songIsSelected ? "outlined" : "contained"}
                  color={songIsSelected ? "error" : "primary"}
                  onClick={() =>
                    songIsSelected ? handleRemoveSong(song) : handleAddSong(song)
                  }
                  disabled={false}
                >
                  {songIsSelected ? 'Remove' : 'Add'}
                </Button>
              </Box>
            );
          })
        ) : (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              {searchTerm ? 'No songs found matching your search.' : 'No songs available.'}
            </Typography>
          </Box>
        )}
      </Box>

      {searchTerm && (
        <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
          Showing {filteredSongs.length} of {availableSongs.length} songs
        </Typography>
      )}
    </Box>
  );
};

export default SongSelector;
