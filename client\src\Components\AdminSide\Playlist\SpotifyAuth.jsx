import React, { useState, useEffect } from 'react';
import { Button, Box, Typography, Avatar } from '@mui/material';
import axios from 'axios';

const SpotifyAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchUserDetails();
  }, []);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/spotify/user-details`,
        { withCredentials: true }
      );
      
      setUser(response.data);
    } catch (error) {
      console.log('User not authenticated');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = () => {
    window.location.href = `${process.env.REACT_APP_API_URL}/login`;
  };

  const handleLogout = async () => {
    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL}/api/logout`,
        {},
        { withCredentials: true }
      );
      setUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  if (loading) {
    return (
      <Button variant="outlined" disabled>
        Loading...
      </Button>
    );
  }

  if (user) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {user.images && user.images.length > 0 && (
            <Avatar 
              src={user.images[0].url} 
              alt={user.display_name}
              sx={{ width: 32, height: 32 }}
            />
          )}
          <Typography variant="body2" color="white">
            {user.display_name}
          </Typography>
        </Box>
        <Button 
          variant="outlined" 
          color="secondary" 
          size="small"
          onClick={handleLogout}
        >
          Logout
        </Button>
      </Box>
    );
  }

  return (
    <Button 
      variant="contained" 
      color="primary" 
      onClick={handleLogin}
    >
      Login with Spotify
    </Button>
  );
};

export default SpotifyAuth;