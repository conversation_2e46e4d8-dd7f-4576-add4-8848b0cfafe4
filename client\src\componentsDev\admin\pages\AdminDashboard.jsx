import React from 'react';
import { Container, Box, useTheme, useMediaQuery } from '@mui/material';
import DashboardCard from '../components/DashboardCard';
// import { useAdminDashboardStats } from '../../hooks/useAdminStats';

const AdminDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // const { data: stats, isLoading, isError } = useAdminDashboardStats();

  return (
    <>
      <Container maxWidth="lg" sx={{ mt: 10, mb: 3 }}>
        <Box display="flex" flexDirection={isMobile ? 'column' : 'row'} gap={2}>
          <DashboardCard to="/admin/requests" imageSrc="/pull_req.png" title="Request Compact" />
          {/* <DashboardCard to="/admin/requests-old" imageSrc="/req.png" title="Request List" /> */}
          <DashboardCard to="/admin/playlists" imageSrc="/logo.webp" title="Playlist" />
          <DashboardCard to="/admin/songs" imageSrc="/songsLogo.jpg" title="Songs" />
          <DashboardCard to="/admin/lookups" imageSrc="/lookupsLogo.jpg" title="Lookups" />
          <DashboardCard to="/admin/imported-songs" imageSrc="/importLogo.jpg" title="Import Songs" />
          <DashboardCard to="/admin/quiz" imageSrc="/quizLogo.jpg" title="Quiz" />
          {/* <DashboardCard to="/admin/showdown" imageSrc="/req.png" title="Showdown" /> */}
        </Box>
      </Container>
    </>
  );
};

export default AdminDashboard;