const lookupModel = require('../models/lookupModel');

const create = (type) => async (req, res) => {
  try {
    const result = await lookupModel.create(type, req.body);
    res.status(201).json({ message: `${type} created successfully`, data: result });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

const getAll = (type) => async (req, res) => {
  try {
    const rows = await lookupModel.getAll(type);
    res.json(rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

const getById = (type) => async (req, res) => {
  try {
    const row = await lookupModel.getById(type, req.params.id);
    if (!row) return res.status(404).json({ error: `${type} not found` });
    res.json(row);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

const update = (type) => async (req, res) => {
  try {
    const result = await lookupModel.update(type, req.params.id, req.body);
    res.json({ message: `${type} updated successfully`, data: result });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

const remove = (type) => async (req, res) => {
  try {
    const result = await lookupModel.delete(type, req.params.id);
    res.json({ message: `${type} deleted successfully`, data: result });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

module.exports = {
  create,
  getAll,
  getById,
  update,
  remove,
};
