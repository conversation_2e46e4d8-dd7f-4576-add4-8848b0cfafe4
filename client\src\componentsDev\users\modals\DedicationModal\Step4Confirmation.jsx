// components/dedication/Step4Confirmation.jsx
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  List,
} from "@mui/material";
import { useSongCart } from "../../../../context/SongCartContext";
import CartItem from "./CartItem";

const Step4Confirmation = ({
  userName,
  setUserName,
  isAnonymous,
  setIsAnonymous,
  onConfirm,
  onBack,
}) => {
  const { cart } = useSongCart();

  const handleBeAnonymous = () => {
    setIsAnonymous(true);
    setUserName("Anonymous");
  };

  const handleUserNameChange = (e) => {
    const value = e.target.value;
    setUserName(value);
    if (value !== "Anonymous") {
      setIsAnonymous(false);
    }
  };

  const handleConfirm = () => {
    onConfirm(userName);
  };

  return (
    <>
      <Box>
        <TextField
          autoFocus
          margin="dense"
          id="user-name"
          placeholder="Type name"
          type="text"
          fullWidth
          variant="outlined"
          value={userName}
          onChange={handleUserNameChange}
          disabled={isAnonymous}
          sx={{
            "& label.Mui-focused": { color: "white" },
            "& .MuiOutlinedInput-root": {
              "&.Mui-focused fieldset": { borderColor: "#976609" },
              color: "#FFFFFF",
              "& input": {
                color: "#FFFFFF",
              },
              "&.Mui-disabled": {
                "& input": {
                  color: "#aaaaaa",
                },
              },
            },
          }}
        />
        
        <Typography sx={{ fontSize: "12px", color: "#545454", mt: 2, mb: 2 }}>
          If you want to be Anonymous, click on{" "}
          <Box
            component="span"
            onClick={isAnonymous ? undefined : handleBeAnonymous}
            sx={{
              fontWeight: "bold",
              textDecoration: "underline",
              color: isAnonymous ? "#aaa" : "#976609",
              cursor: isAnonymous ? "not-allowed" : "pointer",
            }}
          >
            Be Anonymous
          </Box>
        </Typography>
      </Box>
      
      <List sx={{ width: "100%", overflow: "auto", mb: 2, maxHeight: "250px" }}>
        {cart.map((item) => (
          <CartItem
            key={item.songs_master_id}
            item={item}
            showAction={true}
            showDedication={true}
          />
        ))}
      </List>
      
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="outlined"
          onClick={onBack}
          sx={{
            mt: 2,
            color: "#976609",
            borderColor: "#976609",
            "&:hover": {
              borderColor: "#b7830e",
            },
          }}
        >
          Back
        </Button>
        
        <Button
          variant="contained"
          onClick={handleConfirm}
          sx={{
            mt: 2,
            backgroundColor: "#976609",
            color: "#ffff",
            "&:hover": {
              backgroundColor: "#b7830e",
            },
          }}
        >
          Send Request
        </Button>
      </Box>
    </>
  );
};

export default Step4Confirmation;