import React, { useState } from "react";
import {
    Grid,
    Container,
    Box,
    CircularProgress,
    Button,
    Typography,
} from "@mui/material";
import { useQueryClient } from '@tanstack/react-query';

import SongCard from "../../common/SongCard";
import { useSongCart } from "../../../../context/SongCartContext";
import { useActiveRequests } from "../../../hooks/useActiveRequests";
import { usePopularSongs } from "../../../hooks/useSongs";
import DedicationMaster from "../../modals/DedicationModal/DedicationMaster";
import SuccessModal from "../../modals/SuccessModal";
import ThankYouDialog from "../../modals/ThankDialog";
import { useSnackbar } from "notistack";
import { getDeviceId } from "../../../../utils/cookieUtils"; 

const INITIAL_SONGS_COUNT = 20;
const SONGS_PER_PAGE = 10;

const PopularSongList = () => {
    const [visibleCount, setVisibleCount] = useState(INITIAL_SONGS_COUNT);
    const { cart, clearCart } = useSongCart();
    const [selectedSong, setSelectedSong] = useState(null);
    const [showDedicationModal, setShowDedicationModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showThankYouDialog, setShowThankYouDialog] = useState(false);
    const [requesterName, setRequesterName] = useState("");
    const [loadingMore, setLoadingMore] = useState(false); 
    const { enqueueSnackbar } = useSnackbar();
    const queryClient = useQueryClient();

    // React Query hooks
    const {
        data: songs = [],
        isLoading: songsLoading,
        error: songsError,
    } = usePopularSongs();

    const {
        data: activeRequests = {},
        isLoading: activeRequestsLoading,
        error: activeRequestsError,
    } = useActiveRequests();

    // Calculate visible songs
    const visibleSongs = songs.slice(0, visibleCount);
    const hasMore = visibleSongs.length < songs.length;

    const handleLoadMore = () => {
        setLoadingMore(true);
        setTimeout(() => {
            const newVisibleCount = visibleCount + SONGS_PER_PAGE;
            setVisibleCount(newVisibleCount);
            setLoadingMore(false);
        }, 500);
    };

    const handleRequestSuccess = () => {
        // Invalidate and refetch active requests
        queryClient.invalidateQueries({ queryKey: ['activeRequests'] });
    };


    const handleRequestOpen = (song) => {
        setSelectedSong(song);
        setShowDedicationModal(true);
    };

    const handleSubmitRequest = async (dedicationData) => {
        try {
            const deviceId = getDeviceId();
            const { userName, songs } = dedicationData;

            // Validate the data structure
            if (!songs || !Array.isArray(songs) || songs.length === 0) {
                console.error("Invalid songs data received:", dedicationData);
                enqueueSnackbar("Invalid request data. Please try again.", {
                    variant: "error",
                });
                return;
            }

            const finalUserName = userName || "Anonymous";
            setRequesterName(finalUserName);

            // Check for existing active requests for all songs
            for (const song of songs) {
                const canRequestResponse = await fetch(
                    `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${song.songs_master_id}`
                );

                const canRequestData = await canRequestResponse.json();

                if (canRequestData.hasActiveRequest) {
                    enqueueSnackbar(`You already have an active request for song ID ${song.songs_master_id}`, {
                        variant: "warning",
                    });
                    return;
                }
            }

            // Submit the request for all songs
            const requestBody = {
                requester_name: finalUserName,
                device_id: deviceId,
                songs: songs.map(song => ({
                    songs_master_id: song.songs_master_id,
                    dedication_msg: song.dedication || "",
                    action_type: song.actionType,
                })),
            };

            console.log('Request body:', requestBody);
            

            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(requestBody),
                }
            );

            if (!response.ok) {
                throw new Error("Failed to submit request");
            }

            await response.json();
            setShowDedicationModal(false);
            setShowSuccessModal(true);
            handleRequestSuccess();

        } catch (error) {
            console.error("Failed to make request:", error);
            enqueueSnackbar("Failed to submit request. Please try again.", {
                variant: "error",
            });
        }
    };

    const handleSuccessModalClose = () => {
        setShowSuccessModal(false);
        setShowThankYouDialog(true);
    };

    const handleThankYouClose = () => {
        setShowThankYouDialog(false);
        clearCart();
    };


    // Loading state
    if (songsLoading || activeRequestsLoading) {
        return (
            <Container maxWidth="xl" sx={{ mt: 0, bgcolor: "background.default", py: 2, px: 3 }}>
                <Box
                    sx={{
                        height: "60vh",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <CircularProgress />
                </Box>
            </Container>
        );
    }

    // Error state
    if (songsError || activeRequestsError) {
        return (
            <Container maxWidth="xl" sx={{ mt: 0, bgcolor: "background.default", py: 2, px: 3 }}>
                <Box
                    sx={{
                        height: "60vh",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Typography variant="h6" color="error">
                        Error loading popular songs. Please try again.
                    </Typography>
                </Box>
            </Container>
        );
    }

    return (
        <>
            <Container
                maxWidth="xl"
                sx={{
                    mt: 0,
                    bgcolor: "background.default",
                    py: 2,
                    px: 3,
                }}
            >
                {songs.length === 0 ? (
                    <Box
                        sx={{
                            height: "60vh",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <Typography variant="h6" color="textSecondary">
                            No popular songs found.
                        </Typography>
                    </Box>
                ) : (
                    <>
                        <Grid container spacing={2}>
                            {visibleSongs.map((song, index) => (
                                <Grid
                                    item
                                    xs={12}
                                    key={song.songs_master_id || `temp-id-${index}`}
                                    sx={{
                                        width: "100%",
                                        paddingLeft: "0px !important",
                                        paddingTop: "0px !important",
                                    }}
                                >
                                    <SongCard
                                        song={song}
                                        onRequestOpen={handleRequestOpen}
                                        activeRequests={activeRequests}
                                        cart={cart}
                                    />
                                </Grid>
                            ))}
                        </Grid>

                        {hasMore && (
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    mt: 3,
                                }}
                            >
                                <Button
                                    variant="contained"
                                    onClick={handleLoadMore}
                                    sx={{
                                        background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                                        color: "#fff",
                                        fontWeight: "bold",
                                        fontSize: "1rem",
                                        px: 4,
                                        py: 1,
                                        borderRadius: "2rem",
                                        boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
                                        transition: "all 0.3s ease-in-out",
                                        "&:hover": {
                                            background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                                            transform: "scale(1.05)",
                                        },
                                    }}
                                    disabled={loadingMore}
                                >
                                    {loadingMore ? (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: 1,
                                            }}
                                        >
                                            <CircularProgress
                                                size={20}
                                                thickness={5}
                                                color="inherit"
                                            />
                                            <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                                                Loading...
                                            </Typography>
                                        </Box>
                                    ) : (
                                        "Show More Songs"
                                    )}
                                </Button>
                            </Box>
                        )}
                    </>
                )}
            </Container>

            {/* Use your existing modal components directly */}
            <DedicationMaster
                open={showDedicationModal}
                onClose={() => setShowDedicationModal(false)}
                song={selectedSong}
                onSubmit={handleSubmitRequest}
            />

            <SuccessModal
                open={showSuccessModal}
                onClose={handleSuccessModalClose}
                requesterName={requesterName}
            />

            <ThankYouDialog
                open={showThankYouDialog}
                onClose={handleThankYouClose}
            />

        </>
    );
};

export default PopularSongList;