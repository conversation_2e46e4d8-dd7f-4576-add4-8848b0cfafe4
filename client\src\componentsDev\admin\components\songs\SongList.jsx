import React from "react";
import {
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";

const SongList = ({ songs, onEdit, onDelete }) => {
  return (
    <>
      {songs.map((song) => (
        <Box
          key={song.songs_master_id}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <img
              src={song.image || "https://placehold.co/50x50"}
              alt="cover"
              style={{ width: 50, height: 50, borderRadius: 8 }}
            />
            <Box>
              <Typography variant="h6">{song.name}</Typography>
              <Typography variant="subtitle2">{song.artist}</Typography>
            </Box>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <IconButton onClick={() => onEdit(song.songs_master_id)}>
              <EditIcon />
            </IconButton>
            <IconButton onClick={() => onDelete(song.songs_master_id)}>
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>
      ))}
    </>
  );
};

export default SongList;