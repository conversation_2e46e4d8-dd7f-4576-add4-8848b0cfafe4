// components/dedication/SongHeader.jsx
import { Box, Typography, Avatar } from "@mui/material";

const SongHeader = ({ song }) => {
  
  if (!song) return null;


  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        marginBottom: "24px",
      }}
    >
      <Avatar
        src={song.image}
        alt={song.name}
        sx={{
          width: 40,
          height: 40,
          marginRight: "12px",
          border: "2px solid #f59e0b",
        }}
      />
      <Box>
        <Typography
          variant="body1"
          sx={{
            fontWeight: "600",
            fontSize: "16px",
            lineHeight: "16px",
            letterSpacing: "0.2px",
            color: "#966300",
          }}
        >
          {song.name}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontWeight: "400",
            fontSize: "13px",
            lineHeight: "22px",
            letterSpacing: "-0.41px",
            color: "#757575",
            mt: 0.5,
          }}
        >
          by {song.artist}
        </Typography>
      </Box>
    </Box>
  );
};

export default SongHeader;