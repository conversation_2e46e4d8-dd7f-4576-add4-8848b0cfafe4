import { <PERSON>nackbar, Alert, List, ListItem, ListItemText, ListItemAvatar, Avatar } from '@mui/material'; // Add ListItemAvatar and Avatar to imports
import { useSongCart } from '../../context/SongCartContext';

function SuccessMessage({ open, message, onClose}) {
  const { cart } = useSongCart();
  return (
    <Snackbar open={open} autoHideDuration={6000} onClose={onClose}>
      <Alert onClose={onClose} severity="success" sx={{ width: '100%' }}>
        {message}
        <List dense>
          {cart.map((song, index) => (
            <ListItem key={index}>
              <ListItemAvatar>
                <Avatar alt="logo" src={song.image} />
              </ListItemAvatar>
              <ListItemText primary={song.name} secondary={`by ${song.artist}`} />
            </ListItem>
          ))}
        </List>
      </Alert>
    </Snackbar>
  );
}

export default SuccessMessage;