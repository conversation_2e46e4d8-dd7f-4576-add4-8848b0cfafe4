{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.15.21", "@tanstack/react-query": "^5.85.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.542.0", "notistack": "^3.0.1", "react": "^18.2.0", "react-bootstrap": "^2.10.2", "react-dom": "^18.2.0", "react-material-ui-carousel": "^3.4.2", "react-router-dom": "^6.23.0", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}