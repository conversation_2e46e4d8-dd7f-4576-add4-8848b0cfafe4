import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AdminLayout from './AdminLayout';
import AdminProtectedRoute from '../auth/AdminProtectedRoute';

// Import admin pages
import AdminDashboard from '../pages/AdminDashboard';
import AdminRequests from '../pages/AdminRequests';
import AdminSongs from '../pages/AdminSongs';
import AdminPlaylists from '../pages/AdminPlaylists'; 
import AdminQuiz from '../pages/AdminQuiz';
// import AdminShowdown from '../pages/AdminShowdown';
import AdminImportedSongs from '../pages/AdminImportedSongs';
import AdminLookups from '../pages/AdminLookups';
import { QuizDashboard } from '../components/quiz';
// import QuizIntegrationExample from '../components/quiz/QuizIntegrationExample';

const AdminMaster = () => {
  return (
    <AdminProtectedRoute>
      <AdminLayout>
        <Routes>
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="/dashboard" element={<AdminDashboard />} />
          <Route path="/requests" element={<AdminRequests />} />
          <Route path="/songs" element={<AdminSongs />} />
          <Route path="/playlists" element={<AdminPlaylists />} />
          <Route path="/lookups" element={<AdminLookups />} />
          {/* <Route path="/quiz" element={<AdminQuiz />} /> */}
          <Route path="/quiz" element={<QuizDashboard />} />
          {/* <Route path="/quizeg" element={<QuizIntegrationExample />} /> */}
          {/* <Route path="/showdown" element={<AdminShowdown />} /> */}
          <Route path="/imported-songs" element={<AdminImportedSongs />} />
          
          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
      </AdminLayout>
    </AdminProtectedRoute>
  );
};

export default AdminMaster;
