// components/dedication/CartItem.jsx
import { Box, Typography, Avatar, IconButton } from "@mui/material";
import { getAction } from "../../../../lib/utils";

const CartItem = ({ item, onRemove, showAction = false, showDedication = false }) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        my: 1,
        mr: 1,
        p: 1,
        border: "1px solid #333",
        borderRadius: "4px",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
          <Avatar
            src={item.image}
            alt={item.name}
            sx={{
              width: 40,
              height: 40,
              marginRight: "12px",
              border: "2px solid #f59e0b",
            }}
          />
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="body1"
              sx={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#966300",
              }}
            >
              {item.name}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                fontWeight: "400",
                fontSize: "13px",
                color: "#757575",
                mt: 0.5,
              }}
            >
              by {item.artist}
            </Typography>
          </Box>
        </Box>
        
        {onRemove && (
          <IconButton
            edge="end"
            aria-label="delete"
            onClick={() => onRemove(item.songs_master_id)}
            sx={{ marginLeft: "8px" }}
          >
            <img
              src="/assets/cross-red.svg"
              alt="Cross Icon"
              style={{ height: "20px" }}
            />
          </IconButton>
        )}
      </Box>
      
      {(showAction || showDedication) && (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1, mt: 1 }}>
          {showAction && item.actionType && (
            <Typography
              variant="body2"
              sx={{ fontWeight: "600", fontSize: "14px" }}
            >
              <Box component="span" sx={{ color: "#966300", marginRight: "5px" }}>
                Action:
              </Box>
              {getAction(item.actionType)}
            </Typography>
          )}
          
          {showDedication && item.dedication && (
            <Typography
              variant="body2"
              sx={{ fontWeight: "600", fontSize: "14px" }}
            >
              <Box component="span" sx={{ color: "#966300", marginRight: "5px" }}>
                Dedication:
              </Box>
              {item.dedication}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default CartItem;