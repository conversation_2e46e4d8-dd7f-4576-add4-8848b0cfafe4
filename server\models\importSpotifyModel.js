const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const ImportSpotifyModel = {
  // Add tracks for a specific import song
  addTracks: (import_song_id, tracks) => {
    return Promise.all(
      tracks.map(async (track) => {
        const {
          id,
          name,
          artist,
          album,
          release_year,
          duration,
          decade,
          image,
        } = track;

        return new Promise((resolve, reject) => {
          db.run(
            `
            INSERT INTO ImportSpotifyTrackTemp
            (import_song_id, spotify_track_id, name, artist, album, release_year, duration, decade, image, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            `,
            [
              import_song_id,
              id,
              name,
              artist,
              album,
              release_year,
              duration,
              decade,
              image,
            ],
            async function (err) {
              if (err) reject(err);
              else {
                try {
                  const song = await ImportSongModel.getById(import_song_id);
                  if (song && song.video_url) {
                    const karaokeTypeId = await getKaraokeUrlTypeId();
                    
                    // Handle multiple URLs stored as JSON array
                    let urls = [];
                    try {
                      urls = JSON.parse(song.video_url);
                      if (!Array.isArray(urls)) {
                        urls = [song.video_url]; // fallback for legacy single URL
                      }
                    } catch (e) {
                      urls = [song.video_url]; // fallback if not valid JSON
                    }
                    
                    // Add each URL separately
                    for (const url of urls) {
                      if (url && url.trim()) {
                        await ImportTrackUrlModel.addUrl(this.lastID, url.trim(), karaokeTypeId);
                      }
                    }
                  }
                } catch (e) {
                  console.error("Error attaching video_url as karaoke URL:", e);
                }
                resolve({ id: this.lastID });
              }
            }
          );
        });
      })
    );
  },

  // Add artists for a specific import song
  addArtists: (import_song_id, artists) => {
    return Promise.all(
      artists.map((artist) => {
        const { artist_id, artistName, image } = artist;
        return new Promise((resolve, reject) => {
          db.run(
            `
            INSERT INTO ImportSpotifyArtistTemp
            (import_song_id, artist_id, name, image, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))
            `,
            [import_song_id, artist_id, artistName, image],
            function (err) {
              if (err) reject(err);
              else resolve({ id: this.lastID });
            }
          );
        });
      })
    );
  },

  // Get all tracks for a specific import song
  getTracksByImportSongId: (import_song_id) => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM ImportSpotifyTrackTemp WHERE import_song_id = ? AND (is_migrated = 0 OR is_migrated IS NULL) ORDER BY created_at DESC`,
        [import_song_id],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  // Get all artists for a specific import song
  getArtistsByImportSongId: (import_song_id) => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM ImportSpotifyArtistTemp WHERE import_song_id = ? ORDER BY created_at DESC`,
        [import_song_id],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  // Delete all tracks for a specific import song
  deleteTracksByImportSongId: (import_song_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportSpotifyTrackTemp WHERE import_song_id = ?`,
        [import_song_id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },

  // Delete all artists for a specific import song
  deleteArtistsByImportSongId: (import_song_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportSpotifyArtistTemp WHERE import_song_id = ?`,
        [import_song_id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },

  // Delete a specific track by ID
  deleteTrack: (track_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportSpotifyTrackTemp WHERE id = ?`,
        [track_id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },

  // Delete a specific artist by ID
  deleteArtist: (artist_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportSpotifyArtistTemp WHERE id = ?`,
        [artist_id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },

  getAllTracks: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM ImportSpotifyTrackTemp WHERE is_migrated = 0 OR is_migrated IS NULL ORDER BY created_at DESC`,
        [],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  updateTrack: ({ id, name, artist, album, release_year, duration }) => {
    return new Promise((resolve, reject) => {
      db.run(
        `
      UPDATE ImportSpotifyTrackTemp
      SET name = ?, artist = ?, album = ?, release_year = ?, duration = ?
      WHERE id = ?
      `,
        [name, artist, album, release_year, duration, id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },
};

const ImportSongModel = require("./importSongModel");
const ImportTrackUrlModel = require("./importTrackUrlModel");

const getKaraokeUrlTypeId = () => {
  return new Promise((resolve, reject) => {
    db.get(
      "SELECT url_type_lookup_id FROM url_type_lookup WHERE LOWER(url_type_name) = 'karaoke'",
      [],
      (err, row) => {
        if (err) return reject(err);
        if (row && row.url_type_lookup_id) resolve(row.url_type_lookup_id);
        else reject(new Error("Karaoke url_type_lookup_id not found"));
      }
    );
  });
};

module.exports = ImportSpotifyModel;
