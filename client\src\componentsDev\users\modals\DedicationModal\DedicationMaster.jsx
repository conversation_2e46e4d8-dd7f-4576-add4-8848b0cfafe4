// components/dedication/DedicationMaster.jsx
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSongCart } from "../../../../context/SongCartContext";
import { getDeviceId } from "../../../../utils/cookieUtils";
import { useMode } from "../../../../utils/useMode";
import Step1ActionSelection from "./Step1ActionSelection";
import Step2DedicationForm from "./Step2DedicationForm";
import Step3CartReview from "./Step3CartReview";
import Step4Confirmation from "./Step4Confirmation";

const DedicationMaster = ({
  open,
  onClose,
  song,
  onSubmit,
}) => {
  const [step, setStep] = useState(1);
  const [dedication, setDedication] = useState("");
  const [actionType, setActionType] = useState("");
  const [userName, setUserName] = useState("");
  const [isAnonymous, setIsAnonymous] = useState(false);
  const { cart, addToCart, removeFromCart, updateCartItemDedication } = useSongCart();
  const requestMode = useMode();

  useEffect(() => {
    if (open) {
      setStep(1);
      // Retrieve the requester name from local storage
      const storedRequesterName = localStorage.getItem(
        `requesterName_${getDeviceId()}`
      );
      if (storedRequesterName) {
        setUserName(storedRequesterName);
      }
    }
  }, [open]);

  useEffect(() => {

    console.log("SongHeader rendered with song:", song);
  }, [song]);

  const handleActionSelection = (selectedAction) => {
    setActionType(selectedAction);

    if (selectedAction !== "singForMe") {
      // For non-dedication actions, add to cart immediately
      const isDuplicate = cart.some(
        (item) => item.songs_master_id === song.songs_master_id
      );

      if (isDuplicate) {
        updateCartItemDedication(song.songs_master_id, "");
        setStep(3);
      } else {
        const songToAdd = {
          ...song,
          actionType: selectedAction,
          dedication: "",
        };
        addToCart(songToAdd);
        setStep(3);
      }
    } else {
      // For dedication, go to step 2
      setStep(2);
    }
  };

  const handleDedicationSubmit = (dedicationText) => {
    setDedication(dedicationText);

    const isDuplicate = cart.some(
      (item) => item.songs_master_id === song.songs_master_id
    );

    if (isDuplicate) {
      updateCartItemDedication(song.songs_master_id, dedicationText);
    } else {
      const songToAdd = {
        ...song,
        actionType: actionType,
        dedication: dedicationText,
      };
      addToCart(songToAdd);
    }

    setStep(3);
  };

  const handleConfirmRequest = (finalUserName) => {
    console.log("Cart state:", cart);
    console.log("Cart length:", cart ? cart.length : 0);
    
    // Check if this is a single song request (like from SearchModal) or cart request
    if (cart && cart.length > 0) {
      // Cart-based request (like from PopularSongList, ArtistSongsPage, etc.)
      const requestData = {
        userName: finalUserName,
        songs: cart
      };

      console.log("Submitting cart request data:", requestData);
      onSubmit(requestData);
    } else {
      // Single song request (like from SearchModal)
      const requestData = {
        userName: finalUserName,
        dedication: dedication,
        actionType: actionType
      };

      console.log("Submitting single song request data:", requestData);
      onSubmit(requestData);
    }

    onClose();
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const getStepTitle = () => {
    switch (step) {
      case 1: return "Queue your Song Requests";
      case 2: return actionType === "singForMe" ? "Dedication message" : "Add Song";
      case 3: return "Queuing Cart";
      case 4: return "Confirm";
      default: return "";
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      aria-labelledby="song-request-dialog-title"
      maxWidth="sm"
      sx={{
        "& .MuiDialog-paper": {
          width: {
            xs: "90vw",
            sm: "80vw",
            md: "500px",
          },
          margin: {
            xs: "16px",
            sm: "24px",
          },
          borderRadius: "12px",
          border: "1px solid",
          backgroundColor: "#000000",
        },
      }}
      slotProps={{
        backdrop: {
          sx: {
            backdropFilter: "blur(8px)",
            backgroundColor: "rgba(0, 0, 0, 0.4)",
          },
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#000000",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: "Nunito",
            fontSize: "32px",
            fontWeight: "700",
            lineHeight: "40px",
            letterSpacing: "0.2px",
            marginBottom: "8px",
            textAlign: "left",
          }}
        >
          {getStepTitle()}
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ color: "#FFFFFF" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          backgroundColor: "#000000",
          color: "#FFFFFF",
          padding: "24px",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {step === 1 && (
          <Step1ActionSelection
            song={song}
            onActionSelect={handleActionSelection}
            requestMode={requestMode}
          />
        )}

        {step === 2 && (
          <Step2DedicationForm
            song={song}
            dedication={dedication}
            onDedicationSubmit={handleDedicationSubmit}
            onBack={handleBack}
          />
        )}

        {step === 3 && (
          <Step3CartReview
            song={song}
            onAddMoreSongs={onClose}
            onContinue={() => setStep(4)}
            onBack={handleBack}
            onRemoveSong={removeFromCart}
          />
        )}

        {step === 4 && (
          <Step4Confirmation
            userName={userName}
            setUserName={setUserName}
            isAnonymous={isAnonymous}
            setIsAnonymous={setIsAnonymous}
            onConfirm={handleConfirmRequest}
            onBack={handleBack}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default DedicationMaster;