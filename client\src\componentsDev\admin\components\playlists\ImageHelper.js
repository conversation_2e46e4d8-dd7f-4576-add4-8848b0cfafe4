// utils/imageHelper.js

/**
 * Helper function to get the correct image URL
 * Handles both File objects (for preview) and string paths (for existing images)
 */
export const getImageUrl = (image) => {
  // If no image provided, return a default placeholder
  if (!image) {
    return '/default-playlist-image.png'; // Make sure you have this file in public folder
  }

  // If it's a File object (from file input), create a blob URL
  if (image instanceof File) {
    return URL.createObjectURL(image);
  }

  // If it's a string, check if it's already a full URL
  if (typeof image === 'string') {
    // If it starts with http/https, return as is
    if (image.startsWith('http://') || image.startsWith('https://')) {
      return image;
    }
    
    // If it's a relative path, prepend the API URL
    return `${process.env.REACT_APP_API_URL}/${image.replace(/^\/+/, '')}`;
  }

  // Fallback to default image
  return '/default-playlist-image.png';
};

/**
 * Helper function to validate image file
 */
export const validateImageFile = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 5 * 1024 * 1024; // 5MB

  if (!validTypes.includes(file.type)) {
    return { valid: false, error: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)' };
  }

  if (file.size > maxSize) {
    return { valid: false, error: 'Image file size should be less than 5MB' };
  }

  return { valid: true };
};

/**
 * Helper function to format file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};