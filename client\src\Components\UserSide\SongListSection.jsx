import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Grid,
  Container,
  Box,
  CircularProgress,
  Button,
  Typography,
} from "@mui/material";
import RequestHandler from "./RequestHandler";
import { useSongCart } from "../../context/SongCartContext";
import { getDeviceId } from "../../utils/cookieUtils";
import SongCard from "./SongCard";

const INITIAL_SONGS_COUNT = 20;
const SONGS_PER_PAGE = 10; // For "Load More"

const SongListSection = ({ songs }) => {
  const [visibleSongs, setVisibleSongs] = useState([]);
  const [visibleCount, setVisibleCount] = useState(INITIAL_SONGS_COUNT);
  const [initialLoading, setInitialLoading] = useState(false); // For first-time fetch
  const [loadingMore, setLoadingMore] = useState(false); // For "Load More" button
  const [selectedSong, setSelectedSong] = useState({});
  const [openRequestModal, setOpenRequestModal] = useState(false);
  const [step, setStep] = useState(1);
  const [activeRequests, setActiveRequests] = useState({});
  const deviceId = getDeviceId();
  const { cart } = useSongCart();

  const dataLoadedRef = useRef(false);

  useEffect(() => {
    if (dataLoadedRef.current) return;
    dataLoadedRef.current = true;
    fetchActiveRequests();
  }, []);

  const fetchActiveRequests = async () => {
    try {
      const res = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`
      );
      const data = await res.json();
      setActiveRequests(data);
    } catch (error) {
      console.error("Error fetching active requests:", error);
    }
  };

  useEffect(() => {
    if (songs && songs.length > 0) {
      setVisibleSongs(songs.slice(0, INITIAL_SONGS_COUNT));
    }
  }, [songs]);

  const handleRequestSuccess = () => {
    fetchActiveRequests();
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      const newCount = visibleCount + SONGS_PER_PAGE;
      setVisibleSongs(songs.slice(0, newCount));
      setVisibleCount(newCount);
      setLoadingMore(false);
    }, 1000); // Simulated delay
  };

  const handleRequestOpen = (song) => {
    if (cart.length >= 3) {
      setStep(3);
    } else {
      setSelectedSong(song);
      setStep(1);
    }
    setOpenRequestModal(true);
  };

  const hasMore = visibleSongs.length < songs.length;

  return (
    <>
      <Container
        maxWidth="xl"
        sx={{
          mt: 0,
          bgcolor: "background.default",
          py: 2,
          px: 3,
        }}
      >
        {initialLoading ? (
          <Box
            sx={{
              height: "60vh",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CircularProgress size={50} thickness={5} />
          </Box>
        ) : (
          <Grid container spacing={2}>
            {visibleSongs.map((song, index) => (
              <Grid
                item
                xs={12}
                key={`${song.songs_master_id || `temp-id-${index}`}`}
                sx={{
                  width: "100%",
                  paddingLeft: "0px !important",
                  paddingTop: "0px !important",
                }}
              >
                <SongCard
                  song={song}
                  onRequestOpen={handleRequestOpen}
                  activeRequests={activeRequests}
                  cart={cart}
                />
              </Grid>
            ))}
          </Grid>
        )}

        {hasMore && !initialLoading && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              mt: 3,
              marginBottom: "1rem",
            }}
          >
            <Button
              variant="contained"
              onClick={handleLoadMore}
              sx={{
                background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                color: "#fff",
                fontWeight: "bold",
                fontSize: "1rem",
                px: 4,
                py: 1,
                borderRadius: "2rem",
                boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
                transition: "all 0.3s ease-in-out",
                "&:hover": {
                  background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                  transform: "scale(1.05)",
                },
              }}
              disabled={loadingMore}
            >
              {loadingMore ? (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <CircularProgress size={20} thickness={5} color="inherit" />
                  <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                    Loading...
                  </Typography>
                </Box>
              ) : (
                "Show More Songs"
              )}
            </Button>
          </Box>
        )}
      </Container>

      <RequestHandler
        selectedSong={selectedSong}
        setSelectedSong={setSelectedSong}
        openRequestModal={openRequestModal}
        setOpenRequestModal={setOpenRequestModal}
        onRequestSuccess={handleRequestSuccess}
        step={step}
        setStep={setStep}
      />
    </>
  );
};

export default SongListSection;
