const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SongRequestModel = {
  create: ({
    songs_master_id,
    requester_name,
    dedication_msg,
    status,
    action_type,
    device_id,
  }) => {
    return new Promise((resolve, reject) => {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 60 * 60 * 1000);
      const query = `
        INSERT INTO song_requests (songs_master_id, requester_name, dedication_msg, status, action_type, device_id, expires_at, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
      `;
      db.run(
        query,
        [
          songs_master_id,
          requester_name,
          dedication_msg,
          status,
          action_type,
          device_id,
          expiresAt.toISOString(),
        ],
        function (err) {
          if (err) return reject(err);
          resolve({ id: this.lastID });
        }
      );
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM song_requests ORDER BY created_at DESC`,
        [],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  updateStatus: (song_request_id, status) => {
    return new Promise((resolve, reject) => {
      let query = `
      UPDATE song_requests
      SET status = ?${
        status === "Fulfilled" ? ', fulfilled_at = datetime("now")' : ""
      }
      WHERE song_request_id = ?
    `;
      db.run(query, [status, song_request_id], function (err) {
        if (err) return reject(err);
        resolve({ updated: this.changes });
      });
    });
  },

  delete: (song_request_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM song_requests WHERE song_request_id = ?`,
        [song_request_id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  checkIfCanRequest: async (device_id, songs_master_id) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT * FROM song_requests
      WHERE device_id = ? 
      AND songs_master_id = ?
      AND (expires_at IS NULL OR expires_at > datetime('now'))
      ORDER BY created_at DESC
      LIMIT 1
    `;

      db.get(query, [device_id, songs_master_id], (err, row) => {
        if (err) return reject(err);
        resolve(!!row); // returns true if active request exists
      });
    });
  },

  getActiveSongRequests: (device_id) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT songs_master_id, expires_at , status
      FROM song_requests
      WHERE device_id = ?
      AND expires_at > datetime('now')
    `;

      db.all(query, [device_id], (err, rows) => {
        if (err) return reject(err);

        // Convert to object: { songs_master_id: { expires_at, status } }
        const result = {};
        rows.forEach((row) => {
          result[row.songs_master_id] = {
            expires_at: row.expires_at,
            status: row.status,
          };
        });

        resolve(result);
      });
    });
  },

  autoFulfillExpiredRequests: () => {
    const query = `
    UPDATE song_requests
    SET status = 'Unfulfilled'
    WHERE status = 'Pending'
    AND DATETIME(created_at, '+1 hour') <= DATETIME('now')
  `;

    db.run(query, function (err) {
      if (err) {
        console.error("Error updating expired requests:", err);
      } else {
        if (this.changes > 0) {
          console.log(
            `Updated ${this.changes} expired request(s) to Unfulfilled.`
          );
        }
      }
    });
  },

  getUnfulfilledRequests: () => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT * FROM song_requests
      WHERE status = 'Pending'
      AND DATETIME(created_at, '+1 hour') <= DATETIME('now')
      ORDER BY created_at DESC
    `;
      db.all(query, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },
};

module.exports = SongRequestModel;
