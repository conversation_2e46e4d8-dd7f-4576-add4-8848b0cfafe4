const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SongModel = {
  create: async ({
    name,
    artist,
    album,
    release_year,
    decade,
    duration,
    image,
    spotify_id,
  }) => {
    return new Promise(async (resolve, reject) => {
      try {
        // Check for existing song
        const existingSong = await SongModel.getByNameAndArtist(name, artist);
        if (existingSong) {
          return resolve({ song: existingSong, isDuplicate: true });
        }
        const query = `
        INSERT INTO songs_master 
        (name, artist, album, release_year, decade, duration, image, spotify_id, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
      `;

        db.run(
          query,
          [
            name,
            artist,
            album,
            release_year,
            decade,
            duration,
            image,
            spotify_id,
          ],
          function (err) {
            if (err) return reject(err);
            // Use an arrow function to maintain the context of `this`
            SongModel.getById(this.lastID)
              .then((song) => resolve({ song, isDuplicate: false }))
              .catch(reject);
          }
        );
      } catch (err) {
        reject(err);
      }
    });
  },

  addGenre: (songId, genreId) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT OR IGNORE INTO song_genres (songs_master_id, genre_type_lookup_id)
        VALUES (?, ?)
      `;
      db.run(query, [songId, genreId], function (err) {
        if (err) return reject(err);
        resolve({ added: this.changes });
      });
    });
  },

  removeGenres: (songId) => {
    return new Promise((resolve, reject) => {
      const query = `
        DELETE FROM song_genres WHERE songs_master_id = ?
      `;
      db.run(query, [songId], function (err) {
        if (err) return reject(err);
        resolve({ deleted: this.changes });
      });
    });
  },

  getGenresBySongId: (songId) => {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT g.genre_type_lookup_id, g.genre_type_name, g.genre_type_description
        FROM genre_type_lookup g
        JOIN song_genres sg ON g.genre_type_lookup_id = sg.genre_type_lookup_id
        WHERE sg.songs_master_id = ?
      `;
      db.all(query, [songId], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM songs_master
      `;
      db.all(query, [], async (err, songs) => {
        if (err) return reject(err);

        // For each song, get its genres
        try {
          const songsWithGenres = await Promise.all(
            songs.map(async (song) => {
              const genres = await SongModel.getGenresBySongId(
                song.songs_master_id
              );
              return { ...song, genres };
            })
          );
          resolve(songsWithGenres);
        } catch (err) {
          reject(err);
        }
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM songs_master WHERE songs_master_id = ?
      `;
      db.get(query, [id], async (err, song) => {
        if (err) return reject(err);
        if (!song) return resolve(null);

        // Get genres for the song
        try {
          const genres = await SongModel.getGenresBySongId(id);
          resolve({ ...song, genres });
        } catch (err) {
          reject(err);
        }
      });
    });
  },

  update: (
    id,
    { name, artist, album, release_year, decade, duration, image }
  ) => {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE songs_master
        SET name = ?, artist = ?, album = ?, release_year = ?, decade = ?, duration = ?, image = ?
        WHERE songs_master_id = ?
      `;
      db.run(
        query,
        [name, artist, album, release_year, decade, duration, image, id],
        function (err) {
          if (err) return reject(err);
          resolve({ updated: this.changes });
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      // Delete genres first to maintain referential integrity
      SongModel.removeGenres(id)
        .then(() => {
          db.run(
            `DELETE FROM songs_master WHERE songs_master_id = ?`,
            [id],
            function (err) {
              if (err) return reject(err);
              resolve({ deleted: this.changes });
            }
          );
        })
        .catch(reject);
    });
  },

  search: ({ name = "", artist = "", genre = "" }) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT DISTINCT sm.*
      FROM songs_master sm
      LEFT JOIN song_genres sg ON sm.songs_master_id = sg.songs_master_id
      LEFT JOIN genre_type_lookup gtl ON sg.genre_type_lookup_id = gtl.genre_type_lookup_id
      WHERE sm.name LIKE ? AND sm.artist LIKE ? AND (gtl.genre_type_name LIKE ? OR gtl.genre_type_name IS NULL)
    `;

      db.all(query, [`%${name}%`, `%${artist}%`, `%${genre}%`], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getAllArtists: () => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT 
        a.artist_id, 
        a.name AS artistName, 
        a.image,
        COUNT(sa.songs_master_id) AS songs_count
      FROM artists_master a
      LEFT JOIN song_artists sa ON sa.artist_id = a.artist_id
      GROUP BY a.artist_id
    `;
      db.all(query, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getByNameAndArtist: (name, artist) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT * FROM songs_master WHERE name = ? AND artist = ?
    `;
      db.get(query, [name, artist], (err, song) => {
        if (err) return reject(err);
        resolve(song);
      });
    });
  },

  getPopularSongs: () => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT 
        sm.*, 
        COUNT(sr.songs_master_id) AS request_count
      FROM 
        songs_master sm
      LEFT JOIN 
        song_requests sr ON sm.songs_master_id = sr.songs_master_id
      GROUP BY 
        sm.songs_master_id
      HAVING 
        request_count >= 1
      ORDER BY 
        request_count DESC, 
        MAX(sr.created_at) DESC
    `;
      db.all(query, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  saveArtist: ({ id, name, image }) => {
    return new Promise((resolve, reject) => {
      const query = `
      INSERT OR IGNORE INTO artists_master (artist_id, name, image)
      VALUES (?, ?, ?)
    `;
      db.run(query, [id, name, image], function (err) {
        if (err) return reject(err);
        resolve(true);
      });
    });
  },

  linkSongToArtist: ({ songId, artistId }) => {
    return new Promise((resolve, reject) => {
      const query = `
      INSERT OR IGNORE INTO song_artists (songs_master_id, artist_id)
      VALUES (?, ?)
    `;
      db.run(query, [songId, artistId], function (err) {
        if (err) return reject(err);
        resolve(true);
      });
    });
  },

  getSongsByArtistId: (artistId) => {
    return new Promise((resolve, reject) => {
      const query = `
      SELECT s.* FROM songs_master s
      JOIN song_artists sa ON sa.songs_master_id = s.songs_master_id
      WHERE sa.artist_id = ?
    `;
      db.all(query, [artistId], async (err, rows) => {
        if (err) return reject(err);

        try {
          const songsWithGenres = await Promise.all(
            rows.map(async (song) => {
              const genres = await SongModel.getGenresBySongId(
                song.songs_master_id
              );
              return { ...song, genres };
            })
          );
          resolve(songsWithGenres);
        } catch (err) {
          reject(err);
        }
      });
    });
  },

  getArtistById: (artistId) => {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM artists_master WHERE artist_id = ?`;
      db.get(query, [artistId], (err, row) => {
        if (err) return reject(err);
        resolve(row || null);
      });
    });
  },
};

module.exports = SongModel;
