const express = require("express");
const router = express.Router();
const controller = require("../controllers/songFileController");
const SongFileModel = require('../models/songFileModel');
const upload = require('../middleware/fileUploadMiddleware')
const path = require('path');

router.get("/song/:songId", controller.getBySongId);

// CRUD Routes
router.post("/", controller.create);
router.get("/", controller.getAll);
router.get("/:id", controller.getById);
router.put("/:id", controller.update);
router.delete("/:id", controller.delete);

router.post('/files/:songId', upload.single('file'), async (req, res) => {
  const { songId } = req.params;
  const { file_type_lookup_id } = req.body;
  const filePath = path.join('uploads', songId, req.file.filename);
  try {
    const result = await SongFileModel.create({
      songs_master_id: songId,
      file: filePath,
      file_type_lookup_id,
    });
    res.status(201).json({ message: 'File uploaded', data: result });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;
