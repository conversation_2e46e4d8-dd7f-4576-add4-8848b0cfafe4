const express = require("express");
const router = express.Router();
const importSpotifyController = require("../controllers/importSpotifyController");

// Save selected tracks
router.post("/add-spotify-tracks", importSpotifyController.addSpotifyTracks);

// Get all Spotify tracks
router.get("/tracks/all", importSpotifyController.getAllSpotifyTracks);

// Update a specific Spotify track
router.put("/tracks/:id", importSpotifyController.updateSpotifyTrack);

// Save selected artists to master table of artists
router.post("/add-spotify-artists", importSpotifyController.addSpotifyArtists);

// Get tracks for a specific import song
router.get("/tracks/:import_song_id", importSpotifyController.getSpotifyTracks);

// Get artists for a specific import song
router.get("/artists/:import_song_id", importSpotifyController.getSpotifyArtists);

// Delete all tracks for a specific import song
router.delete("/tracks/:import_song_id", importSpotifyController.deleteSpotifyTracks);

// Delete all artists for a specific import song
router.delete("/artists/:import_song_id", importSpotifyController.deleteSpotifyArtists);

// Delete a specific track by track ID
router.delete("/track/:track_id", importSpotifyController.deleteSpotifyTrack);

// Delete a specific artist by artist ID
router.delete("/artist/:artist_id", importSpotifyController.deleteSpotifyArtist);

module.exports = router;