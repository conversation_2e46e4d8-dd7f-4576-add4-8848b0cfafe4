import { useState, useEffect } from "react";
import {
  Con<PERSON>er,
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Divider
} from "@mui/material";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import NavigationBar from "../navigation/Navbar";

// Axios instance using environment variable
const API = axios.create({
  baseURL: process.env.REACT_APP_API_URL
});

export default function ShowdownForm() {
  const [formData, setFormData] = useState({ name: "", email: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");
  const [timeRemaining, setTimeRemaining] = useState(0);

  // Live countdown effect
  useEffect(() => {
    if (timeRemaining <= 0) return; 
    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1000) return 0;
        return prev - 1000;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [timeRemaining]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (timeRemaining > 0) return;

    setIsSubmitting(true);
    setMessage("");

    try {
      const response = await API.post("/api/showdown/register", formData);

      if (response.status === 201) {
        // Registration successful
        toast.success(response.data.message || "Registration successful! 🎉", {
          position: "bottom-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          theme: "colored"
        });
        setMessage(response.data.message || "✅ Registration successful! Thank you for joining the showdown!");
        setFormData({ name: "", email: "" });
        setTimeRemaining(0);
      }
    } catch (err) {
      if (err.response && err.response.status === 429) {
        const data = err.response.data;
        if (data.cooldown) {
          const [mins, secs] = data.cooldown.split(":").map(Number);
          setTimeRemaining(mins * 60000 + secs * 1000);
        }
        toast.error(data.error || "You are on cooldown.");
      } else {
        console.error(err);
        toast.error("Network or server error.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Convert ms to MM:SS
  const formatTime = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  const isBlocked = timeRemaining > 0;
  const isFormValid = formData.name.trim() && formData.email.trim();

  return (
    <Box sx={{ minHeight: "100vh", bgcolor: "#212121", py: 6 }}>

      <NavigationBar isShowdown={false} />
      <Container maxWidth="md" sx={{
        paddingTop: "6rem",
      }}>
        {/* Header */}
        <Box textAlign="center" mb={6}>
          {/* <Avatar
            sx={{
              width: 96,
              height: 96,
              bgcolor: "linear-gradient(to right, #f56565, #ed8936)",
              background: "linear-gradient(to right, rgb(239,68,68), rgb(251,146,60))",
              mb: 2,
              fontSize: "2rem",
              justifySelf: "center",
            }}
          >
            🎤
          </Avatar> */}
          {/* <img src="./MindYourMusic.png" alt="Logo" height={"150px"} /> */}
          <img src="/showdown_logo.png" alt="Logo" height={"180px"} />
          {/* <Typography variant="h4" fontWeight="bold" color="white" gutterBottom>
            Welcome to Centre Stage Showdowns
          </Typography> */}
          <Typography variant="body1" sx={{ color: "gray.400" }}>
            Sign up for crowd-favourite games—Complete the Song Lyrics, Guess the Song Name, and Music Charades. Win over the audience, outsmart your rivals, and walk away with the prizes.
          </Typography>
        </Box>

        {/* Form Card */}
        <Paper
          elevation={6}
          sx={{
            bgcolor: "#2a2a2a",
            p: 4,
            border: "1px solid #3a3a3a",
            borderRadius: 2,
            maxWidth: 500,
            mx: "auto"
          }}
        >
          <form onSubmit={handleSubmit}>
            <TextField
              label="Full Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              disabled={isBlocked}
              variant="outlined"
              margin="normal"
              InputProps={{
                style: { color: isBlocked ? "#888" : "#fff", borderColor: "#666" }
              }}
            />

            <TextField
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              fullWidth
              disabled={isBlocked}
              variant="outlined"
              margin="normal"
              InputProps={{ style: { color: isBlocked ? "#888" : "#fff" } }}
            />

            <Box mt={3}>
              {isBlocked ? (
                <Paper
                  variant="outlined"
                  sx={{
                    p: 3,
                    textAlign: "center",
                    bgcolor: "rgba(251,146,60,0.1)",
                    borderColor: "rgba(251,146,60,0.3)"
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{ color: "orange", fontWeight: "bold", mb: 1 }}
                  >
                    ⏱️ Registration Cooldown
                  </Typography>
                  <Typography variant="body2" sx={{ color: "#bbb", mb: 1 }}>
                    You can register again in:
                  </Typography>
                  <Typography
                    variant="h4"
                    sx={{ fontFamily: "monospace", color: "orange" }}
                  >
                    {formatTime(timeRemaining)}
                  </Typography>
                </Paper>
              ) : (
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={isSubmitting || !isFormValid}
                  sx={{
                    py: 1.5,
                    fontWeight: "bold",
                    color: "white",
                    background: "linear-gradient(to right, rgb(239,68,68), rgb(251,146,60))",
                    "&:hover": {
                      background: "linear-gradient(to right, rgba(221, 23, 23, 1), rgba(241, 115, 47, 1))"
                    }
                  }}
                >
                  {isSubmitting ? (
                    <>
                      <CircularProgress size={20} sx={{ color: "white", mr: 1 }} />
                      Registering...
                    </>
                  ) : (
                    "REGISTER"
                  )}
                </Button>
              )}
            </Box>
          </form>

          {/* Success Message */}
          {message && !isBlocked && (
            <Alert
              severity="success"
              sx={{
                mt: 3,
                backgroundColor: "rgba(34,197,94,0.1)",
                borderColor: "rgba(34,197,94,0.3)",
                color: "#86efac"
              }}
            >
              {message}
            </Alert>
          )}
        </Paper>

        {/* Footer Info */}
        <Typography
          variant="caption"
          sx={{ display: "block", textAlign: "center", mt: 3, color: "#777" }}
        >
          Registration is limited to once per hour per user
        </Typography>

        <Divider sx={{ my: 5, borderColor: "#444" }} />
        <ToastContainer />
      </Container>
    </Box>
  );
}
