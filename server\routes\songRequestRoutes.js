// songRequestRoutes.js
const express = require("express");
const router = express.Router();
const SongRequestModel = require("../models/songRequestModel"); // Import your SongRequest model
const {
  createSongRequest,
  getAllSongRequests,
  updateSongRequestStatus,
  deleteSongRequest,
  checkRequestLimit,
  getActiveSongRequests,
  getUnfulfilledSongRequests,
} = require("../controllers/songRequestController"); // Import the controller functions

// POST: Create a new song request
router.post("/song-requests", createSongRequest);

// GET: Retrieve all song requests
router.get("/song-requests", getAllSongRequests);

// PATCH: Update the status of a specific song request
router.patch(
  "/song-requests/:requestId/song/:songs_master_id",
  updateSongRequestStatus
);

// DELETE: Delete a specific song request (if needed, you can implement this in the controller)
router.delete("/song-requests/:id", deleteSongRequest);

// GET: Check request limit for device_id
router.get("/song-requests/limit", checkRequestLimit);

router.get('/song-requests/can-request', async (req, res) => {
  const { device_id, song_id } = req.query;
  try {
    const hasActiveRequest = await SongRequestModel.checkIfCanRequest(device_id, song_id);
    res.json({ hasActiveRequest });
  } catch (error) {
    res.status(500).json({ message: "Error checking request status" });
  }
});

router.get("/song-requests/active-requests", getActiveSongRequests);

router.get("/song-requests/unfulfilled", getUnfulfilledSongRequests);

module.exports = router;
