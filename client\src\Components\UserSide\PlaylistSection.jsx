import { useState } from "react";
import {
  Grid,
  Card,
  CardActionArea,
  CardMedia,
  Typography,
  Box,
  Button,
  CircularProgress,
} from "@mui/material";
import { getImageUrl } from "../../utils/imageHelper";

const INITIAL_PLAYLIST_COUNT = 20;
const PLAYLISTS_PER_PAGE = 10;

const cardStyle = {
  backgroundColor: "#4d4d4d",
  borderRadius: "14px",
  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.3)",
  overflow: "hidden",
  width: "100%",
  border: "1px solid",
};

const cardMediaStyle = {
  width: "100%",
  height: "auto",
  aspectRatio: "1 / 1",
  objectFit: "cover",
};

const textStyle = {
  marginTop: "10px",
  textAlign: "center",
  color: "#FFF",
  fontSize: {
    xs: "12px",
    sm: "14px",
    md: "16px",
    lg: "18px",
  },
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "normal",
  lineHeight: "1.2",
  maxWidth: "100%",
  height: "auto",
};

const PlaylistSection = ({ playlists, onPlaylistSelect }) => {
  const [visibleCount, setVisibleCount] = useState(INITIAL_PLAYLIST_COUNT);
  const [loadingMore, setLoadingMore] = useState(false);

  const visiblePlaylists = playlists.slice(0, visibleCount);
  const hasMore = visibleCount < playlists.length;

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setVisibleCount((prev) => prev + PLAYLISTS_PER_PAGE);
      setLoadingMore(false);
    }, 800); // Simulated delay
  };

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "1280px",
        margin: "0 auto",
        padding: "0 16px",
      }}
    >
      <Grid container spacing={3}>
        {visiblePlaylists.map((playlist, index) => (
          <Grid
            item
            xs={6}
            sm={6}
            md={4}
            lg={3}
            key={`${playlist.playlist_master_id || "playlist"}-${index}`}
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
          >
            <Card sx={cardStyle}>
              <CardActionArea
                onClick={() => onPlaylistSelect(playlist.playlist_master_id)}
              >
                <CardMedia
                  component="img"
                  sx={cardMediaStyle}
                  image={getImageUrl(playlist.image)}
                  alt={playlist.title}
                />
              </CardActionArea>
            </Card>
            <Typography sx={textStyle}>{playlist.title}</Typography>
          </Grid>
        ))}
      </Grid>

      {/* Load More Button and Loader */}
      {hasMore && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
          <Button
            variant="contained"
            onClick={handleLoadMore}
            disabled={loadingMore}
            sx={{
              background: "linear-gradient(45deg, #ffa040, #ff8f00)",
              color: "#fff",
              fontWeight: "bold",
              fontSize: "1rem",
              px: 4,
              py: 1,
              borderRadius: "2rem",
              boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
              transition: "all 0.3s ease-in-out",
              "&:hover": {
                background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                transform: "scale(1.05)",
              },
            }}
          >
            {loadingMore ? (
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CircularProgress size={20} thickness={5} color="inherit" />
                <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                  Loading...
                </Typography>
              </Box>
            ) : (
              "Show More Playlists"
            )}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default PlaylistSection;
