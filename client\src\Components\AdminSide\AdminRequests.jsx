import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>ton,
  Typo<PERSON>,
  Tabs,
  Tab,
  IconButton,
  <PERSON>,
  Box,
  styled,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
} from "@mui/material";
import MusicNoteIcon from "@mui/icons-material/MusicNote";
import MessageIcon from "@mui/icons-material/Message";
import PersonIcon from "@mui/icons-material/Person";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import YouTubeIcon from "@mui/icons-material/YouTube";
import { timeSince } from "../../utils/timeSince";
import Header from "./Header";
import io from "socket.io-client";

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.9rem",
  fontWeight: "bold",
  color: "white",
  flex: 1,
  "&.Mui-selected": {
    color: "#1db954",
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  "& .MuiTabs-indicator": {
    backgroundColor: "#1db954",
  },
  marginBottom: theme.spacing(2),
}));

// Map original statuses to new labels
const statusLabelMap = {
  Pending: "Song-Q",
  Fulfilled: "Played",
  Unfulfilled: "Not Played",
};

const getRequestStatus = (request) => {
  const now = new Date();
  const expiresAt = new Date(request.expires_at);
  if (request.status === "Fulfilled") return statusLabelMap["Fulfilled"];
  if (now >= expiresAt) return statusLabelMap["Unfulfilled"];
  return statusLabelMap["Pending"];
};

const groupRequestsBySong = (requests) => {
  const grouped = {};

  requests.forEach((request) => {
    // Only group pending requests
    if (request.displayStatus === "Song-Q") {
      if (!grouped[request.songs_master_id]) {
        grouped[request.songs_master_id] = {
          ...request,
          requestCount: 1,
          allRequests: [request],
        };
      } else {
        grouped[request.songs_master_id].requestCount += 1;
        grouped[request.songs_master_id].allRequests.push(request);
      }
    } else {
      // For non-pending requests, keep them as individual entries
      const key = `${request.songs_master_id}_${request.song_request_id}`;
      grouped[key] = {
        ...request,
        requestCount: 1,
        allRequests: [request],
      };
    }
  });

  return Object.values(grouped);
};

const AdminRequests = () => {
  const [selectedTab, setSelectedTab] = useState("Song-Q");
  const [requests, setRequests] = useState([]);
  const [page, setPage] = useState(1);
  const [sortOrder, setSortOrder] = useState("desc");
  const [selectedSong, setSelectedSong] = useState(null);
  const [songUrls, setSongUrls] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [songFiles, setSongFiles] = useState([]);

  const itemsPerPage = 10;

  const socket = io(process.env.REACT_APP_API_URL);

  useEffect(() => {
    const handleNewRequest = (newRequest) => {
      setRequests((prev) => {
        // Check if request already exists to prevent duplicates
        const exists = prev.some(
          (req) => req.song_request_id === newRequest.song_request_id
        );
        if (!exists) {
          return [newRequest, ...prev];
        }
        return prev;
      });

      if (
        selectedSong &&
        newRequest.songs_master_id === selectedSong.songs_master_id
      ) {
        setSelectedSong((prev) => {
          if (prev.request.allRequests) {
            const requestExists = prev.request.allRequests.some(
              (req) => req.song_request_id === newRequest.song_request_id
            );
            if (!requestExists) {
              return {
                ...prev,
                request: {
                  ...prev.request,
                  allRequests: [newRequest, ...prev.request.allRequests],
                  requestCount: prev.request.requestCount + 1,
                },
              };
            }
          }
          return prev;
        });
      }
    };

    socket.on("new-song-request", handleNewRequest);

    return () => {
      socket.off("new-song-request", handleNewRequest);
    };
  }, [selectedSong, socket]);

  const toggleSortOrder = () => {
    setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"));
    setRequests((prev) => [...prev].reverse());
  };

  const fetchRequests = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests`
      );
      const data = await response.json();

      if (response.ok) {
        const requestsWithSongs = await Promise.all(
          data.map(async (req) => {
            try {
              const songRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/songs/${req.songs_master_id}`
              );
              const songData = await songRes.json();

              // Fetch URLs and files count
              const urlsRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${req.songs_master_id}`
              );
              const urlsData = await urlsRes.json();

              const filesRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-files/song/${req.songs_master_id}`
              );
              const filesData = await filesRes.json();

              // Filter for YouTube URLs specifically
              const youtubeLinks = urlsData.filter((url) => 
                url.song_url && (
                  url.song_url.includes('youtube.com') || 
                  url.song_url.includes('youtu.be') ||
                  (url.url_type_name && url.url_type_name.toLowerCase().includes('youtube'))
                )
              );

              const validFiles = filesData.filter((file) => file.file);

              return {
                ...req,
                song_name: songData.name || "Unknown Song",
                artist: songData.artist || "Unknown Artist",
                release_year: songData.release_year || null,
                displayStatus: getRequestStatus(req),
                fulfilled_at: req.fulfilled_at,
                hasYoutubeLinks: youtubeLinks.length > 0,
                youtubeCount: youtubeLinks.length,
                hasFiles: validFiles.length > 0,
                fileCount: validFiles.length,
              };
            } catch {
              return {
                ...req,
                song_name: "Unknown Song",
                artist: "Unknown Artist",
                release_year: null,
                displayStatus: getRequestStatus(req),
                hasYoutubeLinks: false,
                youtubeCount: 0,
                hasFiles: false,
                fileCount: 0,
              };
            }
          })
        );

        setRequests(requestsWithSongs);
      } else {
        throw new Error("Failed to fetch song requests");
      }
    } catch (error) {
      console.error("Error fetching requests:", error);
    }
  }, []);

  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  useEffect(() => {
    const interval = setInterval(() => {
      setRequests((prevRequests) =>
        prevRequests.map((req) => {
          // Update displayStatus dynamically, especially for pending requests
          if (req.status === "Pending") {
            return {
              ...req,
              displayStatus: getRequestStatus(req),
            };
          }
          return req;
        })
      );
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const handleStatusChange = async (requestId, songMasterId, currentStatus) => {
    // Map displayed status back to API status for PATCH
    const statusReverseMap = {
      "Song-Q": "Pending",
      Played: "Fulfilled",
      "Not Played": "Unfulfilled",
    };
    const currentStatusApi = statusReverseMap[currentStatus];
    const newStatusApi =
      currentStatusApi === "Pending" ? "Fulfilled" : "Pending";
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/${requestId}/song/${songMasterId}`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ status: newStatusApi }),
        }
      );
      if (!response.ok) throw new Error("Failed to update status");

      setRequests((prevRequests) =>
        prevRequests.map((req) =>
          req.song_request_id === requestId
            ? {
                ...req,
                status: newStatusApi,
                displayStatus:
                  statusLabelMap[
                    newStatusApi === "Fulfilled" ? "Fulfilled" : "Pending"
                  ], // updated display status
              }
            : req
        )
      );
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const handleOpenSongDetails = async (songId, request) => {
    try {
      const songRes = await fetch(
        `${process.env.REACT_APP_API_URL}/api/songs/${songId}`
      );
      const songData = await songRes.json();

      const urlsRes = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songId}`
      );
      const urlsData = await urlsRes.json();

      const filesRes = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-files/song/${songId}`
      );
      const filesData = await filesRes.json();

      // If this is a grouped request, we already have all request info
      if (request.allRequests) {
        setSelectedSong({ ...songData, request });
      } else {
        // For single requests, find all matching requests from our existing data
        const allRequests = requests.filter(
          (req) =>
            req.songs_master_id === songId && req.displayStatus === "Song-Q"
        );

        setSelectedSong({
          ...songData,
          request: {
            ...request,
            allRequests: allRequests.length > 1 ? allRequests : [request],
          },
        });
      }
      setSongUrls(urlsData);
      setSongFiles(filesData);
      setOpenModal(true);
    } catch (error) {
      console.error("Error loading song info:", error);
    }
  };

  // Filter requests by selectedTab, respecting new status labels
  const filteredRequests = requests.filter((req) => {
    if (selectedTab === "All") return true;
    return req.displayStatus === selectedTab;
  });

  // Group the filtered requests (only affects pending requests)
  const groupedRequests = groupRequestsBySong(filteredRequests);

  // Pagination
  const paginatedRequests = groupedRequests.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  );
  const totalPages = Math.ceil(groupedRequests.length / itemsPerPage);

  return (
    <>
      <Header />
      <Container
        maxWidth="md"
        sx={{ mt: 2, pb: 4, bgcolor: "#121212", minHeight: "100vh" }}
      >
        <StyledTabs
          value={selectedTab}
          onChange={(e, newValue) => {
            setSelectedTab(newValue);
            setPage(1);
          }}
          variant="fullWidth"
        >
          <StyledTab label="Song-Q" value="Song-Q" />
          <StyledTab label="Played" value="Played" />
          <StyledTab label="Not Played" value="Not Played" />
          <StyledTab label="All" value="All" />
        </StyledTabs>

        <Button
          fullWidth
          variant="outlined"
          onClick={toggleSortOrder}
          startIcon={
            sortOrder === "asc" ? (
              <KeyboardArrowUpIcon />
            ) : (
              <KeyboardArrowDownIcon />
            )
          }
          sx={{ mb: 2, color: "white", borderColor: "white" }}
        >
          Sort by Date
        </Button>

        <Stack spacing={2}>
          {paginatedRequests.length > 0 ? (
            paginatedRequests.map((req, idx) => (
              <Card
                key={idx}
                sx={{
                  bgcolor: "#181818",
                  color:
                    req.displayStatus === "Played"
                      ? "#1db954"
                      : req.displayStatus === "Not Played"
                      ? "#ff1744"
                      : "white",
                  "&:hover": { bgcolor: "#282828", cursor: "pointer" },
                }}
                onClick={() => handleOpenSongDetails(req.songs_master_id, req)}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mb: 2,
                      alignItems: "center",
                      flexWrap: "wrap",
                      gap: 1,
                    }}
                  >
                    <Chip
                      label={req.displayStatus}
                      color={
                        req.displayStatus === "Played"
                          ? "success"
                          : req.displayStatus === "Not Played"
                          ? "error"
                          : "warning"
                      }
                      size="small"
                    />
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStatusChange(
                          req.song_request_id,
                          req.songs_master_id,
                          req.displayStatus
                        );
                      }}
                      sx={{
                        color:
                          req.displayStatus === "Played" ? "#1db954" : "orange",
                      }}
                      aria-label={
                        req.displayStatus === "Played"
                          ? "Mark as Song-Q"
                          : "Mark as Played"
                      }
                    >
                      {req.displayStatus === "Played" ? (
                        <CheckCircleIcon />
                      ) : (
                        <RadioButtonUncheckedIcon />
                      )}
                    </IconButton>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          alignItems: "center",
                        }}
                      >
                        <MusicNoteIcon
                          sx={{
                            fontSize: 20,
                            color:
                              req.displayStatus === "Played"
                                ? "#1db954"
                                : "white",
                          }}
                        />
                        <Typography
                          variant="subtitle1"
                          sx={{
                            color:
                              req.displayStatus === "Played"
                                ? "#1db954"
                                : "white",
                            fontWeight: "bold",
                            flexGrow: 1,
                            wordBreak: "break-word",
                          }}
                          component="span"
                        >
                          {req.song_name}{" "}
                          {req.release_year ? `(${req.release_year})` : ""}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          mb: 1,
                          alignItems: "center",
                        }}
                      >
                        <PersonIcon sx={{ fontSize: 20 }} />
                        <Typography variant="body2" color="text.secondary">
                          Requested by : {req.requester_name}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          mb: 1,
                          alignItems: "center",
                        }}
                      >
                        <MessageIcon sx={{ fontSize: 20 }} />
                        <Typography
                          variant="body2"
                          sx={{
                            whiteSpace: "pre-wrap",
                            wordBreak: "break-word",
                            flexGrow: 1,
                          }}
                        >
                          {req.dedication_msg || "No message"}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          mb: 1,
                          alignItems: "center",
                        }}
                      >
                        <AccessTimeIcon sx={{ fontSize: 20 }} />
                        <Typography variant="caption" color="text.secondary">
                          Requested : {timeSince(req.created_at)}
                        </Typography>
                      </Box>
                      {req.displayStatus === "Played" && req.fulfilled_at && (
                        <Box
                          sx={{ display: "flex", gap: 1, alignItems: "center" }}
                        >
                          <CheckCircleIcon sx={{ fontSize: 20 }} />
                          <Typography variant="caption" color="text.secondary">
                            Fulfilled : {timeSince(req.fulfilled_at)}
                          </Typography>
                        </Box>
                      )}
                      <Box sx={{ mt: 1 }}>
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ fontStyle: "italic" }}
                        >
                          Action Type: {req.action_type || "N/A"}
                        </Typography>
                      </Box>

                      <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        {/* YouTube Icon with count */}
                        <IconButton
                          size="small"
                          disabled={!req.hasYoutubeLinks}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenSongDetails(req.songs_master_id, req);
                          }}
                          sx={{
                            color: req.hasYoutubeLinks ? "red" : "gray",
                            bgcolor: "#282828",
                            "&:hover": { bgcolor: "#383838" },
                          }}
                        >
                          <YouTubeIcon fontSize="small" />
                          {req.youtubeCount > 0 && (
                            <Typography variant="caption" sx={{ ml: 0.5 }}>
                              {req.youtubeCount}
                            </Typography>
                          )}
                        </IconButton>

                        {/* File Icon with count */}
                        <IconButton
                          size="small"
                          disabled={!req.hasFiles}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenSongDetails(req.songs_master_id, req);
                          }}
                          sx={{
                            color: req.hasFiles ? "skyblue" : "gray",
                            bgcolor: "#282828",
                            "&:hover": { bgcolor: "#383838" },
                          }}
                        >
                          <MusicNoteIcon fontSize="small" />
                          {req.fileCount > 1 && (
                            <Typography variant="caption" sx={{ ml: 0.5 }}>
                              {req.fileCount}
                            </Typography>
                          )}
                        </IconButton>
                      </Box>
                    </Box>
                    <>
                      {/* Right: Request Count Tile */}
                      {req.requestCount > 1 &&
                        req.displayStatus === "Song-Q" && (
                          <Box
                            sx={{
                              width: "300px",
                              height: "150px",
                              backgroundColor: "#ffa726",
                              color: "#fff",
                              px: 2,
                              py: 1,
                              borderRadius: "10px",
                              fontWeight: "bold",
                              fontSize: "1.5rem",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              cursor: "pointer",
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenSongDetails(req.songs_master_id, req);
                            }}
                          >
                            {req.requestCount} Request
                            {req.requestCount > 1 ? "s" : ""}
                          </Box>
                        )}
                    </>
                  </Box>
                </CardContent>
              </Card>
            ))
          ) : (
            <Typography sx={{ color: "white", textAlign: "center", py: 4 }}>
              No requests found
            </Typography>
          )}
        </Stack>

        {totalPages > 1 && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 3,
              gap: 2,
            }}
          >
            <Button
              variant="outlined"
              onClick={() => setPage((p) => Math.max(1, p - 1))}
              disabled={page === 1}
              sx={{ flex: 1, color: "white", borderColor: "white" }}
            >
              Previous
            </Button>
            <Typography sx={{ color: "white" }}>
              {page} / {totalPages}
            </Typography>
            <Button
              variant="outlined"
              onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
              sx={{ flex: 1, color: "white", borderColor: "white" }}
            >
              Next
            </Button>
          </Box>
        )}
      </Container>

      {/* Dialog Modal */}
      <Dialog
        open={openModal}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
             setOpenModal(false);
             
          }
        }}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: "#1e1e1e",
            color: "white",
            borderRadius: 3,
            border: "1px solid",
          },
        }}
        aria-labelledby="song-details-dialog-title"
      >
        <DialogTitle
          sx={{
            bgcolor: "#2c2c2c",
            fontWeight: "bold",
            display: "flex",
            alignItems: "center",
            gap: 2,
            color: "#fff",
          }}
          id="song-details-dialog-title"
        >
          <Box
            component="img"
            src={selectedSong?.image}
            alt="Song"
            sx={{
              width: 30,
              height: 30,
              borderRadius: 1,
              objectFit: "cover",
            }}
          />
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            {selectedSong?.name || "Song Details"}
            {selectedSong?.release_year
              ? ` (${selectedSong.release_year})`
              : ""}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={() => setOpenModal(false)}
            sx={{ color: "#FFFFFF" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ bgcolor: "#121212" }}>
          {/* Song Metadata */}
          <Box mb={2}>
            <Typography variant="subtitle1" fontWeight="bold">
              Artist :{" "}
              <Typography component="span" color="gray">
                {selectedSong?.artist || "N/A"}
              </Typography>
            </Typography>
            <Typography variant="subtitle1" fontWeight="bold">
              Album :{" "}
              <Typography component="span" color="gray">
                {selectedSong?.album || "N/A"}
              </Typography>
            </Typography>
            <Typography variant="subtitle1" fontWeight="bold">
              Duration :{" "}
              <Typography component="span" color="gray">
                {selectedSong?.duration || "N/A"}
              </Typography>
            </Typography>
            {/* <Typography variant="subtitle1" fontWeight="bold">
              Release Year :{" "}
              <Typography component="span" color="gray">
                {selectedSong?.release_year || "N/A"}
              </Typography>
            </Typography> */}
          </Box>
          <Divider sx={{ borderColor: "#333", mb: 2 }} />

          <Box mb={2}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Request Info ({selectedSong?.request?.allRequests?.length || 1}{" "}
              requests)
            </Typography>

            {selectedSong?.request?.allRequests ? (
              selectedSong.request.allRequests
                .filter(
                  (req, index, self) =>
                    index ===
                    self.findIndex(
                      (r) => r.song_request_id === req.song_request_id
                    )
                )
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                .map((r, i) => (
                  <Box
                    key={i}
                    sx={{ mb: 2, p: 1, bgcolor: "#1a1a1a", borderRadius: 1 }}
                  >
                    <Typography>
                      Requested by:{" "}
                      <Typography component="span" color="#1db954">
                        {r.requester_name || "Unknown"}
                      </Typography>
                    </Typography>
                    <Typography>
                      Dedication Message:{" "}
                      <Typography
                        component="span"
                        color="lightblue"
                        sx={{ whiteSpace: "pre-wrap" }}
                      >
                        {r.dedication_msg || "No message"}
                      </Typography>
                    </Typography>
                    <Typography variant="caption" color="gray">
                      Requested: {timeSince(r.created_at)}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ fontStyle: "italic" }}
                      >
                        Action Type: {r.action_type || "N/A"}
                      </Typography>
                    </Box>
                    {r.status === "Fulfilled" && r.fulfilled_at && (
                      <Typography
                        variant="caption"
                        color="#00e676"
                        display="block"
                      >
                        Fulfilled: {timeSince(r.fulfilled_at)}
                      </Typography>
                    )}
                    {i < selectedSong.request.allRequests.length - 1 && (
                      <Divider sx={{ borderColor: "#333", my: 1 }} />
                    )}
                  </Box>
                ))
            ) : (
              <>
                <Typography>
                  Requested by:{" "}
                  <Typography component="span" color="#1db954">
                    {selectedSong?.request?.requester_name || "Unknown"}
                  </Typography>
                </Typography>
                <Typography>
                  Dedication Message:{" "}
                  <Typography
                    component="span"
                    color="lightblue"
                    sx={{ whiteSpace: "pre-wrap" }}
                  >
                    {selectedSong?.request?.dedication_msg || "No message"}
                  </Typography>
                </Typography>
                <Typography variant="caption" color="gray">
                  Requested: {timeSince(selectedSong?.request?.created_at)}
                </Typography>
                {selectedSong?.request?.status === "Fulfilled" &&
                  selectedSong?.request?.fulfilled_at && (
                    <Typography
                      variant="caption"
                      color="#00e676"
                      display="block"
                    >
                      Fulfilled: {timeSince(selectedSong.request.fulfilled_at)}
                    </Typography>
                  )}
              </>
            )}
          </Box>
          <Divider sx={{ borderColor: "#333", mb: 2 }} />
          {/* Song URLs - Always show the YouTube icon but grayed out if no links */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <YouTubeIcon
                sx={{
                  color: songUrls.length > 0 ? "red" : "gray",
                  opacity: songUrls.length > 0 ? 1 : 0.5,
                }}
              />
              <Typography
                variant="subtitle2"
                sx={{
                  color: songUrls.length > 0 ? "inherit" : "text.disabled",
                }}
              >
                YouTube Links
              </Typography>
              {songUrls.length > 0 && (
                <Chip label={songUrls.length} size="small" />
              )}
            </Box>
            {songUrls.length > 0 &&
              songUrls.map((u, i) => (
                <Box
                  key={i}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    mb: 1,
                    pl: 1,
                    py: 0.5,
                    bgcolor: "#1a1a1a",
                    borderRadius: 1,
                  }}
                >
                  {u.song_url ? (
                    <a
                      href={u.song_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        color: "#1db954",
                        wordBreak: "break-all",
                        fontSize: "0.9rem",
                      }}
                    >
                      {u.url_type_name || "YouTube Link"}
                    </a>
                  ) : (
                    <Typography color="error">Invalid URL</Typography>
                  )}
                </Box>
              ))}
          </Box>

          {/* Song Files - Always show the file icon but grayed out if no files */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
              <MusicNoteIcon
                sx={{
                  color: songFiles.length > 0 ? "skyblue" : "gray",
                  opacity: songFiles.length > 0 ? 1 : 0.5,
                }}
              />
              <Typography
                variant="subtitle2"
                sx={{
                  color: songFiles.length > 0 ? "inherit" : "text.disabled",
                }}
              >
                Song Files
              </Typography>
              {songFiles.length > 1 && (
                <Chip label={songFiles.length} size="small" />
              )}
            </Box>
            {songFiles.length > 0 &&
              songFiles.map((file, i) => (
                <Box
                  key={i}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    mb: 1,
                    pl: 1,
                    py: 0.5,
                    bgcolor: "#1a1a1a",
                    borderRadius: 1,
                  }}
                >
                  <a
                    href={`${process.env.REACT_APP_API_URL}/${file.file}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      color: "#1db954",
                      wordBreak: "break-all",
                      fontSize: "0.9rem",
                    }}
                  >
                    {file.file_type_name || file.file.split("\\").pop()}
                  </a>
                </Box>
              ))}
          </Box>
        </DialogContent>

        <DialogActions sx={{ bgcolor: "#181818" }}>
          <Button
            onClick={() => setOpenModal(false)}
            sx={{
              color: "#fff",
              borderColor: "#1db954",
              "&:hover": { bgcolor: "#1db954", color: "#000" },
            }}
            variant="outlined"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AdminRequests;
