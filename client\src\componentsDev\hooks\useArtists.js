// hooks/useArtists.js
import { useApi } from './useApi';

export const useArtists = (options = {}) => {
  return useApi(
    'artists',
    `${process.env.REACT_APP_API_URL}/api/songs/artists`,
    {
      select: (data) => data.filter((artist) => artist.songs_count > 0),
      ...options,
    }
  );
};

export const useArtistSongs = (id, options = {}) => {
  return useApi(
    ['artist_songs', id],
    `${process.env.REACT_APP_API_URL}/api/songs/artist/${id}`,
    options
  );
};

export const useArtistDetails = (id, options = {}) => {
  return useApi(
    ['artist_details', id],
    `${process.env.REACT_APP_API_URL}/api/songs/artist-detail/${id}`,
    options
  );
};