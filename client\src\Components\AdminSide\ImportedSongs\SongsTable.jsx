import React from "react";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  CircularProgress,
  Badge,
  useMediaQuery,
  Typography,
  Box,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import LibraryMusicIcon from "@mui/icons-material/LibraryMusic";
import { useTheme } from "@mui/material/styles";

const SongsTable = ({ songs, onEdit, onSpotifySearch, loadingSearch }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Paper
      sx={{
        bgcolor: "#121212",
        color: "white",
        width: "100%",
        overflow: "hidden",
      }}
    >
      <TableContainer sx={{ maxWidth: "100%", overflowX: "auto" }}>
        <Table size={isMobile ? "small" : "medium"}>
          <TableHead>
            <TableRow sx={{ backgroundColor: "#1DB954" }}>
              <TableCell
                sx={{
                  color: "white",
                  fontWeight: "bold",
                  borderTopLeftRadius: 8,
                  whiteSpace: "nowrap",
                }}
              >
                Song Name
              </TableCell>
              <TableCell
                sx={{
                  color: "white",
                  fontWeight: "bold",
                  whiteSpace: "nowrap",
                }}
              >
                Artist Name
              </TableCell>
              <TableCell
                sx={{
                  color: "white",
                  fontWeight: "bold",
                  whiteSpace: "nowrap",
                }}
              >
                Video URL
              </TableCell>
              <TableCell
                sx={{
                  color: "white",
                  fontWeight: "bold",
                  whiteSpace: "nowrap",
                }}
              >
                Status
              </TableCell>
              {!isMobile && (
                <TableCell
                  sx={{
                    color: "white",
                    fontWeight: "bold",
                    whiteSpace: "nowrap",
                  }}
                >
                  Created At
                </TableCell>
              )}
              <TableCell
                sx={{
                  color: "white",
                  fontWeight: "bold",
                  borderTopRightRadius: 8,
                  whiteSpace: "nowrap",
                }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {songs.map((song) => (
              <TableRow key={song.id}>
                <TableCell>
                  <Typography fontWeight="medium" color="white">
                    {song.song_name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography color="white">{song.artist_name}</Typography>
                </TableCell>
                <TableCell>
                  {song.video_url ? (
                    (() => {
                      let urls = [];
                      try {
                        urls = JSON.parse(song.video_url);
                        if (!Array.isArray(urls)) urls = [song.video_url]; // fallback for legacy
                      } catch (e) {
                        urls = [song.video_url]; // fallback if not JSON
                      }

                      return urls.map((url, idx) => (
                        <div key={idx}>
                          <a
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: "#1DB954",
                              textDecoration: "underline",
                            }}
                          >
                            Link {urls.length > 1 ? idx + 1 : ""}
                          </a>
                        </div>
                      ));
                    })()
                  ) : (
                    <Typography color="white">-</Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Badge color="primary" badgeContent={song.status} />
                </TableCell>
                {!isMobile && (
                  <TableCell>
                    <Typography variant="body2" color="white">
                      {new Date(song.created_at).toLocaleString()}
                    </Typography>
                  </TableCell>
                )}
                <TableCell>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton onClick={() => onEdit(song)} size="small">
                      <EditIcon sx={{ color: "white" }} />
                    </IconButton>
                    <IconButton
                      onClick={() => onSpotifySearch(song)}
                      disabled={loadingSearch === song.id}
                      title="Search on Spotify"
                      size="small"
                    >
                      {loadingSearch === song.id ? (
                        <CircularProgress size={20} />
                      ) : (
                        <LibraryMusicIcon style={{ color: "#1DB954" }} />
                      )}
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default SongsTable;
