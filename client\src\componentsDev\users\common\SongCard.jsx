// SongCard.js
import React, { useEffect, useState } from "react";
import { <PERSON>, Typography, Button, Chip, Tooltip } from "@mui/material";
import {
  AccessTime,
  MusicNote,
  CheckCircle,
  ShoppingCart
} from "@mui/icons-material";

const SongCard = ({ song, activeRequests, onRequestOpen, cart }) => {
  const [remainingTime, setRemainingTime] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const activeRequest = activeRequests[song.songs_master_id];
  const songStatus = activeRequest?.status;
  const songExpiresAt = activeRequest?.expires_at;

  const isInCart = cart?.some(
    (item) => item.songs_master_id === song.songs_master_id
  );

  const isRequestDisabled =
    activeRequest &&
    songStatus !== "Fulfilled" &&
    new Date(songExpiresAt) > new Date();

  const isDisabled = isInCart || isRequestDisabled;

  useEffect(() => {
    if (isRequestDisabled && songExpiresAt) {
      const interval = setInterval(() => {
        const timeLeft = Math.max(0, new Date(songExpiresAt) - new Date());
        setRemainingTime(timeLeft);

        if (timeLeft <= 0) {
          clearInterval(interval);
        }
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setRemainingTime(0);
    }
  }, [songExpiresAt, isRequestDisabled]);

  const formatTime = (ms) => {
    const seconds = Math.floor((ms / 1000) % 60);
    const minutes = Math.floor((ms / (1000 * 60)) % 60);
    const hours = Math.floor(ms / (1000 * 60 * 60));

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const formattedTime = formatTime(remainingTime);

  const getStatusInfo = () => {
    if (isInCart) return { text: "In Cart", icon: <ShoppingCart sx={{ fontSize: 16 }} />, color: "#1976d2" };
    if (isRequestDisabled) return { text: `Requested (${formattedTime})`, icon: <AccessTime sx={{ fontSize: 16 }} />, color: "#ff9800" };
    if (songStatus === "Fulfilled") return { text: "Played", icon: <CheckCircle sx={{ fontSize: 16 }} />, color: "#2e7d32" };
    return { text: "Available", icon: <MusicNote sx={{ fontSize: 16 }} />, color: "#4caf50" };
  };

  const statusInfo = getStatusInfo();

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 2,
        mb: 2,
        p: 2,
        border: "1px solid",
        borderColor: isHovered ? "#666" : "#3c3c3c",
        backgroundColor: isHovered && !isDisabled ? "#1a1a1a" : "#121212",
        borderRadius: "12px",
        opacity: isDisabled ? 0.7 : 1,
        transition: "all 0.2s ease-in-out",
        transform: isHovered && !isDisabled ? "translateY(-2px)" : "none",
        boxShadow: isHovered && !isDisabled ? "0 4px 12px rgba(0,0,0,0.15)" : "none",
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Song Image */}
      <Box
        component="img"
        src={song.image}
        alt={song.name}
        sx={{
          width: 56,
          height: 56,
          borderRadius: "8px",
          objectFit: "cover",
          flexShrink: 0,
        }}
      />

      {/* Song Info */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "flex-start",
          flex: 1,
          minWidth: 0, // Prevents overflow
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: "1rem",
            fontWeight: "bold",
            color: "#fff",
            textAlign: "left",
            width: "100%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          {song.name}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontSize: "0.85rem",
            color: "#b3b3b3",
            textAlign: "left",
            width: "100%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            mb: 0.5,
          }}
        >
          {song.artist}
        </Typography>

        {/* Status Chip */}
        {/* <Chip
          icon={statusInfo.icon}
          label={statusInfo.text}
          size="small"
          sx={{
            height: 24,
            fontSize: "0.7rem",
            backgroundColor: statusInfo.color,
            color: "white",
            "& .MuiChip-icon": { color: "white" },
          }}
        /> */}
      </Box>

      {/* Request Button */}
      {/* <Tooltip
        title={isInCart ? "Already in your cart" : isRequestDisabled ? "Already requested" : `Request ${song.name}`}
        placement="top"
      > */}
        <span> {/* Wrap button for tooltip to work with disabled state */}
          <Button
            disabled={isDisabled}
            onClick={() => onRequestOpen(song)}
            startIcon={isRequestDisabled ? <AccessTime /> : <MusicNote />}
            sx={{
              backgroundColor: isDisabled ? "#555" : "#966300",
              color: "#fff",
              "&:hover": {
                backgroundColor: isDisabled ? "#555" : "#b17a00",
                transform: isDisabled ? "none" : "scale(1.05)",
              },
              fontWeight: "bold",
              fontSize: "0.8rem",
              px: 2,
              py: 1,
              borderRadius: "8px",
              transition: "all 0.2s ease",
              minWidth: "110px",
            }}
          >
            {isRequestDisabled ? formattedTime : "Request"}
          </Button>
        </span>
      {/* </Tooltip> */}
    </Box>
  );
};

export default SongCard;