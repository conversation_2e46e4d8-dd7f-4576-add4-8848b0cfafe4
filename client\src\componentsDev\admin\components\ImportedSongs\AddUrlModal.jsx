// AddUrlModal.jsx
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
} from "@mui/material";

const AddUrlModal = ({ open, onClose, video, urlTypes, onAdd }) => {
  const [selectedTypeId, setSelectedTypeId] = useState("");

  useEffect(() => {
    if (urlTypes.length > 0) {
      setSelectedTypeId(urlTypes[0].url_type_lookup_id);
    }
  }, [urlTypes]);

  const handleAdd = () => {
    if (video && selectedTypeId) {
      onAdd(video, selectedTypeId);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Select URL Type</DialogTitle>
      <DialogContent>
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel id="url-type-label">URL Type</InputLabel>
          <Select
            labelId="url-type-label"
            value={selectedTypeId}
            label="URL Type"
            onChange={(e) => setSelectedTypeId(e.target.value)}
          >
            {urlTypes.map((type) => (
              <MenuItem key={type.url_type_lookup_id} value={type.url_type_lookup_id}>
                {type.url_type_name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" onClick={handleAdd}>
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddUrlModal;