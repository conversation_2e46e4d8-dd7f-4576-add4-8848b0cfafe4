const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const PlaylistModel = {
  create: ({
    title,
    description,
    image = null,
    playlist_type_lookup_id,
    spotify_url = null,
  }) => {
    return new Promise((resolve, reject) => {
      const query = `
      INSERT INTO playlist_master (title, playlist_type_lookup_id, description, image, spotify_url, created_at)
      VALUES (?, ?, ?, ?, ?, datetime('now'))
    `;
      db.run(
        query,
        [title, playlist_type_lookup_id, description, image, spotify_url],
        function (err) {
          if (err) {
            console.error("Error inserting playlist:", err);
            return reject(err);
          }
          // Fetch the newly created playlist by its ID
          PlaylistModel.getById(this.lastID)
            .then((playlist) => resolve(playlist))
            .catch(reject);
        }
      );
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `
  SELECT p.*, 
         COUNT(ps.songs_master_id) AS songs_count
  FROM playlist_master p
  LEFT JOIN playlist_songs ps 
         ON p.playlist_master_id = ps.playlist_master_id
  GROUP BY p.playlist_master_id
`,
        [],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `
  SELECT p.*, 
         COUNT(ps.songs_master_id) AS songs_count
  FROM playlist_master p
  LEFT JOIN playlist_songs ps 
         ON p.playlist_master_id = ps.playlist_master_id
  WHERE p.playlist_master_id = ?
  GROUP BY p.playlist_master_id
`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (
    id,
    { title, description, image, playlist_type_lookup_id, spotify_url }
  ) => {
    return new Promise((resolve, reject) => {
      const query = `
      UPDATE playlist_master 
      SET title = ?, playlist_type_lookup_id = ?, description = ?, image = ?, spotify_url = ?
      WHERE playlist_master_id = ?
    `;
      db.run(
        query,
        [title, playlist_type_lookup_id, description, image, spotify_url, id],
        function (err) {
          if (err) return reject(err);
          resolve({ updated: this.changes });
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM playlist_master WHERE playlist_master_id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },
};

module.exports = PlaylistModel;
