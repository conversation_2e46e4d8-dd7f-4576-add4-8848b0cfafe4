import { Box, Collapse, IconButton, Typography } from "@mui/material";
import PopularSongList from "./PopularSongList";
import { usePopularSongs } from "../../../hooks/useSongs";
import { useState } from "react";
import { ExpandLess as ExpandLessIcon, ExpandMore as ExpandMoreIcon } from "@mui/icons-material";

const PopularSongMaster = () => {
    const [showPopularSongs, setShowPopularSongs] = useState(true);

    const { data: popularSongs = [] } = usePopularSongs();

    return (
        <Box
            sx={{
                px: 2,
                mb: 0,
                marginTop: showPopularSongs ? "2rem" : "0rem",
                display: "flex",
                flexDirection: "column",
            }}
        >
            <Box
                onClick={() => setShowPopularSongs((prev) => !prev)}
                sx={{
                    position: "sticky",
                    top: 110,
                    zIndex: 1,
                    backgroundColor: "background.default",
                    py: showPopularSongs ? 1 : 0.5,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    gap: "24px",
                    marginBottom: showPopularSongs ? "32px" : "8px",
                    width: "100% !important",
                    border: "1px solid #966300",
                    paddingLeft: "15px",
                    borderRadius: "5px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "24px",
                    }}
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        style={{ flexShrink: 0 }}
                    >
                        <path
                            fill="none"
                            stroke="#966300"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="1.5"
                            d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                        />
                    </svg>
                    <Typography
                        variant="h6"
                        sx={{
                            fontSize: "20px",
                            fontWeight: "bold",
                            color: "#FFF",
                            lineHeight: "1.2",
                        }}
                    >
                        Popular Songs{" "}
                        {popularSongs.length > 0 ? `(${popularSongs.length})` : ""}
                    </Typography>
                </Box>
                <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
                    {showPopularSongs ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
            </Box>
            <Collapse in={showPopularSongs} timeout="auto" unmountOnExit>
                <PopularSongList />
            </Collapse>
        </Box>
    );
};

export default PopularSongMaster;