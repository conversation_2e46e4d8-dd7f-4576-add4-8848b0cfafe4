import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Stack,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Grid,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Save as SaveIcon,
  Restore as RestoreIcon,
  Info as InfoIcon,
  Settings as SettingsIcon,
  Timer as TimerIcon,
  Score as ScoreIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import axios from 'axios';

// Styled components following SongQ design system
const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  marginBottom: '24px',
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiOutlinedInput-input': {
    color: '#fff',
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiSelect-select': {
    color: '#fff',
  },
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  ...(variant === 'contained' && {
    backgroundColor: '#4CAF50',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#45a049',
    },
  }),
  ...(variant === 'outlined' && {
    borderColor: '#333',
    color: '#ccc',
    '&:hover': {
      borderColor: '#4CAF50',
      backgroundColor: 'rgba(76, 175, 80, 0.1)',
    },
  }),
}));

const QuizSettings = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  const [settings, setSettings] = useState({
    // Default question settings
    defaultTimeLimit: 30,
    defaultPoints: 10,
    defaultDifficulty: '',
    
    // Validation rules
    minQuestionLength: 10,
    maxQuestionLength: 500,
    minAnswerLength: 1,
    maxAnswerLength: 200,
    minAnswersRequired: 2,
    maxAnswersAllowed: 6,
    
    // Scoring settings
    enableBonusPoints: true,
    enableTimeMultiplier: true,
    penaltyForWrongAnswer: 0,
    
    // Export/Import settings
    exportFormat: 'csv',
    includeAnswersInExport: true,
    includeDifficultyInExport: true,
    
    // Security settings
    requireApprovalForNewQuestions: false,
    allowDuplicateQuestions: false,
    enableQuestionVersioning: false,
    
    // UI settings
    showQuestionPreview: true,
    enableAutoSave: true,
    autoSaveInterval: 30
  });

  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/admin/questions/settings`);
      if (response.data) {
        setSettings(prev => ({ ...prev, ...response.data }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // Don't show error for missing settings - use defaults
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      await axios.post(`${process.env.REACT_APP_API_URL}/api/admin/questions/settings`, settings);
      enqueueSnackbar('Settings saved successfully!', { variant: 'success' });
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      enqueueSnackbar('Failed to save settings', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      defaultTimeLimit: 30,
      defaultPoints: 10,
      defaultDifficulty: '',
      minQuestionLength: 10,
      maxQuestionLength: 500,
      minAnswerLength: 1,
      maxAnswerLength: 200,
      minAnswersRequired: 2,
      maxAnswersAllowed: 6,
      enableBonusPoints: true,
      enableTimeMultiplier: true,
      penaltyForWrongAnswer: 0,
      exportFormat: 'csv',
      includeAnswersInExport: true,
      includeDifficultyInExport: true,
      requireApprovalForNewQuestions: false,
      allowDuplicateQuestions: false,
      enableQuestionVersioning: false,
      showQuestionPreview: true,
      enableAutoSave: true,
      autoSaveInterval: 30
    });
    setHasChanges(true);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" sx={{ color: '#fff', fontWeight: 600 }}>
          Quiz Settings
        </Typography>
        
        <Stack direction="row" spacing={2}>
          <ActionButton
            variant="outlined"
            startIcon={<RestoreIcon />}
            onClick={resetToDefaults}
            disabled={loading}
          >
            Reset to Defaults
          </ActionButton>
          
          <ActionButton
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={saveSettings}
            disabled={loading || !hasChanges}
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </ActionButton>
        </Stack>
      </Box>

      {hasChanges && (
        <Alert severity="warning" sx={{ mb: 3, backgroundColor: 'rgba(255, 152, 0, 0.1)' }}>
          You have unsaved changes. Don't forget to save your settings.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Default Question Settings */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <SettingsIcon sx={{ color: '#4CAF50' }} />
                Default Question Settings
              </Typography>
              
              <Stack spacing={3}>
                <StyledTextField
                  label="Default Time Limit (seconds)"
                  type="number"
                  value={settings.defaultTimeLimit}
                  onChange={(e) => updateSetting('defaultTimeLimit', parseInt(e.target.value) || 30)}
                  inputProps={{ min: 5, max: 600 }}
                  helperText="Default time limit for new questions"
                  fullWidth
                />
                
                <StyledTextField
                  label="Default Points"
                  type="number"
                  value={settings.defaultPoints}
                  onChange={(e) => updateSetting('defaultPoints', parseInt(e.target.value) || 10)}
                  inputProps={{ min: 1, max: 100 }}
                  helperText="Default point value for new questions"
                  fullWidth
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* Validation Rules */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon sx={{ color: '#2196F3' }} />
                Validation Rules
              </Typography>
              
              <Stack spacing={3}>
                <Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
                  <StyledTextField
                    label="Min Question Length"
                    type="number"
                    value={settings.minQuestionLength}
                    onChange={(e) => updateSetting('minQuestionLength', parseInt(e.target.value) || 10)}
                    inputProps={{ min: 1, max: 100 }}
                  />
                  
                  <StyledTextField
                    label="Max Question Length"
                    type="number"
                    value={settings.maxQuestionLength}
                    onChange={(e) => updateSetting('maxQuestionLength', parseInt(e.target.value) || 500)}
                    inputProps={{ min: 100, max: 1000 }}
                  />
                </Box>
                
                <Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
                  <StyledTextField
                    label="Min Answers Required"
                    type="number"
                    value={settings.minAnswersRequired}
                    onChange={(e) => updateSetting('minAnswersRequired', parseInt(e.target.value) || 2)}
                    inputProps={{ min: 1, max: 10 }}
                  />
                  
                  <StyledTextField
                    label="Max Answers Allowed"
                    type="number"
                    value={settings.maxAnswersAllowed}
                    onChange={(e) => updateSetting('maxAnswersAllowed', parseInt(e.target.value) || 6)}
                    inputProps={{ min: 2, max: 20 }}
                  />
                </Box>
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* Scoring Settings */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <ScoreIcon sx={{ color: '#FF9800' }} />
                Scoring Settings
              </Typography>
              
              <Stack spacing={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableBonusPoints}
                      onChange={(e) => updateSetting('enableBonusPoints', e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4CAF50',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#4CAF50',
                        },
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography sx={{ color: '#fff' }}>Enable Bonus Points</Typography>
                      <Typography variant="caption" sx={{ color: '#ccc' }}>
                        Allow difficulty-based bonus points
                      </Typography>
                    </Box>
                  }
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableTimeMultiplier}
                      onChange={(e) => updateSetting('enableTimeMultiplier', e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4CAF50',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#4CAF50',
                        },
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography sx={{ color: '#fff' }}>Enable Time Multiplier</Typography>
                      <Typography variant="caption" sx={{ color: '#ccc' }}>
                        Apply time-based scoring multipliers
                      </Typography>
                    </Box>
                  }
                />
                
                <StyledTextField
                  label="Penalty for Wrong Answer"
                  type="number"
                  value={settings.penaltyForWrongAnswer}
                  onChange={(e) => updateSetting('penaltyForWrongAnswer', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0, max: 50 }}
                  helperText="Points deducted for incorrect answers"
                  fullWidth
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* Export/Import Settings */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TimerIcon sx={{ color: '#9C27B0' }} />
                Export/Import Settings
              </Typography>
              
              <Stack spacing={3}>
                <StyledFormControl fullWidth>
                  <InputLabel>Export Format</InputLabel>
                  <Select
                    value={settings.exportFormat}
                    onChange={(e) => updateSetting('exportFormat', e.target.value)}
                    label="Export Format"
                  >
                    <MenuItem value="csv">CSV</MenuItem>
                    <MenuItem value="json">JSON</MenuItem>
                    <MenuItem value="xlsx">Excel</MenuItem>
                  </Select>
                </StyledFormControl>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.includeAnswersInExport}
                      onChange={(e) => updateSetting('includeAnswersInExport', e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4CAF50',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#4CAF50',
                        },
                      }}
                    />
                  }
                  label={
                    <Typography sx={{ color: '#fff' }}>Include Answers in Export</Typography>
                  }
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.includeDifficultyInExport}
                      onChange={(e) => updateSetting('includeDifficultyInExport', e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4CAF50',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#4CAF50',
                        },
                      }}
                    />
                  }
                  label={
                    <Typography sx={{ color: '#fff' }}>Include Difficulty in Export</Typography>
                  }
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default QuizSettings;
