{"name": "server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "schema:status": "node scripts/schema-cli.js status", "schema:sync": "node scripts/schema-cli.js sync", "schema:history": "node scripts/schema-cli.js history", "schema:info": "node scripts/schema-cli.js info", "schema:backup": "node scripts/schema-cli.js backup", "schema:validate": "node scripts/schema-cli.js validate", "schema:recover": "node scripts/schema-cli.js recover", "schema:help": "node scripts/schema-cli.js help"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.8", "bcryptjs": "^3.0.2", "chokidar": "^4.0.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-session": "^1.18.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3", "multer": "^2.0.1", "socket.io": "^4.8.1", "spotify-web-api-node": "^5.0.2", "sqlite3": "^5.1.7", "xlsx": "^0.18.5", "zod": "^4.1.3"}, "devDependencies": {"nodemon": "^3.1.10"}}