// PlaylistDetails.jsx
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Box } from '@mui/material';
import PlaylistHeader from './PlaylistHeader';
import SongList from './SongList';

const PlaylistDetails = ({ onSongSelect }) => {
  const { id } = useParams(); // This will get the playlist ID from the URL
  const [playlistDetails, setPlaylistDetails] = useState(null); // Added state for playlist details
  const [songs, setSongs] = useState([]);

  useEffect(() => {
    const fetchPlaylistDetails = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${id}`);
        const data = await response.json();
        setPlaylistDetails(data);
        setSongs(data.tracks);
      } catch (error) {
        console.error("Error fetching playlist details:", error);
      }
    };

    if (id) {
      fetchPlaylistDetails();
    }
  }, [id]);

  return (
    <Box>
      {playlistDetails && <PlaylistHeader details={playlistDetails} />}
      <SongList songs={songs} onSongSelect={onSongSelect} />
    </Box>
  );
};

export default PlaylistDetails;