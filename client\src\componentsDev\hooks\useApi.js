 
import { useMutation, useQuery } from '@tanstack/react-query';

// Generic hook for GET requests
export const useApi = (key, url, options = {}) => {
  return useQuery({
    queryKey: [key],
    queryFn: async () => {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    },
    ...options,
  });
};

// Generic hook for POST/PUT/DELETE requests
export const useApiMutation = (mutationFn, options = {}) => {
  return useMutation({
    mutationFn,
    ...options,
  });
};