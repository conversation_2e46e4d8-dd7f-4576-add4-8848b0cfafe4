const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const importSongController = require("../controllers/importSongController");

// Configure multer for file uploads
const upload = multer({
  dest: "uploads/",
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (ext !== ".xlsx" && ext !== ".xls") {
      return cb(new Error("Only Excel files (.xlsx, .xls) are allowed"));
    }
    cb(null, true);
  },
});

// Upload Excel file and import songs
router.post(
  "/upload-songs",
  upload.single("file"),
  importSongController.uploadExcel
);

// Get all imported songs
router.get("/songs", importSongController.getAllImportedSongs);

// Get a specific song by ID
router.get("/songs/:id", importSongController.getSongById);

// Get song with its Spotify data (tracks and artists)
router.get("/songs/:id/spotify-data", importSongController.getSongWithSpotifyData);

// Update a song entry by ID
router.put("/songs/:id", importSongController.updateSong);

// Delete a song entry by ID
router.delete("/songs/:id", importSongController.deleteSong);

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      return res.status(400).json({ error: "File too large. Maximum size is 10MB." });
    }
  }
  
  if (error.message === "Only Excel files (.xlsx, .xls) are allowed") {
    return res.status(400).json({ error: error.message });
  }
  
  res.status(500).json({ error: "File upload error" });
});

module.exports = router;