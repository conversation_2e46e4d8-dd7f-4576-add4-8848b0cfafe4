const express = require("express");
const router = express.Router();
const migrateController = require("../controllers/migrateTempToMainController");

// Static routes first
router.get("/migrated", migrateController.getAllMigratedTracks);

// Specific routes before parameterized routes
router.get("/check/:spotify_track_id", migrateController.checkIfMigrated);

// Parameterized routes in order of specificity
router.get("/migrated/:id", migrateController.getMigratedTrack);
router.delete("/migrated/:id", migrateController.deleteMigratedTrack);
// Ensure specific '/restore/all' is registered before the parameterized '/restore/:id'
router.post("/restore/all", migrateController.restoreAllToTemp);
router.post("/restore/:id", migrateController.restoreToTemp);

// Migration operations
router.post("/single/:id", migrateController.migrateSingle);
router.post("/all", migrateController.migrateAll);
router.delete("/temp/:id", migrateController.deleteTempTrack);


// New job status route(s)
// router.get("/job/:jobId", migrateController.getJobStatus);
// router.get("/job/:jobId/result", migrateController.getJobResult);


module.exports = router;