export const getImageUrl = (image) => {
  if (!image) return "https://placehold.co/50x50";

  // Handle File objects for preview
  if (image instanceof File) {
    return URL.createObjectURL(image);
  }

  // Handle Spotify URLs (both single and mosaic)
  if (typeof image === 'string' && image.includes("cdn")) return image;
  
  // if (typeof image === 'string' && image.includes("spotifycdn")) return image;

  // Handle local file paths
  if (typeof image === 'string' && image.startsWith("/uploads")) {
    return `${process.env.REACT_APP_API_URL}${image}`;
  }

  // Fallback
  return "https://placehold.co/50x50";
};
