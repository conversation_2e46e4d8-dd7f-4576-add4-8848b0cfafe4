// pages/PlaylistPage.jsx
import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Box, Grid, IconButton, CircularProgress, Alert, Typography } from "@mui/material";
import PlaylistHeader from "./PlaylistHeader";
import SongCard from "../../common/SongCard";
import ArrowCircleUpOutlinedIcon from "@mui/icons-material/ArrowCircleUpOutlined";
import NavigationBar from "../../navigation/Navbar";
import TabNavigation from "../../navigation/TabNavigation";
import { useSongCart } from "../../../../context/SongCartContext";
import { usePlaylistDetails, usePlaylistSongs } from "../../../hooks/usePlaylists";
import DedicationMaster from "../../modals/DedicationModal/DedicationMaster";
import SuccessModal from "../../modals/SuccessModal";
import ThankYouDialog from "../../modals/ThankDialog";
import { useSnackbar } from "notistack";
import { useActiveRequests } from '../../../hooks/useActiveRequests';
import { getDeviceId } from "../../../../utils/cookieUtils";
import { useQueryClient } from "@tanstack/react-query";

const PlaylistPage = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { cart, clearCart } = useSongCart();
    const [selectedSong, setSelectedSong] = useState(null);
    const [showDedicationModal, setShowDedicationModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showThankYouDialog, setShowThankYouDialog] = useState(false);
    const [requesterName, setRequesterName] = useState("");
    const { enqueueSnackbar } = useSnackbar();
    const [activeTab, setActiveTab] = useState("playlists");
    const queryClient = useQueryClient();

    // Using your useApi-based hooks
    const {
        data: playlistDetails,
        isLoading: isLoadingDetails,
        isError: isDetailsError,
        error: detailsError
    } = usePlaylistDetails(id, { enabled: !!id });

    const {
        data: songs = [],
        isLoading: isLoadingSongs,
        isError: isSongsError,
        error: songsError
    } = usePlaylistSongs(playlistDetails?.playlist_master_id, {
        enabled: !!playlistDetails?.playlist_master_id
    });

    const {
        data: activeRequests = {}, 
    } = useActiveRequests();

   
  const handleRequestSuccess = () => {
    // Invalidate and refetch active requests
    queryClient.invalidateQueries({ queryKey: ['activeRequests'] });
  };

    const handleRequestOpen = (song) => {
        setSelectedSong(song);
        setShowDedicationModal(true);
    };


    const handleSubmitRequest = async (dedicationData) => {
        try {
            const deviceId = getDeviceId();
            const { userName, songs } = dedicationData;

            // Validate the data structure
            if (!songs || !Array.isArray(songs) || songs.length === 0) {
                console.error("Invalid songs data received:", dedicationData);
                enqueueSnackbar("Invalid request data. Please try again.", {
                    variant: "error",
                });
                return;
            }

            const finalUserName = userName || "Anonymous";
            setRequesterName(finalUserName);

            // Check for existing active requests for all songs
            for (const song of songs) {
                const canRequestResponse = await fetch(
                    `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${song.songs_master_id}`
                );

                const canRequestData = await canRequestResponse.json();

                if (canRequestData.hasActiveRequest) {
                    enqueueSnackbar(`You already have an active request for song ID ${song.songs_master_id}`, {
                        variant: "warning",
                    });
                    return;
                }
            }

            // Submit the request for all songs
            const requestBody = {
                requester_name: finalUserName,
                device_id: deviceId,
                songs: songs.map(song => ({
                    songs_master_id: song.songs_master_id,
                    dedication_msg: song.dedication || "",
                    action_type: song.actionType,
                })),
            };

            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(requestBody),
                }
            );

            if (!response.ok) {
                throw new Error("Failed to submit request");
            }

            await response.json();
            setShowDedicationModal(false);
            setShowSuccessModal(true);
            handleRequestSuccess();

        } catch (error) {
            console.error("Failed to make request:", error);
            enqueueSnackbar("Failed to submit request. Please try again.", {
                variant: "error",
            });
        }
    };

    const handleSuccessModalClose = () => {
        setShowSuccessModal(false);
        setShowThankYouDialog(true);
    };

    const handleThankYouClose = () => {
        setShowThankYouDialog(false);
        clearCart();
    };

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    const handleTabClick = (tab) => {
        setActiveTab(tab);
        navigate(`/?activeTab=${tab}`);
    };

    // Combined loading state
    const isLoading = isLoadingDetails || isLoadingSongs;

    if (isLoading) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                bgcolor: 'background.default'
            }}>
                <CircularProgress sx={{ color: '#966300' }} />
            </Box>
        );
    }

    // Error states
    if (isDetailsError) {
        return (
            <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
                <NavigationBar />
                <Alert severity="error" sx={{ mt: 2 }}>
                    Error loading playlist: {detailsError?.message || 'Unknown error'}
                </Alert>
            </Box>
        );
    }

    if (isSongsError) {
        return (
            <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
                <NavigationBar />
                {playlistDetails && <PlaylistHeader details={playlistDetails} />}
                <Alert severity="error" sx={{ mt: 2 }}>
                    Error loading songs: {songsError?.message || 'Unknown error'}
                </Alert>
            </Box>
        );
    }

    return (
        <Box sx={{ bgcolor: "background.default", color: "text.primary", pb: 4, minHeight: '100vh' }}>
            <NavigationBar />

            {playlistDetails && <PlaylistHeader details={playlistDetails} />}

            <Box sx={{ padding: "16px", marginBottom: "4rem !important" }}>
                {songs.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 8 }}>
                        <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                            No songs available in this playlist
                        </Typography>
                    </Box>
                ) : (
                    <Grid container spacing={3}>
                        {songs.map((song) => (
                            <Grid item xs={12} sm={6} md={4} key={song.songs_master_id}>
                                <SongCard
                                    song={song}
                                    activeRequests={activeRequests}
                                    onRequestOpen={handleRequestOpen}
                                    cart={cart}
                                />
                            </Grid>
                        ))}
                    </Grid>
                )}
            </Box>

            {/* Dedication Modal */}
            {showDedicationModal && (
                <DedicationMaster
                    open={showDedicationModal}
                    onClose={() => setShowDedicationModal(false)}
                    onSubmit={handleSubmitRequest}
                    song={selectedSong}
                />
            )}

            {/* Success Modal */}
            {showSuccessModal && (
                <SuccessModal
                    open={showSuccessModal}
                    onClose={handleSuccessModalClose}
                    requesterName={requesterName}
                />
            )}

            {/* Thank You Dialog */}
            {showThankYouDialog && (
                <ThankYouDialog
                    open={showThankYouDialog}
                    onClose={handleThankYouClose}
                />
            )}

            <IconButton
                onClick={scrollToTop}
                sx={{
                    position: "fixed",
                    bottom: "100px",
                    right: "20px",
                    zIndex: 9999,
                    backgroundColor: "primary.main",
                    color: "white",
                    "&:hover": {
                        backgroundColor: "primary.dark",
                    },
                }}
            >
                <ArrowCircleUpOutlinedIcon />
            </IconButton>

            <TabNavigation activeTab={activeTab} handleTabClick={handleTabClick} />
        </Box>
    );
};

export default PlaylistPage;