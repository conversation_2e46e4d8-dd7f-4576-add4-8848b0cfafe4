import axios from "axios";
import { FIELD_MAP } from "./constants";

const fetchItems = async (type, currentShowAllOptions = true) => {
  if (type === 'settings') {
    return [
      {
        id: 'request_options_mode',
        name: 'Request Options Mode',
        description: 'Controls which request options are shown to users',
        value: currentShowAllOptions,
        type: 'toggle'
      }
    ];
  }
  // Quiz data is handled by QuizLookupManagement component directly
  if (type === 'quiz') return [];

  const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`);
  return response.data;
};

const createItem = async (type, item) => {
  await axios.post(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`, item);
};

const updateItem = async (type, id, item) => {
  await axios.put(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`, item);
};

const deleteItem = async (type, id) => {
  await axios.delete(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`);
};

const getSettings = async () => {
  return await axios.get(
    `${process.env.REACT_APP_API_URL}/api/settings/public/request-options-mode`
  );
};

const updateSettings = async (newValue) => {
  return await axios.put(
    `${process.env.REACT_APP_API_URL}/api/settings/request-options-mode`,
    { showAllOptions: newValue },
    { withCredentials: true }
  );
};

const getFieldMap = (type) => {
  return FIELD_MAP[type];
};

const getItemId = (item, type) => {
  const fields = FIELD_MAP[type];
  return item[fields.id];
};

const createPayload = (item, type) => {
  const fields = FIELD_MAP[type];
  return {
    name: item[fields.name],
    description: item[fields.description],
    status: item[fields.status],
    createdAt: item[fields.createdAt],
  };
};

export const lookupService = {
  fetchItems,
  createItem,
  updateItem,
  deleteItem,
  getSettings,
  updateSettings,
  getFieldMap,
  getItemId,
  createPayload,
};