const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();
const SchemaWatcher = require('./schemaWatcher');

class DatabaseManager {
  constructor(options = {}) {
    this.dbPath = options.dbPath || path.join(__dirname, '../../app.db');
    this.schemaPath = options.schemaPath || path.join(__dirname, 'schema.sql');
    this.schemaWatcher = null;
    this.db = null;
    
    this.options = {
      enableAutoSync: options.enableAutoSync !== false, // Default to true
      enableWatcher: options.enableWatcher !== false, // Default to true
      debounceMs: options.debounceMs || 2000,
      ...options
    };
  }

  async initialize() {
    try {
      console.log('🚀 Initializing database manager...');
      console.log(`📁 Database path: ${this.dbPath}`);
      console.log(`📄 Schema path: ${this.schemaPath}`);

      // Ensure database directory exists
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
        console.log(`📁 Created database directory: ${dbDir}`);
      }

      // Initialize schema watcher and synchronizer
      if (this.options.enableAutoSync) {
        this.schemaWatcher = new SchemaWatcher(this.dbPath, this.schemaPath, this.options);
        
        // Set up event listeners
        this.schemaWatcher.on('migrationCompleted', (result) => {
          console.log('🎉 Schema migration completed successfully');
          this.emit('schemaUpdated', result);
        });

        this.schemaWatcher.on('migrationFailed', (error) => {
          console.error('💥 Schema migration failed:', error);
          this.emit('schemaError', error);
        });

        await this.schemaWatcher.initialize();
      }

      // Initialize main database connection
      await this.initializeDatabase();

      console.log('✅ Database manager initialized successfully');
      return this;

    } catch (error) {
      console.error('❌ Failed to initialize database manager:', error);
      throw error;
    }
  }

  async initializeDatabase() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ Failed to connect to database:', err);
          reject(err);
        } else {
          console.log('✅ Connected to SQLite database');
          
          // Enable foreign keys
          this.db.run('PRAGMA foreign_keys = ON', (err) => {
            if (err) {
              console.error('⚠️  Failed to enable foreign keys:', err);
            } else {
              console.log('✅ Foreign keys enabled');
            }
            resolve();
          });
        }
      });
    });
  }

  getDatabase() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  async getSchemaStatus() {
    if (!this.schemaWatcher) {
      throw new Error('Schema watcher not enabled. Set enableAutoSync: true');
    }
    return await this.schemaWatcher.getSchemaStatus();
  }

  async getMigrationHistory() {
    if (!this.schemaWatcher) {
      throw new Error('Schema watcher not enabled. Set enableAutoSync: true');
    }
    return await this.schemaWatcher.getMigrationHistory();
  }

  async forceSchemaSync() {
    if (!this.schemaWatcher) {
      throw new Error('Schema watcher not enabled. Set enableAutoSync: true');
    }
    return await this.schemaWatcher.forceSync();
  }

  async executeQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async executeUpdate(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes, lastID: this.lastID });
        }
      });
    });
  }

  async beginTransaction() {
    return new Promise((resolve, reject) => {
      this.db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async commitTransaction() {
    return new Promise((resolve, reject) => {
      this.db.run('COMMIT', (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async rollbackTransaction() {
    return new Promise((resolve, reject) => {
      this.db.run('ROLLBACK', (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  // Simple event emitter functionality
  emit(event, data) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  on(event, callback) {
    if (!this.listeners) {
      this.listeners = {};
    }
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  async close() {
    console.log('🔄 Closing database manager...');
    
    if (this.schemaWatcher) {
      await this.schemaWatcher.close();
    }

    if (this.db) {
      return new Promise((resolve) => {
        this.db.close((err) => {
          if (err) {
            console.error('❌ Error closing database:', err);
          } else {
            console.log('✅ Database connection closed');
          }
          resolve();
        });
      });
    }
  }
}

// Create singleton instance
let databaseManager = null;

const initializeDatabase = async (options = {}) => {
  if (!databaseManager) {
    databaseManager = new DatabaseManager(options);
    await databaseManager.initialize();
  }
  return databaseManager;
};

const getDatabase = () => {
  if (!databaseManager) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return databaseManager.getDatabase();
};

const getDatabaseManager = () => {
  if (!databaseManager) {
    throw new Error('Database manager not initialized. Call initializeDatabase() first.');
  }
  return databaseManager;
};

module.exports = {
  DatabaseManager,
  initializeDatabase,
  getDatabase,
  getDatabaseManager
};
