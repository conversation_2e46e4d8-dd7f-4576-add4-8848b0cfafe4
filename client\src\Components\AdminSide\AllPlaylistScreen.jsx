import {
  <PERSON>ton,
  TextField,
  MenuItem,
  InputLabel,
  Select,
  FormControl,
  Typography,
  Box,
  Container,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import CommonModal from "./CommonModalComponent";
import { useState, useEffect, useMemo } from "react";
import Header from "./Header";
import axios from "axios";
import { getImageUrl } from '../../utils/imageHelper';

const AllPlaylistScreen = () => {
  const [open, setOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editId, setEditId] = useState(null);
  const [playlists, setPlaylists] = useState([]);
  const [availableSongs, setAvailableSongs] = useState([]);
  const [playlistTypes, setPlaylistTypes] = useState([]);
  const [selectedSongs, setSelectedSongs] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  // const [filteredSongs, setFilteredSongs] = useState([]);
  const [currentPlaylistId, setCurrentPlaylistId] = useState(null);
  const [playlistData, setPlaylistData] = useState({
    title: "",
    image: "",
    description: "",
    playlist_type_lookup_id: "",
  });

  const fetchAllData = async () => {
    const [songsRes, typesRes, playlistsRes] = await Promise.all([
      axios.get(`${process.env.REACT_APP_API_URL}/api/songs`),
      axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/playlist`),
      axios.get(`${process.env.REACT_APP_API_URL}/api/playlist`),
    ]);

    const playlistsWithSongs = await Promise.all(
      playlistsRes.data.map(async (playlist) => {
        const res = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlist.playlist_master_id}/songs`
        );
        return { ...playlist, songs: res.data };
      })
    );

    setAvailableSongs(songsRes.data);
    setPlaylistTypes(typesRes.data.filter((type) => type.status === 1));
    // setPlaylists(playlistsRes.data);
    setPlaylists(playlistsWithSongs);
  };

  const getTotalDuration = (songs = []) => {
    let totalSeconds = 0;

    songs.forEach((song) => {
      const [min, sec] = song.duration.split(":").map(Number);
      totalSeconds += min * 60 + sec;
    });

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours} hr ${minutes} min ${seconds} sec`;
    } else {
      return `${minutes} min ${seconds} sec`;
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const filteredSongs = useMemo(() => {
    if (!searchTerm.trim()) return availableSongs;
    return availableSongs.filter((song) =>
      song.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, availableSongs]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setPlaylistData({ ...playlistData, [name]: value });
  };

  const handleOpen = () => setOpen(true);

  const handleClose = () => {
    setOpen(false);
    setIsEditing(false);
    setEditId(null);
    setCurrentPlaylistId(null);
    setPlaylistData({
      title: "",
      image: "",
      description: "",
      playlist_type_lookup_id: "",
    });
    setSelectedSongs([]);
  };

  const handleCreate = async () => {
    try {
      const formData = new FormData();
      formData.append("title", playlistData.title);
      formData.append("description", playlistData.description);
      formData.append(
        "playlist_type_lookup_id",
        playlistData.playlist_type_lookup_id
      );
      formData.append("spotify_url", ""); // or your value

      // Handle both File objects and existing image paths
      if (playlistData.image instanceof File) {
        formData.append("image", playlistData.image); // send file
      } else if (typeof playlistData.image === 'string' && playlistData.image !== '') {
        // Always include the existing image path when editing and if it exists
        formData.append("image", playlistData.image); // keep existing image path
      }
      // If no image is set (empty string or null), don't append image field to keep existing image

      let playlistId;

      if (isEditing) {
        // Step 1: Update playlist
        const response = await axios.put(
          `${process.env.REACT_APP_API_URL}/api/playlist/${editId}`,
          formData,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        playlistId = editId;
        
        // Update the local state with the returned data
        setPlaylists(prev => prev.map(p => 
          p.playlist_master_id === editId ? { ...p, ...response.data } : p
        ));
      } else {
        // Step 1: Create playlist
        const res = await axios.post(
          `${process.env.REACT_APP_API_URL}/api/playlist`,
          formData,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        playlistId = res.data.playlist_master_id;
      }

      // Step 3: Attach selected songs
      if (selectedSongs.length > 0) {
        // For editing: Clear existing songs first
        if (isEditing) {
          await axios.delete(
            `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistId}`
          );
        }

        // Attach all selected songs
        await Promise.all(
          selectedSongs.map((song) =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/playlist-songs`, {
              playlist_master_id: playlistId,
              songs_master_id: song.songs_master_id,
            })
          )
        );
      }

      await fetchAllData();
      handleClose();
    } catch (err) {
      console.error("Error saving playlist:", err);
    }
  };

  const handleEdit = async (playlist) => {
    setPlaylistData({
      title: playlist.title,
      image: playlist.image,
      description: playlist.description,
      playlist_type_lookup_id: playlist.playlist_type_lookup_id,
    });

    const playlistId = playlist.playlist_master_id;
    setEditId(playlistId);
    setCurrentPlaylistId(playlistId);
    setIsEditing(true);

    try {
      const res = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistId}/songs`
      );
      const attachedSongs = await res.json();
      setSelectedSongs(attachedSongs); // ✅ Load pre-selected songs for edit mode
    } catch (err) {
      console.error("Failed to fetch attached songs", err);
    }

    setOpen(true);
  };

  const handleDelete = async (id) => {
    await axios.delete(`${process.env.REACT_APP_API_URL}/api/playlist/${id}`);
    await fetchAllData();
  };

  const handleAddSong = async (song) => {
    try {
      await axios.post(`${process.env.REACT_APP_API_URL}/api/playlist-songs`, {
        playlist_master_id: currentPlaylistId,
        songs_master_id: song.songs_master_id,
      });
      setSelectedSongs((prev) => [...prev, song]);
    } catch (err) {
      console.error("Error adding song:", err);
    }
  };

  const handleRemoveSong = async (song) => {
    try {
      await axios.delete(
        `${process.env.REACT_APP_API_URL}/api/playlist-songs/${currentPlaylistId}/${song.songs_master_id}`
      );
      setSelectedSongs((prev) =>
        prev.filter((s) => s.songs_master_id !== song.songs_master_id)
      );
    } catch (err) {
      console.error("Error removing song:", err);
    }
  };

  return (
    <>
      <Header />
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 2,
            pb: 2,
            borderBottom: "1px solid",
          }}
        >
          <Typography variant="h4">Playlist</Typography>
          <Button variant="contained" color="success" onClick={handleOpen}>
            Create New Playlist
          </Button>
          <CommonModal
            open={open}
            onClose={handleClose}
            title="Create Playlist"
          >
            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                label="Playlist Title"
                name="title"
                value={playlistData.title}
                onChange={handleChange}
                fullWidth
              />
              <Typography variant="subtitle1">Upload Playlist Image</Typography>
              <Button variant="outlined" component="label">
                Upload Image
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) setPlaylistData({ ...playlistData, image: file });
                  }}
                />
              </Button>
              {playlistData.image && (
                <Box mt={2}>
                  <img
                    src={getImageUrl(playlistData.image)}
                    alt="Preview"
                    style={{ width: "100%", borderRadius: 8 }}
                  />
                </Box>
              )}
              <FormControl fullWidth>
                <InputLabel id="playlist-type-label">Type</InputLabel>
                <Select
                  labelId="playlist-type-label"
                  name="playlist_type_lookup_id"
                  value={playlistData.playlist_type_lookup_id}
                  onChange={handleChange}
                  label="Type"
                  displayEmpty
                >
                  {playlistTypes.map((type) => (
                    <MenuItem
                      key={type.playlist_type_lookup_id}
                      value={type.playlist_type_lookup_id}
                    >
                      {type.playlist_type_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                label="Description"
                name="description"
                value={playlistData.description}
                onChange={handleChange}
                fullWidth
                multiline
                minRows={3}
              />
              <TextField
                label="Search Songs"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                fullWidth
                margin="normal"
              />

              <Typography variant="caption" sx={{ display: "block", mb: 1 }}>
                {selectedSongs.length} song
                {selectedSongs.length !== 1 ? "s" : ""} selected
              </Typography>

              <Box
                sx={{
                  maxHeight: 250,
                  overflowY: "auto",
                  border: "1px solid #ccc",
                  borderRadius: 2,
                  p: 1,
                }}
              >
                {filteredSongs.map((song) => {
                  const isAdded = selectedSongs.some(
                    (s) => s.songs_master_id === song.songs_master_id
                  );

                  return (
                    <Box
                      key={song.songs_master_id}
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      p={1}
                      borderBottom="1px solid #eee"
                    >
                      <Typography>{song.name}</Typography>
                      <Button
                        size="small"
                        variant="contained"
                        color={isAdded ? "error" : "primary"}
                        onClick={() =>
                          isAdded ? handleRemoveSong(song) : handleAddSong(song)
                        }
                      >
                        {isAdded ? "-" : "+"}
                      </Button>
                    </Box>
                  );
                })}
              </Box>
              {filteredSongs.length === 0 && (
                <Typography variant="body2" sx={{ mt: 1, color: "gray" }}>
                  No songs found.
                </Typography>
              )}
              <Button
                variant="contained"
                color="success"
                onClick={handleCreate}
              >
                {isEditing ? "Update Playlist" : "Create Playlist"}
              </Button>
            </Box>
          </CommonModal>
        </Box>
        {playlists.map((item) => (
          <Box
            key={item.playlist_master_id}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mt: 3,
            }}
          >
            <Box
              sx={{ display: "flex", alignItems: "center", columnGap: "1rem" }}
            >
              <Box
                component="img"
                src={getImageUrl(item.image)}
                alt="Preview"
                sx={{
                  borderRadius: 2,
                  border: "1px solid",
                  width: "50px",
                  height: "50px",
                }}
              />
              <Box>
                <Typography variant="h5" sx={{ fontSize: "18px" }}>
                  {item.title}
                </Typography>
                <Typography variant="h6" sx={{ fontSize: "15px" }}>
                  {item.songs?.length || 0} songs •{" "}
                  {getTotalDuration(item.songs)}
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{ display: "flex", alignItems: "center", columnGap: "1rem" }}
            >
              <EditIcon
                onClick={() => handleEdit(item)}
                sx={{ cursor: "pointer" }}
              />
              <DeleteIcon
                onClick={() => handleDelete(item.playlist_master_id)}
                sx={{ cursor: "pointer" }}
              />
            </Box>
          </Box>
        ))}
      </Container>
    </>
  );
};

export default AllPlaylistScreen;
