-- Database Schema for SongQ
-- Updated: July 22, 2025

-- ============================================================================
-- LOOKUP TABLES (Reference Data)
-- ============================================================================

-- Playlist Type Lookup Table
CREATE TABLE playlist_type_lookup (
    playlist_type_lookup_id INTEGER PRIMARY KEY AUTOINCREMENT,
    playlist_type_name TEXT NOT NULL UNIQUE,
    playlist_type_description TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- URL Type Lookup Table
CREATE TABLE url_type_lookup (
    url_type_lookup_id INTEGER PRIMARY KEY AUTOINCREMENT,
    url_type_name TEXT NOT NULL UNIQUE,
    url_type_description TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- File Type Lookup Table
CREATE TABLE file_type_lookup (
    file_type_lookup_id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_type_name TEXT NOT NULL UNIQUE,
    file_type_description TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Genre Type Lookup Table
CREATE TABLE genre_type_lookup (
    genre_type_lookup_id INTEGER PRIMARY KEY AUTOINCREMENT,
    genre_type_name TEXT NOT NULL UNIQUE,
    genre_type_description TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- MASTER DATA TABLES
-- ============================================================================

-- Artists Master Table
CREATE TABLE artists_master (
    artist_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    image TEXT
);

-- Songs Master Table
CREATE TABLE songs_master (
    songs_master_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    artist TEXT,
    album TEXT,
    release_year INTEGER,
    decade TEXT,
    image TEXT,
    duration TEXT,
    spotify_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Playlist Master Table
CREATE TABLE playlist_master (
    playlist_master_id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    image TEXT,
    playlist_type_lookup_id INTEGER NOT NULL,
    spotify_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (playlist_type_lookup_id) REFERENCES playlist_type_lookup(playlist_type_lookup_id)
);

-- ============================================================================
-- RELATIONSHIP TABLES
-- ============================================================================

-- Song Artists Junction Table
CREATE TABLE song_artists (
    songs_master_id INTEGER,
    artist_id TEXT,
    PRIMARY KEY (songs_master_id, artist_id),
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id),
    FOREIGN KEY (artist_id) REFERENCES artists_master(artist_id)
);

-- Playlist Songs Junction Table
CREATE TABLE playlist_songs (
    playlist_songs_id INTEGER PRIMARY KEY AUTOINCREMENT,
    playlist_master_id INTEGER NOT NULL,
    songs_master_id INTEGER NOT NULL,
    FOREIGN KEY (playlist_master_id) REFERENCES playlist_master(playlist_master_id),
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id),
    UNIQUE (playlist_master_id, songs_master_id)
);

-- Song Genres Junction Table
CREATE TABLE song_genres (
    song_genres_id INTEGER PRIMARY KEY AUTOINCREMENT,
    songs_master_id INTEGER NOT NULL,
    genre_type_lookup_id INTEGER NOT NULL,
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id),
    FOREIGN KEY (genre_type_lookup_id) REFERENCES genre_type_lookup(genre_type_lookup_id),
    UNIQUE (songs_master_id, genre_type_lookup_id)
);

-- ============================================================================
-- MEDIA AND URL TABLES
-- ============================================================================

-- Song URLs Table
CREATE TABLE song_urls (
    song_urls_id INTEGER PRIMARY KEY AUTOINCREMENT,
    songs_master_id INTEGER NOT NULL,
    song_url TEXT NOT NULL,
    url_type_lookup_id INTEGER,
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id),
    FOREIGN KEY (url_type_lookup_id) REFERENCES url_type_lookup(url_type_lookup_id)
);

-- Song Files Table
CREATE TABLE song_files (
    song_files_id INTEGER PRIMARY KEY AUTOINCREMENT,
    songs_master_id INTEGER NOT NULL,
    file TEXT NOT NULL,
    file_type_lookup_id INTEGER,
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id),
    FOREIGN KEY (file_type_lookup_id) REFERENCES file_type_lookup(file_type_lookup_id)
);

-- ============================================================================
-- SPOTIFY INTEGRATION TABLES
-- ============================================================================

-- Spotify Playlist Table
CREATE TABLE spotify_playlist (
    spotify_id INTEGER PRIMARY KEY AUTOINCREMENT,
    spotify_playlist_id TEXT UNIQUE NOT NULL,
    name TEXT,
    description TEXT,
    type TEXT,
    image TEXT,
    tracks INTEGER
);

-- Spotify Songs Table
CREATE TABLE spotify_songs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    spotify_song_id TEXT UNIQUE NOT NULL,
    playlist_id INTEGER,
    name TEXT,
    artist TEXT,
    album TEXT,
    release_year INTEGER,
    duration_ms INTEGER,
    image_url TEXT,
    karaoke_url TEXT,
    lyrics_url TEXT,
    FOREIGN KEY (playlist_id) REFERENCES spotify_playlist(spotify_id)
);

-- ============================================================================
-- OPERATIONAL TABLES
-- ============================================================================

-- Song Requests Table
CREATE TABLE song_requests (
    song_request_id INTEGER PRIMARY KEY AUTOINCREMENT,
    songs_master_id INTEGER NOT NULL,
    requester_name TEXT NOT NULL,
    dedication_msg TEXT DEFAULT "No dedication message",
    status TEXT DEFAULT "Pending",
    action_type TEXT NOT NULL,
    device_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME DEFAULT (datetime('now', '+1 hour')),
    fulfilled_at TEXT NOT NULL,
    FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id)
);

-- Admin Users Table
CREATE TABLE admin_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE,
    password TEXT
);

-- App Settings Table
CREATE TABLE app_settings (
    setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE showdown_user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    last_registration_time INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- TEMPORARY IMPORT TABLES
-- ============================================================================

-- Import Song Table Temp
CREATE TABLE ImportSongTableTemp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    song_name TEXT NOT NULL,
    artist_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- 'pending', 'completed', 'disabled'
    video_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Import Spotify Track Temp
CREATE TABLE ImportSpotifyTrackTemp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_song_id INTEGER NOT NULL,
    spotify_track_id TEXT,
    name TEXT,
    artist TEXT,
    album TEXT,
    release_year TEXT,
    duration TEXT,
    decade TEXT,
    image TEXT,
    is_migrated INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_song_id) REFERENCES ImportSongTableTemp(id) ON DELETE CASCADE
);

-- Import Spotify Artist Temp
CREATE TABLE ImportSpotifyArtistTemp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    import_song_id INTEGER NOT NULL,
    artist_id TEXT,
    name TEXT,
    image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_song_id) REFERENCES ImportSongTableTemp(id) ON DELETE CASCADE
);

-- Import Track URLs Temp
CREATE TABLE ImportTrackUrlsTemp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    track_id INTEGER NOT NULL,
    song_url TEXT NOT NULL,
    url_type_lookup_id INTEGER NOT NULL,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (track_id) REFERENCES ImportSpotifyTrackTemp(id) ON DELETE CASCADE,
    FOREIGN KEY (url_type_lookup_id) REFERENCES url_type_lookup(url_type_lookup_id),
    UNIQUE(track_id, url_type_lookup_id, song_url)
);

-- =========================================
-- Questions & Answers
-- ========================================= 

CREATE TABLE difficulty_level_lookup ( -- level lookup
  difficulty_id INTEGER PRIMARY KEY AUTOINCREMENT,
  level_name TEXT NOT NULL UNIQUE,   -- 'Easy','Medium','Hard','Very Hard', 'Extra Hard'
  display_image TEXT,
  bonus_points INTEGER NOT NULL DEFAULT 10,
  time_multiplier REAL DEFAULT 1.0,
  status INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE question_type_lookup (
  question_type_id INTEGER PRIMARY KEY AUTOINCREMENT,
  type_name TEXT NOT NULL UNIQUE, -- 'text', 'text_image', ...
  display_name TEXT NOT NULL,
  status INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE answer_type_lookup (
  answer_type_id INTEGER PRIMARY KEY AUTOINCREMENT,
  type_name TEXT NOT NULL UNIQUE, -- 'multiple_choice','true_false','yes_no'
  display_name TEXT NOT NULL,
  status INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- =========================================
-- QUIZ
-- =========================================
CREATE TABLE quiz_questions (
  question_id INTEGER PRIMARY KEY AUTOINCREMENT,
  songs_master_id INTEGER NOT NULL,        -- FK -> songs_master(songs_master_id)
  display_image TEXT,
  question_text TEXT NOT NULL,
  question_image_url TEXT,
  question_video_url TEXT,
  question_audio_url TEXT,
  question_type_id INTEGER NOT NULL,       -- FK -> question_type_lookup
  answer_type_id INTEGER NOT NULL,         -- FK -> answer_type_lookup
  difficulty_id INTEGER NOT NULL,          -- FK -> difficulty_level_lookup
  time_limit_seconds INTEGER DEFAULT 30,
  question_order INTEGER DEFAULT 1,
  created_by_admin INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (songs_master_id) REFERENCES songs_master(songs_master_id) ON DELETE CASCADE,
  FOREIGN KEY (question_type_id) REFERENCES question_type_lookup(question_type_id),
  FOREIGN KEY (answer_type_id) REFERENCES answer_type_lookup(answer_type_id),
  FOREIGN KEY (difficulty_id) REFERENCES difficulty_level_lookup(difficulty_id)
);

CREATE TABLE question_answers (
  answer_id INTEGER PRIMARY KEY AUTOINCREMENT,
  question_id INTEGER NOT NULL,           -- FK -> quiz_questions
  answer_text TEXT,
  answer_image_url TEXT,
  is_correct INTEGER DEFAULT 0,
  answer_order INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (question_id) REFERENCES quiz_questions(question_id) ON DELETE CASCADE
);

CREATE TABLE testing_db (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE zehahaha_db (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name_bb TEXT,
  col TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


-- ============================================================================
-- SQLITE SYSTEM TABLES
-- ============================================================================

-- Note: sqlite_sequence table is automatically created by SQLite for AUTOINCREMENT
-- No need to manually create it

-- ============================================================================
-- INDEXES
-- ============================================================================

-- Unique Index for ImportSongTableTemp
CREATE UNIQUE INDEX idx_unique_song_artist
ON ImportSongTableTemp(song_name, artist_name);

-- Performance indexes for core tables
CREATE INDEX idx_songs_master_name ON songs_master(name);
CREATE INDEX idx_songs_master_artist ON songs_master(artist);
CREATE INDEX idx_songs_master_spotify_id ON songs_master(spotify_id);
CREATE INDEX idx_playlist_songs_playlist ON playlist_songs(playlist_master_id);
CREATE INDEX idx_playlist_songs_song ON playlist_songs(songs_master_id);
CREATE INDEX idx_song_urls_song ON song_urls(songs_master_id);
CREATE INDEX idx_song_files_song ON song_files(songs_master_id);
CREATE INDEX idx_song_genres_song ON song_genres(songs_master_id);
CREATE INDEX idx_song_genres_genre ON song_genres(genre_type_lookup_id);
CREATE INDEX idx_song_artists_song ON song_artists(songs_master_id);
CREATE INDEX idx_song_artists_artist ON song_artists(artist_id);

-- Indexes for operational tables
CREATE INDEX idx_song_requests_status ON song_requests(status);
CREATE INDEX idx_song_requests_created ON song_requests(created_at);
CREATE INDEX idx_song_requests_expires ON song_requests(expires_at);
CREATE INDEX idx_song_requests_device ON song_requests(device_id);


-- Indexes for import tables
CREATE INDEX idx_import_spotify_track_migrated ON ImportSpotifyTrackTemp(is_migrated);
CREATE INDEX idx_import_spotify_track_song ON ImportSpotifyTrackTemp(import_song_id);
CREATE INDEX idx_import_spotify_artist_song ON ImportSpotifyArtistTemp(import_song_id);
CREATE INDEX idx_import_track_urls_track ON ImportTrackUrlsTemp(track_id);

-- Indexes for Spotify integration
CREATE INDEX idx_spotify_songs_playlist ON spotify_songs(playlist_id);
CREATE INDEX idx_spotify_songs_name ON spotify_songs(name);

CREATE INDEX IF NOT EXISTS idx_question_answers_q ON question_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_song ON quiz_questions(songs_master_id);
CREATE INDEX idx_spotify_songs_artist ON spotify_songs(artist);

-- -- Seed difficulties (idempotent)
-- INSERT OR IGNORE INTO difficulty_level_lookup (difficulty_id, level_name, base_points, time_multiplier,  status)
-- VALUES
--   (1, 'Easy', 10, 1.0, 1, 1),
--   (2, 'Medium', 20, 1.1, 2, 1),
--   (3, 'Hard', 30, 1.2, 3, 1),
--   (4, 'Very Hard', 40, 1.3, 4, 1);

-- -- Seed question types (ensure 'text' exists)
-- INSERT OR IGNORE INTO question_type_lookup (question_type_id, type_name, display_name, status)
-- VALUES
--   (1, 'text', 'Text Only', 1);

-- -- Seed answer types (ensure 'multiple_choice' exists) 
-- INSERT OR IGNORE INTO answer_type_lookup (  type_name, display_name, status)
-- VALUES
--   ('single_choice', 'Single Choice', 1),
--   ('multiple_choice', 'Multiple Choice', 1),
--   ('true_false', 'True / False', 1);