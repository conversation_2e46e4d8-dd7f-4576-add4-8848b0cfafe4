// Simple test script to verify the settings API
// Run this with: node test-settings-api.js

const API_BASE = 'http://127.0.0.1:3001'; // Adjust port as needed

async function testSettingsAPI() {
  console.log('🧪 Testing Settings API...\n');

  try {
    // Test 1: Get current settings (should default to true)
    console.log('1️⃣ Testing GET request-options-mode...');
    const getResponse = await fetch(`${API_BASE}/api/settings/public/request-options-mode`);
    const getData = await getResponse.json();
    console.log('✅ GET Response:', getData);
    console.log('Expected: { showAllOptions: true }\n');

    // Test 2: Update to false
    console.log('2️⃣ Testing PUT request-options-mode (set to false)...');
    const putResponse1 = await fetch(`${API_BASE}/api/settings/request-options-mode`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ showAllOptions: false }),
      credentials: 'include'
    });
    const putData1 = await putResponse1.json();
    console.log('✅ PUT Response:', putData1);
    console.log('Expected: { showAllOptions: false }\n');

    // Test 3: Get updated settings
    console.log('3️⃣ Testing GET after update...');
    const getResponse2 = await fetch(`${API_BASE}/api/settings/public/request-options-mode`);
    const getData2 = await getResponse2.json();
    console.log('✅ GET Response:', getData2);
    console.log('Expected: { showAllOptions: false }\n');

    // Test 4: Update back to true
    console.log('4️⃣ Testing PUT request-options-mode (set to true)...');
    const putResponse2 = await fetch(`${API_BASE}/api/settings/request-options-mode`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ showAllOptions: true }),
      credentials: 'include'
    });
    const putData2 = await putResponse2.json();
    console.log('✅ PUT Response:', putData2);
    console.log('Expected: { showAllOptions: true }\n');

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSettingsAPI();
