import React, { useState, useEffect, useCallback } from 'react';
import {
  ChevronRight,
  ExpandMore,
  Add,
  Edit,
  Delete,
  CloudUpload,
  Image as ImageIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormHelperText,
  Modal,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import axios from 'axios';
import { useSnackbar } from 'notistack';

// Styled components
const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[900],
  color: theme.palette.common.white,
  '&:before': {
    display: 'none',
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.grey[800],
  },
  '&:hover': {
    backgroundColor: theme.palette.grey[700],
  },
}));

const StyledTableHeader = styled(TableRow)(({ theme }) => ({
  backgroundColor: theme.palette.grey[800],
}));

const ImagePreview = styled('img')({
  height: '48px',
  width: '48px',
  objectFit: 'contain',
  borderRadius: '4px',
  border: '1px solid #444',
});

const UploadButton = styled(Button)({
  textTransform: 'none',
  marginRight: '8px',
});

// Modal styling following SongQ design system
const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "90%",
  maxWidth: 600,
  maxHeight: "80vh",
  bgcolor: "#1A1A1A",
  border: "1px solid #333",
  borderRadius: "12px",
  boxShadow: 24,
  p: 0,
  display: "flex",
  flexDirection: "column",
};

const StyledModal = styled(Modal)(({ theme }) => ({
  '& .MuiBackdrop-root': {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiOutlinedInput-input': {
    color: '#fff',
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiSelect-select': {
    color: '#fff',
  },
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  padding: '10px 20px',
  ...(variant === 'contained' && {
    backgroundColor: '#4CAF50',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#45a049',
    },
  }),
  ...(variant === 'outlined' && {
    borderColor: '#333',
    color: '#ccc',
    '&:hover': {
      borderColor: '#4CAF50',
      backgroundColor: 'rgba(76, 175, 80, 0.1)',
    },
  }),
}));

// Helper function to get full image URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;
  if (imagePath.startsWith('http')) return imagePath;
  return `${process.env.REACT_APP_API_URL}${imagePath}`;
};

// Modal Edit Form Component
const EditModal = ({ open, onClose, editingItem, lookupType, onSave }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data when modal opens or editing item changes
  useEffect(() => {
    if (open) {
      if (editingItem === 'CREATE') {
        // Initialize with default form data for create mode
        const defaultForm = {
          difficulty: {
            level_name: '',
            display_image: '',
            bonus_points: 10,
            time_multiplier: 1.0,
            status: 1
          },
          'question-type': {
            type_name: '',
            display_name: '',
            status: 1
          },
          'answer-type': {
            type_name: '',
            display_name: '',
            status: 1
          },
          'question-mood': {
            mood_name: '',
            display_name: '',
            status: 1
          }
        };
        setFormData(defaultForm[lookupType] || {});
      } else if (editingItem) {
        setFormData({ ...editingItem });
      }
      setErrors({});
    }
  }, [open, editingItem, lookupType]);

  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (lookupType === 'difficulty') {
      if (!formData.level_name?.trim()) {
        newErrors.level_name = 'Level name is required';
      }
      if (formData.bonus_points < 0) {
        newErrors.bonus_points = 'Bonus points must be 0 or greater';
      }
      if (formData.time_multiplier <= 0) {
        newErrors.time_multiplier = 'Time multiplier must be greater than 0';
      }
    } else if (lookupType === 'question-mood') {
      if (!formData.mood_name?.trim()) {
        newErrors.mood_name = 'Mood name is required';
      }
      if (!formData.display_name?.trim()) {
        newErrors.display_name = 'Display name is required';
      }
    } else {
      if (!formData.type_name?.trim()) {
        newErrors.type_name = 'Type name is required';
      }
      if (!formData.display_name?.trim()) {
        newErrors.display_name = 'Display name is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      enqueueSnackbar('Please fix the form errors', { variant: 'error' });
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
      const action = editingItem === 'CREATE' ? 'created' : 'updated';
      enqueueSnackbar(`Item ${action} successfully!`, { variant: 'success' });
    } catch (error) {
      console.error('Error saving:', error);
      enqueueSnackbar(`Error saving: ${error.message}`, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({});
    setErrors({});
    onClose();
  };

  if (!editingItem) return null;

  return (
    <StyledModal open={open} onClose={handleClose}>
      <Box sx={modalStyle}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 3,
            borderBottom: "1px solid #333",
            flexShrink: 0,
          }}
        >
          <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
            {editingItem === 'CREATE' ? 'Create' : 'Edit'} {
              lookupType === 'difficulty' ? 'Difficulty Level' :
              lookupType === 'question-type' ? 'Question Type' :
              lookupType === 'answer-type' ? 'Answer Type' :
              lookupType === 'question-mood' ? 'Question Mood' : 'Item'
            }
          </Typography>
          <IconButton onClick={handleClose} sx={{ color: '#ccc' }}>
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Scrollable content */}
        <Box
          sx={{
            overflowY: "auto",
            flexGrow: 1,
            p: 3,
          }}
        >
          <Box display="flex" flexDirection="column" gap={3}>
            {lookupType === 'difficulty' ? (
              <>
                <StyledTextField
                  label="Level Name"
                  value={formData.level_name || ''}
                  onChange={(e) => handleFieldChange('level_name', e.target.value)}
                  error={!!errors.level_name}
                  helperText={errors.level_name}
                  fullWidth
                  required
                />

                <ImageUpload
                  value={formData.display_image || ''}
                  onChange={(value) => handleFieldChange('display_image', value)}
                  disabled={loading}
                />

                <StyledTextField
                  label="Bonus Points"
                  type="number"
                  value={formData.bonus_points || 0}
                  onChange={(e) => handleFieldChange('bonus_points', parseFloat(e.target.value) || 0)}
                  error={!!errors.bonus_points}
                  helperText={errors.bonus_points}
                  inputProps={{ min: 0 }}
                  fullWidth
                  required
                />

                <StyledTextField
                  label="Time Multiplier"
                  type="number"
                  value={formData.time_multiplier || 1.0}
                  onChange={(e) => handleFieldChange('time_multiplier', parseFloat(e.target.value) || 1.0)}
                  error={!!errors.time_multiplier}
                  helperText={errors.time_multiplier}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  fullWidth
                  required
                />
              </>
            ) : lookupType === 'question-mood' ? (
              <>
                <StyledTextField
                  label="Mood Name"
                  value={formData.mood_name || ''}
                  onChange={(e) => handleFieldChange('mood_name', e.target.value)}
                  error={!!errors.mood_name}
                  helperText={errors.mood_name}
                  fullWidth
                  required
                />

                <StyledTextField
                  label="Display Name"
                  value={formData.display_name || ''}
                  onChange={(e) => handleFieldChange('display_name', e.target.value)}
                  error={!!errors.display_name}
                  helperText={errors.display_name}
                  fullWidth
                  required
                />
              </>
            ) : (
              <>
                <StyledTextField
                  label="Type Name"
                  value={formData.type_name || ''}
                  onChange={(e) => handleFieldChange('type_name', e.target.value)}
                  error={!!errors.type_name}
                  helperText={errors.type_name}
                  fullWidth
                  required
                />

                <StyledTextField
                  label="Display Name"
                  value={formData.display_name || ''}
                  onChange={(e) => handleFieldChange('display_name', e.target.value)}
                  error={!!errors.display_name}
                  helperText={errors.display_name}
                  fullWidth
                  required
                />
              </>
            )}

            <StyledFormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status || 1}
                onChange={(e) => handleFieldChange('status', parseInt(e.target.value))}
                label="Status"
              >
                <MenuItem value={1}>Active</MenuItem>
                <MenuItem value={0}>Inactive</MenuItem>
              </Select>
            </StyledFormControl>
          </Box>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            p: 3,
            borderTop: "1px solid #333",
            flexShrink: 0,
          }}
        >
          <ActionButton
            variant="outlined"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </ActionButton>
          <ActionButton
            variant="contained"
            onClick={handleSave}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} color="inherit" /> : null}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </ActionButton>
        </Box>
      </Box>
    </StyledModal>
  );
};

// Image upload component
const ImageUpload = ({ value, onChange, disabled = false }) => {
  const [uploading, setUploading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const handleUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/upload/quiz`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      onChange(response.data.imageUrl);
      enqueueSnackbar('Image uploaded successfully!', { variant: 'success' });
    } catch (error) {
      console.error('Error uploading image:', error);
      enqueueSnackbar('Error uploading image. Please try again.', { variant: 'error' });
    } finally {
      setUploading(false);
    }
  };

  return (
    <Box mb={2}>
      <Typography variant="subtitle2" gutterBottom color="white">
        Display Image
      </Typography>

      {value && (
        <Box display="flex" alignItems="center" mb={1}>
          <ImageIcon sx={{ mr: 1, color: 'rgba(255,255,255,0.7)' }} fontSize="small" />
          <Typography variant="body2" color="rgba(255,255,255,0.7)" noWrap>
            {value.split('/').pop()}
          </Typography>
        </Box>
      )}

      <Box display="flex" alignItems="center">
        <UploadButton
          variant="outlined"
          component="label"
          disabled={disabled || uploading}
          startIcon={<CloudUpload />}
          color="primary"
        >
          {uploading ? 'Uploading...' : 'Upload Image'}
          <input
            type="file"
            accept="image/*"
            onChange={handleUpload}
            hidden
            disabled={disabled || uploading}
          />
        </UploadButton>

        {value && !disabled && (
          <Button
            variant="contained"
            color="error"
            onClick={() => onChange('')}
          >
            Remove
          </Button>
        )}
      </Box>

      {value && (
        <Box mt={1}>
          <ImagePreview
            src={getImageUrl(value)}
            alt="Preview"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        </Box>
      )}
    </Box>
  );
};

// Enhanced lookupService with image upload support
const lookupService = {
  fetchItems: async (type) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type}:`, error);
      throw error;
    }
  },

  createItem: async (type, item) => {
    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`, item);
      return response.data;
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      throw error;
    }
  },

  updateItem: async (type, id, item) => {
    try {
      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`, item);
      return response.data;
    } catch (error) {
      console.error(`Error updating ${type}:`, error);
      throw error;
    }
  },

  deleteItem: async (type, id) => {
    try {
      const response = await axios.delete(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      throw error;
    }
  },

  getFieldMap(type) {
    const fieldMaps = {
      difficulty: {
        name: 'level_name',
        description: 'display_image',
        id: 'difficulty_id',
        status: 'status',
        bonus_points: 'bonus_points',
        time_multiplier: 'time_multiplier'
      },
      'question-type': {
        name: 'type_name',
        description: 'display_name',
        id: 'question_type_id',
        status: 'status'
      },
      'answer-type': {
        name: 'type_name',
        description: 'display_name',
        id: 'answer_type_id',
        status: 'status'
      },
      'question-mood': {
        name: 'mood_name',
        description: 'display_name',
        id: 'mood_type_id',
        status: 'status'
      }
    };
    return fieldMaps[type] || { name: 'name', description: 'description', id: 'id' };
  },

  getDisplayHeaders(type) {
    const headers = {
      difficulty: ['ID', 'Level Name', 'Display Image', 'Bonus Points', 'Time Multiplier', 'Status'],
      'question-type': ['ID', 'Type Name', 'Display Name', 'Status'],
      'answer-type': ['ID', 'Type Name', 'Display Name', 'Status'],
      'question-mood': ['ID', 'Mood Name', 'Display Name', 'Status']
    };
    return headers[type] || ['ID', 'Name', 'Description', 'Status'];
  },

  getItemId(item, type) {
    const fieldMap = this.getFieldMap(type);
    return item[fieldMap.id] || item.id;
  },

  createPayload(item, type) {
    console.log('createPayload called with:', { item, type });
    const fields = this.getFieldMap(type);
    console.log('Field mapping for type:', type, fields);

    if (type === 'difficulty') {
      // Backend expects: { name, description, status, bonus_points, time_multiplier }
      const payload = {
        name: item.level_name || item[fields.name] || '',
        description: item.display_image || item[fields.description] || '',
        bonus_points: Number(item.bonus_points) || Number(item[fields.bonus_points]) || 10,
        time_multiplier: Number(item.time_multiplier) || Number(item[fields.time_multiplier]) || 1.0,
        status: Number(item.status) || Number(item[fields.status]) || 0
      };

      // Ensure required fields are not empty
      if (!payload.name) {
        throw new Error('Level name is required');
      }

      console.log('Difficulty payload created:', payload);
      return payload;
    } else if (type === 'question-mood') {
      // Backend expects: { name, description, status } for question-mood
      const payload = {
        name: item.mood_name || item[fields.name] || '',
        description: item.display_name || item[fields.description] || '',
        status: Number(item.status) || Number(item[fields.status]) || 0
      };

      // Ensure required fields are not empty
      if (!payload.name) {
        throw new Error('Mood name is required');
      }
      if (!payload.description) {
        throw new Error('Display name is required');
      }

      console.log('Question mood payload created:', payload);
      return payload;
    } else {
      // Backend expects: { name, description, status } for other types
      const payload = {
        name: item.type_name || item[fields.name] || '',
        description: item.display_name || item[fields.description] || '',
        status: Number(item.status) || Number(item[fields.status]) || 0
      };

      // Ensure required fields are not empty
      if (!payload.name) {
        throw new Error('Type name is required');
      }
      if (!payload.description) {
        throw new Error('Display name is required');
      }

      console.log('Other type payload created:', payload);
      return payload;
    }
  }
};

const QuizLookupManagement = () => {
  const [expanded, setExpanded] = useState(null);
  const [editModal, setEditModal] = useState({ open: false, item: null, type: null });
  const [loading, setLoading] = useState({});
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, type: null, id: null });
  const { enqueueSnackbar } = useSnackbar();

  // State for lookup data
  const [lookupData, setLookupData] = useState({
    difficulty: [],
    'question-type': [],
    'answer-type': [],
    'question-mood': []
  });

  // Form states
  const [forms, setForms] = useState({
    difficulty: {
      level_name: '',
      display_image: '',
      bonus_points: 10,
      time_multiplier: 1.0,
      status: 1
    },
    'question-type': {
      type_name: '',
      display_name: '',
      status: 1
    },
    'answer-type': {
      type_name: '',
      display_name: '',
      status: 1
    },
    'question-mood': {
      mood_name: '',
      display_name: '',
      status: 1
    }
  });

  // Form validation states
  const [errors, setErrors] = useState({
    difficulty: {},
    'question-type': {},
    'answer-type': {},
    'question-mood': {}
  });

  const loadAllData = useCallback(async () => {
    try {
      const types = ['difficulty', 'question-type', 'answer-type', 'question-mood'];
      const promises = types.map(async (type) => {
        setLoading(prev => ({ ...prev, [type]: true }));
        const data = await lookupService.fetchItems(type);
        setLoading(prev => ({ ...prev, [type]: false }));
        return { type, data };
      });

      const results = await Promise.all(promises);
      const newData = {};
      results.forEach(({ type, data }) => {
        newData[type] = data;
      });

      setLookupData(newData);
    } catch (error) {
      console.error('Error loading data:', error);
      enqueueSnackbar('Error loading data. Please refresh the page.', { variant: 'error' });
    }
  }, [enqueueSnackbar]);

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  const handleAccordionChange = (panel) => (_, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const validateForm = (type) => {
    const form = forms[type];
    const fields = lookupService.getFieldMap(type);
    const newErrors = {};

    if (!form[fields.name] || form[fields.name].trim() === '') {
      newErrors[fields.name] = 'This field is required';
    }

    if (!form[fields.description] || form[fields.description].trim() === '') {
      newErrors[fields.description] = 'This field is required';
    }

    if (type === 'difficulty') {
      if (!form.bonus_points || form.bonus_points <= 0) {
        newErrors.bonus_points = 'Must be a positive number';
      }

      if (!form.time_multiplier || form.time_multiplier <= 0) {
        newErrors.time_multiplier = 'Must be a positive number';
      }
    }

    setErrors(prev => ({ ...prev, [type]: newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  // Generic CRUD handlers
  const handleCreate = async (type, formData = null) => {
    // If formData is provided, use it (from modal), otherwise use forms state (from old form)
    const dataToUse = formData || forms[type];

    if (!formData && !validateForm(type)) {
      enqueueSnackbar('Please fix the form errors', { variant: 'error' });
      return;
    }

    try {
      setLoading(prev => ({ ...prev, [type]: true }));

      const payload = lookupService.createPayload(dataToUse, type);
      await lookupService.createItem(type, payload);

      // Refresh data
      const updatedData = await lookupService.fetchItems(type);
      setLookupData(prev => ({ ...prev, [type]: updatedData }));

      // Reset form only if using old form system
      if (!formData) {
        setForms(prev => ({
          ...prev,
          [type]: getDefaultForm(type)
        }));
      }

      enqueueSnackbar(`${type.charAt(0).toUpperCase() + type.slice(1)} created successfully!`, { variant: 'success' });
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      enqueueSnackbar(`Error creating ${type}: ${error.message}`, { variant: 'error' });
      throw error; // Re-throw for modal error handling
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleUpdate = async (type, id, updatedData) => {
    try {
      console.log('handleUpdate called with:', { type, id, updatedData });
      setLoading(prev => ({ ...prev, [type]: true }));

      // Enhanced validation with detailed error messages
      if (type === 'difficulty') {
        if (!updatedData.level_name || updatedData.level_name.trim() === '') {
          throw new Error('Level name is required and cannot be empty');
        }
        if (updatedData.bonus_points < 0) {
          throw new Error('Bonus points must be 0 or greater');
        }
        if (updatedData.time_multiplier <= 0) {
          throw new Error('Time multiplier must be greater than 0');
        }
      } else if (type === 'question-type' || type === 'answer-type') {
        if (!updatedData.type_name || updatedData.type_name.trim() === '') {
          throw new Error('Type name is required and cannot be empty');
        }
        if (!updatedData.display_name || updatedData.display_name.trim() === '') {
          throw new Error('Display name is required and cannot be empty');
        }
      }

      // Ensure status is properly converted
      if (updatedData.status !== undefined) {
        updatedData.status = parseInt(updatedData.status, 10);
      }

      const payload = lookupService.createPayload(updatedData, type);
      console.log('Created payload:', payload);

      const response = await lookupService.updateItem(type, id, payload);
      console.log('Update response:', response);

      // Refresh the data to get the latest from server
      await loadAllData();

      // Don't show success message here as it's handled in EditModal
      return response;
    } catch (error) {
      console.error(`Error updating ${type}:`, error);

      // Enhanced error handling for different error types
      let errorMessage = error.message;
      if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Item not found. It may have been deleted by another user.';
      }

      throw new Error(errorMessage);
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  // Modal handlers
  const handleEditClick = (type, item) => {
    setEditModal({ open: true, item, type });
  };

  const handleCreateClick = (type) => {
    const defaultForm = getDefaultForm(type);
    setEditModal({ open: true, item: 'CREATE', type, defaultForm });
  };

  const handleModalClose = () => {
    setEditModal({ open: false, item: null, type: null });
  };

  const handleModalSave = async (updatedData) => {
    const { type, item } = editModal;

    if (item === 'CREATE') {
      // Create new item
      await handleCreate(type, updatedData);
    } else {
      // Update existing item
      const id = lookupService.getItemId(item, type);
      await handleUpdate(type, id, updatedData);
    }
  };

  const handleDelete = async (type, id) => {
    try {
      setLoading(prev => ({ ...prev, [type]: true }));

      await lookupService.deleteItem(type, id);

      // Update local state
      setLookupData(prev => ({
        ...prev,
        [type]: prev[type].filter(item => lookupService.getItemId(item, type) !== id)
      }));

      setDeleteConfirm({ open: false, type: null, id: null });
      enqueueSnackbar(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`, { variant: 'success' });
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      enqueueSnackbar(`Error deleting ${type}: ${error.message}`, { variant: 'error' });
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const getDefaultForm = (type) => {
    const defaults = {
      difficulty: {
        level_name: '',
        display_image: '',
        bonus_points: 10,
        time_multiplier: 1.0,
        status: 1
      },
      'question-type': {
        type_name: '',
        display_name: '',
        status: 1
      },
      'answer-type': {
        type_name: '',
        display_name: '',
        status: 1
      },
      'question-mood': {
        mood_name: '',
        display_name: '',
        status: 1
      }
    };
    return defaults[type];
  };

  const updateForm = (type, field, value) => {
    setForms(prev => ({
      ...prev,
      [type]: { ...prev[type], [field]: value }
    }));

    // Clear error when field is updated
    if (errors[type][field]) {
      setErrors(prev => ({
        ...prev,
        [type]: { ...prev[type], [field]: '' }
      }));
    }
  };

  // Simple Data Table Component with Modal Editing
  const SimpleDataTable = ({ headers, data, onEdit, onDelete, tableType, isLoading }) => {
    if (isLoading) {
      return (
        <Box mt={3} display="flex" justifyContent="center" alignItems="center" py={4}>
          <CircularProgress size={24} sx={{ mr: 2 }} />
          <Typography variant="body2" color="grey.400">
            Loading data...
          </Typography>
        </Box>
      );
    }

    if (data.length === 0) {
      return (
        <Box mt={3} textAlign="center" py={4}>
          <Typography variant="body2" color="grey.400">
            No records found. Add your first {tableType} above!
          </Typography>
        </Box>
      );
    }

    return (
      <Box mt={1}>
        <TableContainer component={Paper} sx={{ backgroundColor: 'grey.900', border: '1px solid', borderColor: 'grey.700' }}>
          <Table>
            <TableHead>
              <StyledTableHeader>
                {headers.map((header, index) => (
                  <TableCell key={index} sx={{ color: 'grey.300', fontWeight: 'bold' }}>
                    {header}
                  </TableCell>
                ))}
                <TableCell sx={{ color: 'grey.300', fontWeight: 'bold' }}>Actions</TableCell>
              </StyledTableHeader>
            </TableHead>
            <TableBody>
              {data.map((row, index) => {
                const rowId = Object.values(row)[0];

                return (
                  <StyledTableRow key={index}>
                    {Object.entries(row).map(([key, value], cellIndex) => (
                      <TableCell key={cellIndex} sx={{ color: 'white' }}>
                        {key === 'display_image' && value ? (
                          <Box display="flex" alignItems="center">
                            <ImagePreview
                              src={getImageUrl(value)}
                              alt="Display"
                              onError={(e) => {
                                e.target.style.display = 'none';
                              }}
                            />
                          </Box>
                        ) : key === 'status' ? (
                          <Chip
                            label={value === 1 ? 'Active' : 'Inactive'}
                            color={value === 1 ? 'success' : 'error'}
                            size="small"
                          />
                        ) : (
                          <Typography variant="body2">
                            {value?.toString()}
                          </Typography>
                        )}
                      </TableCell>
                    ))}
                    <TableCell sx={{ color: 'white' }}>
                      <Box display="flex" gap={1}>
                        <IconButton
                          onClick={() => onEdit(row)}
                          size="small"
                          color="primary"
                          title="Edit"
                        >
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton
                          onClick={() => onDelete(rowId)}
                          size="small"
                          color="error"
                          title="Delete"
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </StyledTableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
  };



  // Section configurations
  const sections = [
    {
      key: 'difficulty',
      title: 'Difficulty Levels Management',
      headers: ['ID', 'Level Name', 'Display Image', 'Bonus Points', 'Time Multiplier', 'Status', 'Update at'],
      formFields: [
        { key: 'level_name', label: 'Level Name', required: true, placeholder: 'e.g., Easy, Medium, Hard' },
        { key: 'bonus_points', label: 'Bonus Points', type: 'number', required: true, min: 0 },
        { key: 'time_multiplier', label: 'Time Multiplier', type: 'number', required: true, min: 0.1, step: 0.1 },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    },
    {
      key: 'question-type',
      title: 'Question Types Management',
      headers: ['ID', 'Type Name', 'Display Name', 'Status', 'Update at'],
      formFields: [
        { key: 'type_name', label: 'Type Name', required: true, placeholder: 'e.g., text, text_image' },
        { key: 'display_name', label: 'Display Name', required: true, placeholder: 'e.g., Text Only, Text with Image' },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    },
    {
      key: 'answer-type',
      title: 'Answer Types Management',
      headers: ['ID', 'Type Name', 'Display Name', 'Status', 'Update at'],
      formFields: [
        { key: 'type_name', label: 'Type Name', required: true, placeholder: 'e.g., multiple_choice, true_false' },
        { key: 'display_name', label: 'Display Name', required: true, placeholder: 'e.g., Multiple Choice, True/False' },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    },
    {
      key: 'question-mood',
      title: 'Question Moods Management',
      headers: ['ID', 'Mood Name', 'Display Name', 'Status', 'Update at'],
      formFields: [
        { key: 'mood_name', label: 'Mood Name', required: true, placeholder: 'e.g., upbeat, calm, energetic' },
        { key: 'display_name', label: 'Display Name', required: true, placeholder: 'e.g., Upbeat, Calm, Energetic' },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    }
  ];

  return (
    <Box sx={{ maxWidth: 1200, margin: '0 auto', p: 3, minHeight: '100vh', backgroundColor: 'grey.900' }}>
      <Card sx={{ mb: 3, backgroundColor: 'grey.800', color: 'white' }}>
        <CardContent sx={{ textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            Quiz Lookup Management
          </Typography>
          <Typography variant="body1" color="grey.400">
            Manage difficulty levels, question types, answer types, and question moods for your quiz system
          </Typography>
        </CardContent>
      </Card>

      {sections.map((section, index) => {
        const isLoading = loading[section.key];
        const sectionData = lookupData[section.key] || [];
        const panelId = `panel-${index}`;

        return (
          <StyledAccordion
            key={section.key}
            expanded={expanded === panelId}
            onChange={handleAccordionChange(panelId)}
          >
            <AccordionSummary
              expandIcon={expanded === panelId ? <ExpandMore sx={{ color: 'white' }} /> : <ChevronRight sx={{ color: 'white' }} />}
              sx={{
                background: expanded === panelId
                  ? 'linear-gradient(to right, #1ae65eff, #12a142ff)'
                  : 'linear-gradient(to right, #2A2A2A, #333)',
              }}
            >
              <Box display="flex" alignItems="center">
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                  {section.title}
                  {isLoading && <CircularProgress size={16} sx={{ ml: 1 }} />}
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {/* Form Section */}
              <Card sx={{ mb: 3, backgroundColor: 'grey.800', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6" color="primary.main" gutterBottom>
                    Add New {section.title.replace(' Management', '')}
                  </Typography>

                  <Box display="grid" gridTemplateColumns="repeat(auto-fill, minmax(250px, 1fr))" gap={2}>
                    {section.formFields.map((field) => {
                      if (field.type === 'select') {
                        return (
                          <FormControl fullWidth key={field.key} error={!!errors[section.key]?.[field.key]}>
                            <InputLabel>{field.label}</InputLabel>
                            <Select
                              value={forms[section.key][field.key]}
                              onChange={(e) => updateForm(section.key, field.key, parseInt(e.target.value))}
                              label={field.label}
                              disabled={isLoading}
                            >
                              <MenuItem value={1}>Active</MenuItem>
                              <MenuItem value={0}>Inactive</MenuItem>
                            </Select>
                            {errors[section.key]?.[field.key] && (
                              <FormHelperText>{errors[section.key][field.key]}</FormHelperText>
                            )}
                          </FormControl>
                        );
                      }

                      return (
                        <TextField
                          key={field.key}
                          label={field.label}
                          type={field.type || 'text'}
                          value={forms[section.key][field.key]}
                          onChange={(e) => updateForm(section.key, field.key, e.target.value)}
                          placeholder={field.placeholder}
                          required={field.required}
                          disabled={isLoading}
                          error={!!errors[section.key]?.[field.key]}
                          helperText={errors[section.key]?.[field.key]}
                          InputProps={{
                            inputProps: {
                              min: field.min,
                              step: field.step
                            }
                          }}
                          fullWidth
                        />
                      );
                    })}

                    {/* Special handling for image upload in difficulty section */}
                    {section.key === 'difficulty' && (
                      <ImageUpload
                        value={forms.difficulty.display_image}
                        onChange={(value) => updateForm('difficulty', 'display_image', value)}
                        disabled={isLoading}
                      />
                    )}
                  </Box>

                  <Button
                    variant="contained"
                    onClick={() => handleCreate(section.key)}
                    disabled={isLoading}
                    startIcon={isLoading ? <CircularProgress size={16} /> : <Add />}
                    sx={{ mt: 2 }}
                  >
                    Add {section.title.replace(' Management', '').replace('s', '')}
                  </Button>
                </CardContent>
              </Card>

              {/* Create Button */}
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" color="white">
                  Current Records ({sectionData.length})
                </Typography>
                <ActionButton
                  variant="contained"
                  onClick={() => handleCreateClick(section.key)}
                  startIcon={<Add />}
                  sx={{
                    backgroundColor: '#4CAF50',
                    '&:hover': { backgroundColor: '#45a049' }
                  }}
                >
                  Create {section.title.replace(' Management', '').replace('s', '')}
                </ActionButton>
              </Box>

              {/* Data Table */}
              <SimpleDataTable
                headers={section.headers}
                data={sectionData}
                onEdit={(item) => handleEditClick(section.key, item)}
                onDelete={(id) => setDeleteConfirm({ open: true, type: section.key, id })}
                tableType={section.key}
                isLoading={isLoading}
              />
            </AccordionDetails>
          </StyledAccordion>
        );
      })}

      {/* Edit Modal */}
      <EditModal
        open={editModal.open}
        onClose={handleModalClose}
        editingItem={editModal.item}
        lookupType={editModal.type}
        onSave={handleModalSave}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, type: null, id: null })}
        PaperProps={{
          sx: {
            backgroundColor: '#1A1A1A',
            border: '1px solid #333',
            borderRadius: '12px',
          }
        }}
      >
        <DialogTitle sx={{ color: '#fff', borderBottom: '1px solid #333' }}>
          Confirm Delete
        </DialogTitle>
        <DialogContent sx={{ color: '#fff', pt: 2 }}>
          <Typography>
            Are you sure you want to delete this {deleteConfirm.type}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid #333', pt: 2 }}>
          <ActionButton
            variant="outlined"
            onClick={() => setDeleteConfirm({ open: false, type: null, id: null })}
          >
            Cancel
          </ActionButton>
          <ActionButton
            onClick={() => handleDelete(deleteConfirm.type, deleteConfirm.id)}
            variant="contained"
            sx={{
              backgroundColor: '#F44336',
              '&:hover': { backgroundColor: '#d32f2f' }
            }}
          >
            Delete
          </ActionButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default QuizLookupManagement;