import React, { useState, useEffect, useCallback } from 'react';
import {
  ChevronRight,
  ExpandMore,
  Add,
  Edit,
  Delete,
  Save,
  Cancel,
  CloudUpload,
  Image as ImageIcon,
} from '@mui/icons-material';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormHelperText,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import axios from 'axios';
import { useSnackbar } from 'notistack';

// Styled components
const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[900],
  color: theme.palette.common.white,
  '&:before': {
    display: 'none',
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.grey[800],
  },
  '&:hover': {
    backgroundColor: theme.palette.grey[700],
  },
}));

const StyledTableHeader = styled(TableRow)(({ theme }) => ({
  backgroundColor: theme.palette.grey[800],
}));

const ImagePreview = styled('img')({
  height: '48px',
  width: '48px',
  objectFit: 'contain',
  borderRadius: '4px',
  border: '1px solid #444',
});

const UploadButton = styled(Button)({
  textTransform: 'none',
  marginRight: '8px',
});

// Helper function to get full image URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;
  if (imagePath.startsWith('http')) return imagePath;
  return `${process.env.REACT_APP_API_URL}${imagePath}`;
};

// Image upload component
const ImageUpload = ({ value, onChange, disabled = false }) => {
  const [uploading, setUploading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const handleUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/upload/quiz`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      onChange(response.data.imageUrl);
      enqueueSnackbar('Image uploaded successfully!', { variant: 'success' });
    } catch (error) {
      console.error('Error uploading image:', error);
      enqueueSnackbar('Error uploading image. Please try again.', { variant: 'error' });
    } finally {
      setUploading(false);
    }
  };

  return (
    <Box mb={2}>
      <Typography variant="subtitle2" gutterBottom color="white">
        Display Image
      </Typography>

      {value && (
        <Box display="flex" alignItems="center" mb={1}>
          <ImageIcon sx={{ mr: 1, color: 'rgba(255,255,255,0.7)' }} fontSize="small" />
          <Typography variant="body2" color="rgba(255,255,255,0.7)" noWrap>
            {value.split('/').pop()}
          </Typography>
        </Box>
      )}

      <Box display="flex" alignItems="center">
        <UploadButton
          variant="outlined"
          component="label"
          disabled={disabled || uploading}
          startIcon={<CloudUpload />}
          color="primary"
        >
          {uploading ? 'Uploading...' : 'Upload Image'}
          <input
            type="file"
            accept="image/*"
            onChange={handleUpload}
            hidden
            disabled={disabled || uploading}
          />
        </UploadButton>

        {value && !disabled && (
          <Button
            variant="contained"
            color="error"
            onClick={() => onChange('')}
          >
            Remove
          </Button>
        )}
      </Box>

      {value && (
        <Box mt={1}>
          <ImagePreview
            src={getImageUrl(value)}
            alt="Preview"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        </Box>
      )}
    </Box>
  );
};

// Enhanced lookupService with image upload support
const lookupService = {
  fetchItems: async (type) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type}:`, error);
      throw error;
    }
  },

  createItem: async (type, item) => {
    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`, item);
      return response.data;
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      throw error;
    }
  },

  updateItem: async (type, id, item) => {
    try {
      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`, item);
      return response.data;
    } catch (error) {
      console.error(`Error updating ${type}:`, error);
      throw error;
    }
  },

  deleteItem: async (type, id) => {
    try {
      const response = await axios.delete(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      throw error;
    }
  },

  getFieldMap(type) {
    const fieldMaps = {
      difficulty: {
        name: 'level_name',
        description: 'display_image',
        id: 'difficulty_id',
        status: 'status',
        bonus_points: 'bonus_points',
        time_multiplier: 'time_multiplier'
      },
      'question-type': {
        name: 'type_name',
        description: 'display_name',
        id: 'question_type_id',
        status: 'status'
      },
      'answer-type': {
        name: 'type_name',
        description: 'display_name',
        id: 'answer_type_id',
        status: 'status'
      }
    };
    return fieldMaps[type] || { name: 'name', description: 'description', id: 'id' };
  },

  getDisplayHeaders(type) {
    const headers = {
      difficulty: ['ID', 'Level Name', 'Display Image', 'Bonus Points', 'Time Multiplier', 'Status'],
      'question-type': ['ID', 'Type Name', 'Display Name', 'Status'],
      'answer-type': ['ID', 'Type Name', 'Display Name', 'Status']
    };
    return headers[type] || ['ID', 'Name', 'Description', 'Status'];
  },

  getItemId(item, type) {
    const fieldMap = this.getFieldMap(type);
    return item[fieldMap.id] || item.id;
  },

  createPayload(item, type) {
    console.log('createPayload called with:', { item, type });
    const fields = this.getFieldMap(type);
    console.log('Field mapping for type:', type, fields);

    if (type === 'difficulty') {
      // Backend expects: { name, description, status, bonus_points, time_multiplier }
      const payload = {
        name: item.level_name || item[fields.name] || '',
        description: item.display_image || item[fields.description] || '',
        bonus_points: Number(item.bonus_points) || Number(item[fields.bonus_points]) || 10,
        time_multiplier: Number(item.time_multiplier) || Number(item[fields.time_multiplier]) || 1.0,
        status: Number(item.status) || Number(item[fields.status]) || 0
      };

      // Ensure required fields are not empty
      if (!payload.name) {
        throw new Error('Level name is required');
      }

      console.log('Difficulty payload created:', payload);
      return payload;
    } else {
      // Backend expects: { name, description, status }
      const payload = {
        name: item.type_name || item[fields.name] || '',
        description: item.display_name || item[fields.description] || '',
        status: Number(item.status) || Number(item[fields.status]) || 0
      };

      // Ensure required fields are not empty
      if (!payload.name) {
        throw new Error('Type name is required');
      }
      if (!payload.description) {
        throw new Error('Display name is required');
      }

      console.log('Other type payload created:', payload);
      return payload;
    }
  }
};

const QuizLookupManagement = () => {
  const [expanded, setExpanded] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [loading, setLoading] = useState({});
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, type: null, id: null });
  const { enqueueSnackbar } = useSnackbar();

  // State for lookup data
  const [lookupData, setLookupData] = useState({
    difficulty: [],
    'question-type': [],
    'answer-type': []
  });

  // Form states
  const [forms, setForms] = useState({
    difficulty: {
      level_name: '',
      display_image: '',
      bonus_points: 10,
      time_multiplier: 1.0,
      status: 1
    },
    'question-type': {
      type_name: '',
      display_name: '',
      status: 1
    },
    'answer-type': {
      type_name: '',
      display_name: '',
      status: 1
    }
  });

  // Form validation states
  const [errors, setErrors] = useState({
    difficulty: {},
    'question-type': {},
    'answer-type': {}
  });

  const loadAllData = useCallback(async () => {
    try {
      const types = ['difficulty', 'question-type', 'answer-type'];
      const promises = types.map(async (type) => {
        setLoading(prev => ({ ...prev, [type]: true }));
        const data = await lookupService.fetchItems(type);
        setLoading(prev => ({ ...prev, [type]: false }));
        return { type, data };
      });

      const results = await Promise.all(promises);
      const newData = {};
      results.forEach(({ type, data }) => {
        newData[type] = data;
      });

      setLookupData(newData);
    } catch (error) {
      console.error('Error loading data:', error);
      enqueueSnackbar('Error loading data. Please refresh the page.', { variant: 'error' });
    }
  }, [enqueueSnackbar]);

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  const handleAccordionChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const validateForm = (type) => {
    const form = forms[type];
    const fields = lookupService.getFieldMap(type);
    const newErrors = {};

    if (!form[fields.name] || form[fields.name].trim() === '') {
      newErrors[fields.name] = 'This field is required';
    }

    if (!form[fields.description] || form[fields.description].trim() === '') {
      newErrors[fields.description] = 'This field is required';
    }

    if (type === 'difficulty') {
      if (!form.bonus_points || form.bonus_points <= 0) {
        newErrors.bonus_points = 'Must be a positive number';
      }

      if (!form.time_multiplier || form.time_multiplier <= 0) {
        newErrors.time_multiplier = 'Must be a positive number';
      }
    }

    setErrors(prev => ({ ...prev, [type]: newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  // Generic CRUD handlers
  const handleCreate = async (type) => {
    if (!validateForm(type)) {
      enqueueSnackbar('Please fix the form errors', { variant: 'error' });
      return;
    }

    const form = forms[type];

    try {
      setLoading(prev => ({ ...prev, [type]: true }));

      const payload = lookupService.createPayload(form, type);
      await lookupService.createItem(type, payload);

      // Refresh data
      const updatedData = await lookupService.fetchItems(type);
      setLookupData(prev => ({ ...prev, [type]: updatedData }));

      // Reset form
      setForms(prev => ({
        ...prev,
        [type]: getDefaultForm(type)
      }));

      enqueueSnackbar(`${type.charAt(0).toUpperCase() + type.slice(1)} created successfully!`, { variant: 'success' });
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      enqueueSnackbar(`Error creating ${type}: ${error.message}`, { variant: 'error' });
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleUpdate = async (type, id, updatedData) => {
    try {
      console.log('handleUpdate called with:', { type, id, updatedData });
      setLoading(prev => ({ ...prev, [type]: true }));

      // Enhanced validation with detailed error messages
      if (type === 'difficulty') {
        if (!updatedData.level_name || updatedData.level_name.trim() === '') {
          throw new Error('Level name is required and cannot be empty');
        }
        if (updatedData.bonus_points < 0) {
          throw new Error('Bonus points must be 0 or greater');
        }
        if (updatedData.time_multiplier <= 0) {
          throw new Error('Time multiplier must be greater than 0');
        }
      } else if (type === 'question-type' || type === 'answer-type') {
        if (!updatedData.type_name || updatedData.type_name.trim() === '') {
          throw new Error('Type name is required and cannot be empty');
        }
        if (!updatedData.display_name || updatedData.display_name.trim() === '') {
          throw new Error('Display name is required and cannot be empty');
        }
      }

      // Ensure status is properly converted
      if (updatedData.status !== undefined) {
        updatedData.status = parseInt(updatedData.status, 10);
      }

      const payload = lookupService.createPayload(updatedData, type);
      console.log('Created payload:', payload);

      const response = await lookupService.updateItem(type, id, payload);
      console.log('Update response:', response);

      // Refresh the data to get the latest from server
      await loadAllData();

      setEditingItem(null);

      // Don't show success message here as it's handled in DataTable
      return response;
    } catch (error) {
      console.error(`Error updating ${type}:`, error);

      // Enhanced error handling for different error types
      let errorMessage = error.message;
      if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Item not found. It may have been deleted by another user.';
      }

      throw new Error(errorMessage);
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleDelete = async (type, id) => {
    try {
      setLoading(prev => ({ ...prev, [type]: true }));

      await lookupService.deleteItem(type, id);

      // Update local state
      setLookupData(prev => ({
        ...prev,
        [type]: prev[type].filter(item => lookupService.getItemId(item, type) !== id)
      }));

      setDeleteConfirm({ open: false, type: null, id: null });
      enqueueSnackbar(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`, { variant: 'success' });
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      enqueueSnackbar(`Error deleting ${type}: ${error.message}`, { variant: 'error' });
    } finally {
      setLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const getDefaultForm = (type) => {
    const defaults = {
      difficulty: {
        level_name: '',
        display_image: '',
        bonus_points: 10,
        time_multiplier: 1.0,
        status: 1
      },
      'question-type': {
        type_name: '',
        display_name: '',
        status: 1
      },
      'answer-type': {
        type_name: '',
        display_name: '',
        status: 1
      }
    };
    return defaults[type];
  };

  const updateForm = (type, field, value) => {
    setForms(prev => ({
      ...prev,
      [type]: { ...prev[type], [field]: value }
    }));

    // Clear error when field is updated
    if (errors[type][field]) {
      setErrors(prev => ({
        ...prev,
        [type]: { ...prev[type], [field]: '' }
      }));
    }
  };

  const EditableField = ({ value, onChange, type = "text", field }) => {
    const [localValue, setLocalValue] = useState(value);

    // Update local value when prop value changes
    useEffect(() => {
      setLocalValue(value);
    }, [value]);

    const handleChange = (newValue) => {
      setLocalValue(newValue);
      onChange(newValue);
    };

    const handleKeyDown = (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        // We'll handle this at the table level
        if (e.target.closest('tr')) {
          const saveButton = e.target.closest('tr').querySelector('[data-save-button]');
          if (saveButton) saveButton.click();
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        // We'll handle this at the table level
        if (e.target.closest('tr')) {
          const cancelButton = e.target.closest('tr').querySelector('[data-cancel-button]');
          if (cancelButton) cancelButton.click();
        }
      }
    };

    return (
      <Box sx={{ minWidth: 120 }}>
        {field === 'status' ? (
          <Select
            value={localValue}
            onChange={(e) => handleChange(e.target.value)}
            size="small"
            fullWidth
            sx={{
              backgroundColor: 'white',
              color: 'black',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#2196F3',
                borderWidth: 2
              },
              '& .MuiSelect-select': {
                color: 'black'
              }
            }}
          >
            <MenuItem value={1}>Active</MenuItem>
            <MenuItem value={0}>Inactive</MenuItem>
          </Select>
        ) : (
          <TextField
            type={type}
            value={localValue || ''}
            onChange={(e) => handleChange(e.target.value)}
            onKeyDown={handleKeyDown}
            size="small"
            fullWidth
            placeholder={`Enter ${field.replace('_', ' ')}`}
            sx={{
              backgroundColor: 'white',
              '& input': {
                color: 'black',
                padding: '8px 12px'
              },
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  borderColor: '#2196F3',
                  borderWidth: 2
                },
                '&:hover fieldset': {
                  borderColor: '#1976D2'
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#0D47A1'
                }
              }
            }}
            inputProps={type === "number" ? { step: "0.1", min: "0" } : {}}
          />
        )}
      </Box>
    );
  };

  const DataTable = ({ headers, data, onEdit, onDelete, editingId, onUpdateField, onCancelEdit, tableType, isLoading }) => {
    const [editingData, setEditingData] = useState({});
    const [saveLoading, setSaveLoading] = useState(false);
    const { enqueueSnackbar } = useSnackbar();

    const handleFieldChange = (field, value) => {
      console.log('Field change:', { field, value, type: typeof value });

      // Proper type conversion for different field types
      let processedValue = value;

      if (field === 'status') {
        processedValue = parseInt(value, 10);
      } else if (field === 'bonus_points' || field === 'time_multiplier') {
        processedValue = parseFloat(value) || 0;
      } else if (typeof value === 'string') {
        processedValue = value.trim();
      }

      setEditingData(prev => ({
        ...prev,
        [field]: processedValue
      }));

      console.log('Updated editing data:', { ...editingData, [field]: processedValue });
    };

    const validateChanges = () => {
      const originalRow = data.find(row => Object.values(row)[0] === editingId);
      if (!originalRow) return { isValid: false, message: 'Original data not found' };

      // Check if any changes were made
      const hasChanges = Object.keys(editingData).some(key => {
        const newValue = editingData[key];
        const oldValue = originalRow[key];
        return newValue !== oldValue;
      });

      if (!hasChanges) {
        return { isValid: false, message: 'No changes to save' };
      }

      // Validate required fields
      const mergedData = { ...originalRow, ...editingData };

      if (tableType === 'difficulty') {
        if (!mergedData.level_name || mergedData.level_name.trim() === '') {
          return { isValid: false, message: 'Level name is required' };
        }
        if (mergedData.bonus_points < 0) {
          return { isValid: false, message: 'Bonus points must be 0 or greater' };
        }
        if (mergedData.time_multiplier <= 0) {
          return { isValid: false, message: 'Time multiplier must be greater than 0' };
        }
      } else {
        if (!mergedData.type_name || mergedData.type_name.trim() === '') {
          return { isValid: false, message: 'Type name is required' };
        }
        if (!mergedData.display_name || mergedData.display_name.trim() === '') {
          return { isValid: false, message: 'Display name is required' };
        }
      }

      return { isValid: true, data: mergedData };
    };

    const handleSaveAll = async () => {
      if (!editingId) return;

      const validation = validateChanges();
      if (!validation.isValid) {
        enqueueSnackbar(validation.message, { variant: 'warning' });
        return;
      }

      setSaveLoading(true);
      try {
        await onUpdateField(editingId, validation.data);
        setEditingData({});
        enqueueSnackbar('Changes saved successfully!', { variant: 'success' });
      } catch (error) {
        console.error('Save error:', error);
        enqueueSnackbar(`Error saving changes: ${error.message}`, { variant: 'error' });
      } finally {
        setSaveLoading(false);
      }
    };

    const handleCancelEdit = () => {
      setEditingData({});
      onCancelEdit();
    };



    if (isLoading) {
      return (
        <Box mt={3} display="flex" justifyContent="center" alignItems="center" py={4}>
          <CircularProgress size={24} sx={{ mr: 2 }} />
          <Typography variant="body2" color="grey.400">
            Loading data...
          </Typography>
        </Box>
      );
    }

    if (data.length === 0) {
      return (
        <Box mt={3} textAlign="center" py={4}>
          <Typography variant="body2" color="grey.400">
            No records found. Add your first {tableType} above!
          </Typography>
        </Box>
      );
    }

    return (
      <Box mt={3}>
        <Typography variant="h6" gutterBottom color="white">
          Current Records ({data.length})
        </Typography>
        <TableContainer component={Paper} sx={{ backgroundColor: 'grey.900', border: '1px solid', borderColor: 'grey.700' }}>
          <Table>
            <TableHead>
              <StyledTableHeader>
                {headers.map((header, index) => (
                  <TableCell key={index} sx={{ color: 'grey.300', fontWeight: 'bold' }}>
                    {header}
                  </TableCell>
                ))}
                <TableCell sx={{ color: 'grey.300', fontWeight: 'bold' }}>Actions</TableCell>
              </StyledTableHeader>
            </TableHead>
            <TableBody>
              {data.map((row, index) => {
                const rowId = Object.values(row)[0];
                const isEditing = editingId === rowId;

                return (
                  <StyledTableRow
                    key={index}
                    sx={{
                      backgroundColor: isEditing ? 'rgba(33, 150, 243, 0.15)' : 'inherit',
                      border: isEditing ? '2px solid #2196F3' : 'none',
                      '& td': {
                        borderBottom: isEditing ? '1px solid #2196F3' : '1px solid rgba(255,255,255,0.1)'
                      }
                    }}
                  >
                    {Object.entries(row).map(([key, value], cellIndex) => (
                      <TableCell
                        key={cellIndex}
                        sx={{
                          color: 'white',
                          backgroundColor: isEditing && key !== Object.keys(row)[0] ? 'rgba(33, 150, 243, 0.05)' : 'transparent',
                          padding: isEditing ? '16px 8px' : '8px'
                        }}
                      >
                        {key === 'display_image' && value ? (
                          <Box display="flex" alignItems="center">
                            <ImagePreview
                              src={getImageUrl(value)}
                              alt="Display"
                              onError={(e) => {
                                e.target.style.display = 'none';
                              }}
                            />
                          </Box>
                        ) : isEditing && key !== Object.keys(row)[0] && key !== 'created_at' && key !== 'updated_at' ? (
                          <EditableField
                            value={editingData[key] !== undefined ? editingData[key] : value}
                            onChange={(newValue) => handleFieldChange(key, newValue)}
                            type={typeof value === 'number' ? 'number' : 'text'}
                            field={key}
                          />
                        ) : key === 'status' && !isEditing ? (
                          <Chip
                            label={value === 1 ? 'Active' : 'Inactive'}
                            color={value === 1 ? 'success' : 'error'}
                            size="small"
                          />
                        ) : key === 'status' && isEditing ? (
                          <EditableField
                            value={editingData[key] !== undefined ? editingData[key] : value}
                            onChange={(newValue) => handleFieldChange(key, newValue)}
                            type="text"
                            field={key}
                          />
                        ) : (
                          <Typography variant="body2">
                            {value?.toString()}
                          </Typography>
                        )}
                      </TableCell>
                    ))}
                    <TableCell sx={{ color: 'white' }}>
                      {!isEditing ? (
                        <Box display="flex" gap={1}>
                          <IconButton
                            onClick={() => onEdit(rowId)}
                            size="small"
                            color="primary"
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton
                            onClick={() => setDeleteConfirm({ open: true, type: tableType, id: rowId })}
                            size="small"
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      ) : (
                        <Box display="flex" gap={1}>
                          <IconButton
                            onClick={handleSaveAll}
                            disabled={saveLoading}
                            size="small"
                            data-save-button
                            title="Save changes (Enter)"
                            sx={{
                              color: 'success.main',
                              backgroundColor: 'rgba(76, 175, 80, 0.1)',
                              '&:hover': { backgroundColor: 'rgba(76, 175, 80, 0.2)' },
                              '&:disabled': {
                                backgroundColor: 'rgba(76, 175, 80, 0.05)',
                                color: 'rgba(76, 175, 80, 0.5)'
                              }
                            }}
                          >
                            {saveLoading ? (
                              <CircularProgress size={16} color="inherit" />
                            ) : (
                              <Save fontSize="small" />
                            )}
                          </IconButton>
                          <IconButton
                            onClick={handleCancelEdit}
                            disabled={saveLoading}
                            size="small"
                            data-cancel-button
                            title="Cancel changes (Escape)"
                            sx={{
                              color: 'error.main',
                              backgroundColor: 'rgba(244, 67, 54, 0.1)',
                              '&:hover': { backgroundColor: 'rgba(244, 67, 54, 0.2)' },
                              '&:disabled': {
                                backgroundColor: 'rgba(244, 67, 54, 0.05)',
                                color: 'rgba(244, 67, 54, 0.5)'
                              }
                            }}
                          >
                            <Cancel fontSize="small" />
                          </IconButton>
                        </Box>
                      )}
                    </TableCell>
                  </StyledTableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
  };

  // Section configurations
  const sections = [
    {
      key: 'difficulty',
      title: 'Difficulty Levels Management',
      headers: ['ID', 'Level Name', 'Display Image', 'Bonus Points', 'Time Multiplier', 'Status', 'Update at'],
      formFields: [
        { key: 'level_name', label: 'Level Name', required: true, placeholder: 'e.g., Easy, Medium, Hard' },
        { key: 'bonus_points', label: 'Bonus Points', type: 'number', required: true, min: 0 },
        { key: 'time_multiplier', label: 'Time Multiplier', type: 'number', required: true, min: 0.1, step: 0.1 },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    },
    {
      key: 'question-type',
      title: 'Question Types Management',
      headers: ['ID', 'Type Name', 'Display Name', 'Status', 'Update at'],
      formFields: [
        { key: 'type_name', label: 'Type Name', required: true, placeholder: 'e.g., text, text_image' },
        { key: 'display_name', label: 'Display Name', required: true, placeholder: 'e.g., Text Only, Text with Image' },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    },
    {
      key: 'answer-type',
      title: 'Answer Types Management',
      headers: ['ID', 'Type Name', 'Display Name', 'Status', 'Update at'],
      formFields: [
        { key: 'type_name', label: 'Type Name', required: true, placeholder: 'e.g., multiple_choice, true_false' },
        { key: 'display_name', label: 'Display Name', required: true, placeholder: 'e.g., Multiple Choice, True/False' },
        { key: 'status', label: 'Status', type: 'select', required: true }
      ]
    }
  ];

  return (
    <Box sx={{ maxWidth: 1200, margin: '0 auto', p: 3, minHeight: '100vh', backgroundColor: 'grey.900' }}>
      <Card sx={{ mb: 3, backgroundColor: 'grey.800', color: 'white' }}>
        <CardContent sx={{ textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            Quiz Lookup Management
          </Typography>
          <Typography variant="body1" color="grey.400">
            Manage difficulty levels, question types, and answer types for your quiz system
          </Typography>
        </CardContent>
      </Card>

      {sections.map((section, index) => {
        const isLoading = loading[section.key];
        const sectionData = lookupData[section.key] || [];
        const panelId = `panel-${index}`;

        return (
          <StyledAccordion
            key={section.key}
            expanded={expanded === panelId}
            onChange={handleAccordionChange(panelId)}
          >
            <AccordionSummary
              expandIcon={expanded === panelId ? <ExpandMore sx={{ color: 'white' }} /> : <ChevronRight sx={{ color: 'white' }} />}
              sx={{
                background: expanded === panelId
                  ? 'linear-gradient(to right, #1ae65eff, #12a142ff)'
                  : 'linear-gradient(to right, #2A2A2A, #333)',
              }}
            >
              <Box display="flex" alignItems="center">
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                  {section.title}
                  {isLoading && <CircularProgress size={16} sx={{ ml: 1 }} />}
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {/* Form Section */}
              <Card sx={{ mb: 3, backgroundColor: 'grey.800', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6" color="primary.main" gutterBottom>
                    Add New {section.title.replace(' Management', '')}
                  </Typography>

                  <Box display="grid" gridTemplateColumns="repeat(auto-fill, minmax(250px, 1fr))" gap={2}>
                    {section.formFields.map((field) => {
                      if (field.type === 'select') {
                        return (
                          <FormControl fullWidth key={field.key} error={!!errors[section.key]?.[field.key]}>
                            <InputLabel>{field.label}</InputLabel>
                            <Select
                              value={forms[section.key][field.key]}
                              onChange={(e) => updateForm(section.key, field.key, parseInt(e.target.value))}
                              label={field.label}
                              disabled={isLoading}
                            >
                              <MenuItem value={1}>Active</MenuItem>
                              <MenuItem value={0}>Inactive</MenuItem>
                            </Select>
                            {errors[section.key]?.[field.key] && (
                              <FormHelperText>{errors[section.key][field.key]}</FormHelperText>
                            )}
                          </FormControl>
                        );
                      }

                      return (
                        <TextField
                          key={field.key}
                          label={field.label}
                          type={field.type || 'text'}
                          value={forms[section.key][field.key]}
                          onChange={(e) => updateForm(section.key, field.key, e.target.value)}
                          placeholder={field.placeholder}
                          required={field.required}
                          disabled={isLoading}
                          error={!!errors[section.key]?.[field.key]}
                          helperText={errors[section.key]?.[field.key]}
                          InputProps={{
                            inputProps: {
                              min: field.min,
                              step: field.step
                            }
                          }}
                          fullWidth
                        />
                      );
                    })}

                    {/* Special handling for image upload in difficulty section */}
                    {section.key === 'difficulty' && (
                      <ImageUpload
                        value={forms.difficulty.display_image}
                        onChange={(value) => updateForm('difficulty', 'display_image', value)}
                        disabled={isLoading}
                      />
                    )}
                  </Box>

                  <Button
                    variant="contained"
                    onClick={() => handleCreate(section.key)}
                    disabled={isLoading}
                    startIcon={isLoading ? <CircularProgress size={16} /> : <Add />}
                    sx={{ mt: 2 }}
                  >
                    Add {section.title.replace(' Management', '').replace('s', '')}
                  </Button>
                </CardContent>
              </Card>

              {/* Data Table */}
              <DataTable
                headers={section.headers}
                data={sectionData}
                onEdit={(id) => setEditingItem({ type: section.key, id })}
                onDelete={(id) => setDeleteConfirm({ open: true, type: section.key, id })}
                editingId={editingItem?.type === section.key ? editingItem.id : null}
                onUpdateField={(id, updatedData) => handleUpdate(section.key, id, updatedData)}
                onCancelEdit={() => setEditingItem(null)}
                tableType={section.key}
                isLoading={isLoading}
              />
            </AccordionDetails>
          </StyledAccordion>
        );
      })}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, type: null, id: null })}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this {deleteConfirm.type}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirm({ open: false, type: null, id: null })}>
            Cancel
          </Button>
          <Button
            onClick={() => handleDelete(deleteConfirm.type, deleteConfirm.id)}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default QuizLookupManagement;