const express = require('express');
const router = express.Router();
const controller = require('../controllers/playlistSongsController');

router.post('/', controller.create);
router.get('/', controller.getAll);
router.get('/:id', controller.getById);
router.put('/:id', controller.update);
router.delete('/:id', controller.delete);

router.delete("/:playlist_master_id/:songs_master_id", controller.deleteByPlaylistAndSong);
router.get('/playlist/:playlist_master_id/songs', controller.getSongsByPlaylistId);

// get playlist by song id
router.get('/song/:songs_master_id/playlists', controller.getPlaylistsBySongId);

module.exports = router;