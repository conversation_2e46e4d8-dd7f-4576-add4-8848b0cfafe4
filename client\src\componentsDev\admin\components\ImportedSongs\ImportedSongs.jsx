import { useState, useEffect, useCallback } from "react";
import { 
  Box, 
  Typography, 
  Tabs, 
  Tab, 
  Divider, 
  Container, 
  styled,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Paper,
  Chip
} from "@mui/material";
import { useSnackbar } from "notistack";
import axios from "axios";
import FileUploadSection from "./FileUploadSection";
import SongsTable from "./SongsTable";
import EditSongDialog from "./EditSongDialog";
import SpotifySearchDialog from "./SpotifySearchDialog";
import SpotifyTracksTable from "./SpotifyTracksTable";
import EditSpotifyTrackDialog from "./EditSpotifyTrackDialog";
import MigratedTracksTable from "./MigratedTracksTable";
// import Header from "../Header";

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.9rem",
  fontWeight: "bold",
  color: "white",
  flex: 1,
  "&.Mui-selected": {
    color: "#1db954",
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  "& .MuiTabs-indicator": {
    backgroundColor: "#1db954",
  },
  marginBottom: theme.spacing(2),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundColor: "#1e1e1e",
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  border: "1px solid rgba(255, 255, 255, 0.12)",
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiInputBase-root": {
    backgroundColor: "#2a2a2a",
    color: "white",
  },
  "& .MuiInputLabel-root": {
    color: "rgba(255, 255, 255, 0.7)",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "rgba(255, 255, 255, 0.23)",
    },
    "&:hover fieldset": {
      borderColor: "rgba(255, 255, 255, 0.5)",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#1db954",
    },
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  "& .MuiInputBase-root": {
    backgroundColor: "#2a2a2a",
    color: "white",
  },
  "& .MuiInputLabel-root": {
    color: "rgba(255, 255, 255, 0.7)",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "rgba(255, 255, 255, 0.23)",
    },
    "&:hover fieldset": {
      borderColor: "rgba(255, 255, 255, 0.5)",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#1db954",
    },
  },
}));

const ImportedSongs = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [songs, setSongs] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    sortBy: "created_at",
    sortOrder: "DESC",
    limit: 10,
  });
  const [loading, setLoading] = useState(false);
  const [editSong, setEditSong] = useState(null);
  const [editSpotifyTrack, setEditSpotifyTrack] = useState(null);
  const [searchResult, setSearchResult] = useState(null);
  const [searchDialogOpen, setSearchDialogOpen] = useState(false);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [spotifyTracks, setSpotifyTracks] = useState([]);
  const [migratedTracks, setMigratedTracks] = useState([]);
  const [spotifySearchSongId, setSpotifySearchSongId] = useState(null);
  const [urlRefreshTrigger, setUrlRefreshTrigger] = useState(0);
  const { enqueueSnackbar } = useSnackbar();

  const fetchSongs = useCallback( async (page = 1, newFilters = filters) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: newFilters.limit,
        search: newFilters.search,
        status: newFilters.status,
        sortBy: newFilters.sortBy,
        sortOrder: newFilters.sortOrder,
      };

      // Remove empty parameters
      Object.keys(params).forEach(key => {
        if (params[key] === "" || params[key] === undefined) {
          delete params[key];
        }
      });

      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/import/songs`,
        { params }
      );

      setSongs(res.data.data);
      setPagination(res.data.pagination);
    } catch (error) {
      console.error("Error fetching songs", error);
      enqueueSnackbar("❌ Failed to fetch songs", { variant: "error" });
    } finally {
      setLoading(false);
    }
  }, [filters, enqueueSnackbar]);

  const fetchSpotifyTracks = useCallback( async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/import/spotify/tracks/all`
      );
      setSpotifyTracks(res.data);
    } catch (error) {
      console.error("Error fetching tracks", error);
      enqueueSnackbar("❌ Failed to fetch Spotify tracks", {
        variant: "error",
      });
    }
  }, [ enqueueSnackbar ]);

  const fetchMigratedTracks = useCallback( async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/migrate/migrated`
      );
      setMigratedTracks(res.data);
    } catch (error) {
      console.error("Error fetching migrated tracks", error);
      enqueueSnackbar("❌ Failed to fetch migrated tracks", {
        variant: "error",
      });
    }
  }, [ enqueueSnackbar ]);

  const handlePageChange = (event, newPage) => {
    fetchSongs(newPage);
  };

  const handleFilterChange = (field, value) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    fetchSongs(1, newFilters); // Reset to page 1 when filters change
  };

  const handleFileUpload = async (file) => {
    const formData = new FormData();
    formData.append("file", file);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/import/upload-songs`,
        formData
      );
      fetchSongs(1); // Refresh first page after upload
      enqueueSnackbar(
        `✅ Imported: ${res.data.inserted}, Skipped: ${res.data.skipped}`,
        { variant: "success" }
      );
    } catch (error) {
      console.error("Upload failed", error);
      enqueueSnackbar("❌ Upload failed", { variant: "error" });
    }
  };

  const handleEditSpotifyTrack = (track) => {
    setEditSpotifyTrack(track);
  };

  const handleSaveSpotifyTrack = async (updatedTrack) => {
    try {
      await axios.put(
        `${process.env.REACT_APP_API_URL}/api/import/spotify/tracks/${updatedTrack.id}`,
        updatedTrack
      );
      enqueueSnackbar("✅ Track updated", { variant: "success" });
      setEditSpotifyTrack(null);
      fetchSpotifyTracks();
    } catch (err) {
      console.error("Update failed", err);
      enqueueSnackbar("❌ Update failed", { variant: "error" });
    }
  };

  const handleSpotifySearch = async (song) => {
    setSpotifySearchSongId(song.id);
    setLoadingSearch(true);
    try {
      const [trackRes, artistRes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/spotify/search-track`, {
          params: { songName: song.song_name, artistName: song.artist_name },
        }),
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/spotify/search-artist`,
          {
            params: { artistName: song.artist_name },
          }
        ),
      ]);
      setSearchResult({
        trackResults: trackRes.data.tracks,
        artistResults: artistRes.data.artists,
        originalSong: song,
      });
      setSearchDialogOpen(true);
    } catch (err) {
      console.error("Spotify search failed", err);
      enqueueSnackbar("❌ Spotify search failed", { variant: "error" });
    } finally {
      setLoadingSearch(false);
    }
  };

  const handleEditSong = (song) => {
    setEditSong(song);
  };

  const handleSaveEdit = async (editForm) => {
    try {
      await axios.put(
        `${process.env.REACT_APP_API_URL}/api/import/songs/${editSong.id}`,
        editForm
      );
      enqueueSnackbar("✅ Song updated", { variant: "success" });
      setEditSong(null);
      fetchSongs(pagination.currentPage); // Refresh current page
    } catch (err) {
      console.error("Update failed", err);
      enqueueSnackbar("❌ Update failed", { variant: "error" });
    }
  };

  // Refresh all data when migration happens
  const handleMigrationComplete = () => {
    fetchSongs();
    fetchSpotifyTracks();
    fetchMigratedTracks();
  };

  // Refresh temp tab data when songs are updated
  const handleSongUpdate = () => {
    fetchSongs();
    fetchSpotifyTracks();
  };

  // Trigger URL refresh in SpotifyTracksTable
  const handleUrlAdded = () => {
    setUrlRefreshTrigger(prev => prev + 1);
  };

  useEffect(() => {
    fetchSongs();
    fetchSpotifyTracks();
    fetchMigratedTracks();
  }, [fetchSongs, fetchSpotifyTracks, fetchMigratedTracks]);

  const renderPaginationInfo = () => {
    const start = (pagination.currentPage - 1) * pagination.itemsPerPage + 1;
    const end = Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems);
    
    return (
      <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}>
        Showing {start}-{end} of {pagination.totalItems} songs
      </Typography>
    );
  };

  return (
    <>
      {/* <Header /> */}
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box sx={{ marginTop: 3 }}>
          <StyledTabs
            value={tabIndex}
            onChange={(e, newIndex) => setTabIndex(newIndex)}
            variant="fullWidth"
          >
            <StyledTab label="🎵 New" value={0} />
            <StyledTab label="🎧 Temp" value={1} />
            <StyledTab label="✅ Migrated" value={2} />
          </StyledTabs>
          <Divider sx={{ mb: 2, borderColor: "rgba(255, 255, 255, 0.12)" }} />

          {tabIndex === 0 && (
            <>
              <FileUploadSection onUpload={handleFileUpload} />

              {/* Filters and Search */}
              <StyledPaper>
                <Typography variant="h6" sx={{ color: "white", mb: 2 }}>
                  Filters & Search
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={3}>
                    <StyledTextField
                      fullWidth
                      label="Search songs/artists"
                      value={filters.search}
                      onChange={(e) => handleFilterChange("search", e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <StyledFormControl fullWidth size="small">
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={filters.status}
                        label="Status"
                        onChange={(e) => handleFilterChange("status", e.target.value)}
                      >
                        <MenuItem value="">All</MenuItem>
                        <MenuItem value="pending">Pending</MenuItem>
                        <MenuItem value="completed">Completed</MenuItem>
                        <MenuItem value="disabled">Disabled</MenuItem>
                      </Select>
                    </StyledFormControl>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <StyledFormControl fullWidth size="small">
                      <InputLabel>Sort By</InputLabel>
                      <Select
                        value={filters.sortBy}
                        label="Sort By"
                        onChange={(e) => handleFilterChange("sortBy", e.target.value)}
                      >
                        <MenuItem value="created_at">Created Date</MenuItem>
                        <MenuItem value="song_name">Song Name</MenuItem>
                        <MenuItem value="artist_name">Artist Name</MenuItem>
                        <MenuItem value="status">Status</MenuItem>
                        <MenuItem value="id">ID</MenuItem>
                      </Select>
                    </StyledFormControl>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <StyledFormControl fullWidth size="small">
                      <InputLabel>Order</InputLabel>
                      <Select
                        value={filters.sortOrder}
                        label="Order"
                        onChange={(e) => handleFilterChange("sortOrder", e.target.value)}
                      >
                        <MenuItem value="DESC">Descending</MenuItem>
                        <MenuItem value="ASC">Ascending</MenuItem>
                      </Select>
                    </StyledFormControl>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <StyledFormControl fullWidth size="small">
                      <InputLabel>Per Page</InputLabel>
                      <Select
                        value={filters.limit}
                        label="Per Page"
                        onChange={(e) => handleFilterChange("limit", e.target.value)}
                      >
                        <MenuItem value={5}>5</MenuItem>
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                        <MenuItem value={100}>100</MenuItem>
                        <MenuItem value={500}>500</MenuItem>
                      </Select>
                    </StyledFormControl>
                  </Grid>
                  <Grid item xs={12} md={1}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      {filters.search && (
                        <Chip
                          label={`Search: ${filters.search}`}
                          onDelete={() => handleFilterChange("search", "")}
                          size="small"
                          sx={{ backgroundColor: "#1db954", color: "white" }}
                        />
                      )}
                      {filters.status && (
                        <Chip
                          label={`Status: ${filters.status}`}
                          onDelete={() => handleFilterChange("status", "")}
                          size="small"
                          sx={{ backgroundColor: "#1db954", color: "white" }}
                        />
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </StyledPaper>

              {/* Pagination Info */}
              {renderPaginationInfo()}

              <SongsTable
                songs={songs}
                onEdit={handleEditSong}
                onSpotifySearch={handleSpotifySearch}
                loadingSearch={loadingSearch}
                loading={loading}
              />

              {/* Pagination Controls */}
              <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
                <Pagination
                  count={pagination.totalPages}
                  page={pagination.currentPage}
                  onChange={handlePageChange}
                  color="primary"
                  size="large"
                  disabled={loading}
                  sx={{
                    "& .MuiPaginationItem-root": {
                      color: "white",
                      "&.Mui-selected": {
                        backgroundColor: "#1db954",
                        color: "white",
                      },
                      "&:hover": {
                        backgroundColor: "rgba(29, 185, 84, 0.1)",
                      },
                    },
                  }}
                />
              </Box>

              <EditSongDialog
                song={editSong}
                open={!!editSong}
                onClose={() => setEditSong(null)}
                onSave={handleSaveEdit}
              />

              <SpotifySearchDialog
                open={searchDialogOpen}
                spotifySearchSongId={spotifySearchSongId}
                onClose={() => setSearchDialogOpen(false)}
                searchResult={searchResult}
                onSave={handleSongUpdate}
              />
            </>
          )}

          {tabIndex === 1 && (
            <SpotifyTracksTable
              tracks={spotifyTracks}
              onEdit={handleEditSpotifyTrack}
              fetchTracks={fetchSpotifyTracks}
              onMigrationComplete={handleMigrationComplete}
              onSongUpdate={handleSongUpdate}
              urlRefreshTrigger={urlRefreshTrigger}
            />
          )}

          {tabIndex === 2 && (
            <MigratedTracksTable
              tracks={migratedTracks}
              fetchTracks={fetchMigratedTracks}
            />
          )}

          <EditSpotifyTrackDialog
            track={editSpotifyTrack}
            open={!!editSpotifyTrack}
            onClose={() => setEditSpotifyTrack(null)}
            onSave={handleSaveSpotifyTrack}
            onSongUpdate={handleSongUpdate}
            onUrlAdded={handleUrlAdded}
          />
        </Box>
      </Container>
    </>
  );
};

export default ImportedSongs;