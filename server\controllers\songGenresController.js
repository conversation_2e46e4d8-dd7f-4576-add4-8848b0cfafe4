const SongGenresModel = require("../models/songGenresModel");

const SongGenresController = {
  create: async (req, res) => {
    try {
      const id = await SongGenresModel.create(req.body);
      res.status(201).json(id);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getAll: async (req, res) => {
    try {
      const result = await SongGenresModel.getAll();
      res.json(result);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getById: async (req, res) => {
    try {
      const result = await SongGenresModel.getById(req.params.id);
      if (!result) return res.status(404).json({ error: "Not found" });
      res.json(result);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  update: async (req, res) => {
    try {
      const updated = await SongGenresModel.update(req.params.id, req.body);
      res.json(updated);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  delete: async (req, res) => {
    try {
      const deleted = await SongGenresModel.delete(req.params.id);
      res.json(deleted);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getBySongId: async (req, res) => {
    try {
      const rows = await SongGenresModel.getBySongId(req.params.songId);
      res.json(rows);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  deleteBySongId: async (req, res) => {
    try {
      const deleted = await SongGenresModel.deleteBySongId(req.params.songId);
      res.json(deleted);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },
};

module.exports = SongGenresController;