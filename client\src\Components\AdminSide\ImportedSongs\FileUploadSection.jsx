import React, { useState } from "react";
import { Box, Button, Input } from "@mui/material";

const FileUploadSection = ({ onUpload }) => {
  const [file, setFile] = useState(null);

  const handleUpload = () => {
    if (file) {
      onUpload(file);
      setFile(null);
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
      <Input
        type="file"
        onChange={(e) => setFile(e.target.files[0])}
        inputProps={{ accept: ".xlsx, .xls" }}
      />
      <Button variant="contained" onClick={handleUpload} disabled={!file}>
        Upload Excel
      </Button>
    </Box>
  );
};

export default FileUploadSection;