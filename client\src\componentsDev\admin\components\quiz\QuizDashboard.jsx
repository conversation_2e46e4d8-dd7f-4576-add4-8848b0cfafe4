import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Ta<PERSON>,
  Tab,
  Badge,
  Alert,
  Fade,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Quiz as QuizIcon,
  Add as AddIcon,
  List as ListIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import QuizQuestionForm from './QuizQuestionForm';
import QuizQuestionManagement from './QuizQuestionManagement';
import QuizSettings from './QuizSettings';
import { useQuizQuestionStats } from '../../../hooks/useQuizQuestions';

// Styled components following SongQ design system
const DashboardContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#121212',
  minHeight: '100vh',
  padding: '24px',
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  borderRadius: '12px',
  padding: '8px',
  marginBottom: '24px',
  '& .MuiTabs-indicator': {
    backgroundColor: '#4CAF50',
    height: '3px',
    borderRadius: '3px',
  },
  '& .MuiTab-root': {
    color: '#ccc',
    textTransform: 'none',
    fontWeight: 600,
    fontSize: '1rem',
    minHeight: '48px',
    borderRadius: '8px',
    margin: '0 4px',
    '&.Mui-selected': {
      color: '#4CAF50',
      backgroundColor: 'rgba(76, 175, 80, 0.1)',
    },
    '&:hover': {
      backgroundColor: 'rgba(76, 175, 80, 0.05)',
    },
  },
}));

const WelcomeCard = styled(Card)(({ theme }) => ({
  backgroundColor: 'linear-gradient(135deg, #1A1A1A 0%, #2A2A2A 100%)',
  border: '1px solid #333',
  borderRadius: '16px',
  marginBottom: '24px',
  overflow: 'hidden',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #4CAF50, #2196F3, #FF9800)',
  },
}));

const StatsGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(125px, 1fr))',
  gap: '16px',
  marginBottom: '24px',
}));

const StatCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: '#4CAF50',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 24px rgba(76, 175, 80, 0.1)',
  },
}));

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`quiz-tabpanel-${index}`}
      aria-labelledby={`quiz-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Fade in={true} timeout={300}>
          <Box>{children}</Box>
        </Fade>
      )}
    </div>
  );
}

const QuizDashboard = () => {
  const [activeTab, setActiveTab] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { data: stats, isLoading: statsLoading } = useQuizQuestionStats();

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const tabs = [
    {
      label: 'Overview',
      icon: <AnalyticsIcon />,
      badge: null,
    },
    {
      label: 'Create Question',
      icon: <AddIcon />,
      badge: null,
    },
    {
      label: 'Manage Questions',
      icon: <ListIcon />,
      badge: stats?.totalQuestions || 0,
    },
    // {
    //   label: 'Settings',
    //   icon: <SettingsIcon />,
    //   badge: null,
    // },
  ];

  return (
    <DashboardContainer>
      {/* Header */}
      <Box mb={3}>
        <Typography 
          variant="h3" 
          sx={{ 
            color: '#fff', 
            fontWeight: 700, 
            mb: 1,
            background: 'linear-gradient(45deg, #4CAF50, #2196F3)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Quiz Management Dashboard
        </Typography>
        <Typography variant="h6" sx={{ color: '#ccc', fontWeight: 400 }}>
          Create, manage, and analyze your quiz questions
        </Typography>
      </Box>

      {/* Welcome Card */}
      <WelcomeCard>
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <QuizIcon sx={{ fontSize: 48, color: '#4CAF50' }} />
            <Box>
              <Typography variant="h5" sx={{ color: '#fff', fontWeight: 600, mb: 1 }}>
                Welcome to Quiz Management
              </Typography>
              <Typography variant="body1" sx={{ color: '#ccc' }}>
                Create engaging quiz questions, manage your question bank, and track performance metrics.
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </WelcomeCard>

      {/* Quick Stats */}
      {stats && !statsLoading && (
        <StatsGrid>
          <StatCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h3" sx={{ color: '#4CAF50', fontWeight: 700 }}>
                {stats.totalQuestions}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Total Questions
              </Typography>
            </CardContent>
          </StatCard>

          <StatCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h3" sx={{ color: '#2196F3', fontWeight: 700 }}>
                {stats.questionsByDifficulty?.length || 0}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Difficulty Levels
              </Typography>
            </CardContent>
          </StatCard>

          <StatCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h3" sx={{ color: '#FF9800', fontWeight: 700 }}>
                {Math.round(stats.averageTimeLimit / 60)}m
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Avg. Time Limit
              </Typography>
            </CardContent>
          </StatCard>

          <StatCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h3" sx={{ color: '#9C27B0', fontWeight: 700 }}>
                {stats.mostUsedSongs?.length || 0}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Songs with Questions
              </Typography>
            </CardContent>
          </StatCard>
        </StatsGrid>
      )}

      {/* Navigation Tabs */}
      <StyledTabs
        value={activeTab}
        onChange={handleTabChange}
        variant={isMobile ? "scrollable" : "fullWidth"}
        scrollButtons={isMobile ? "auto" : false}
        allowScrollButtonsMobile 
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            icon={tab.icon}
            label={
              tab.badge !== null ? (
                <Badge badgeContent={tab.badge} color="primary">
                  {tab.label}
                </Badge>
              ) : (
                tab.label
              )
            }
            iconPosition="start"
          />
        ))}
      </StyledTabs>

      {/* Tab Panels */}
      <TabPanel value={activeTab} index={0}>
        {/* Overview Panel */}
        <Box>
          <Typography variant="h5" sx={{ color: '#fff', mb: 3, fontWeight: 600 }}>
            Quiz Analytics Overview
          </Typography>
          
          {stats ? (
            <Box>
              {/* Difficulty Distribution */}
              {stats.questionsByDifficulty && stats.questionsByDifficulty.length > 0 && (
                <Card sx={{ backgroundColor: '#1A1A1A', border: '1px solid #333', mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                      Questions by Difficulty
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={2}>
                      {stats.questionsByDifficulty.map((item, index) => (
                        <Box
                          key={index}
                          sx={{
                            backgroundColor: '#121212',
                            border: '1px solid #333',
                            borderRadius: '8px',
                            p: 2,
                            minWidth: '120px',
                            textAlign: 'center',
                          }}
                        >
                          <Typography variant="h4" sx={{ color: '#4CAF50', fontWeight: 600 }}>
                            {item.count}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#ccc' }}>
                            {item.level_name}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Most Used Songs */}
              {stats.mostUsedSongs && stats.mostUsedSongs.length > 0 && (
                <Card sx={{ backgroundColor: '#1A1A1A', border: '1px solid #333' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                      Most Popular Songs
                    </Typography>
                    <Box display="flex" flexDirection="column" gap={1}>
                      {stats.mostUsedSongs.slice(0, 5).map((song, index) => (
                        <Box
                          key={index}
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          sx={{
                            backgroundColor: '#121212',
                            border: '1px solid #333',
                            borderRadius: '6px',
                            p: 2,
                          }}
                        >
                          <Box>
                            <Typography variant="body1" sx={{ color: '#fff' }}>
                              {song.name}
                            </Typography>
                            {song.artist && (
                              <Typography variant="body2" sx={{ color: '#ccc' }}>
                                by {song.artist}
                              </Typography>
                            )}
                          </Box>
                          <Typography variant="h6" sx={{ color: '#4CAF50', fontWeight: 600 }}>
                            {song.question_count}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              )}
            </Box>
          ) : (
            <Alert severity="info" sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)' }}>
              No quiz data available yet. Create your first question to see analytics.
            </Alert>
          )}
        </Box>
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        {/* Create Question Panel */}
        <QuizQuestionForm onSuccess={() => setActiveTab(2)} />
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        {/* Manage Questions Panel */}
        <QuizQuestionManagement />
      </TabPanel>

      <TabPanel value={activeTab} index={3}>
        {/* Settings Panel */}
        <QuizSettings />
      </TabPanel>
    </DashboardContainer>
  );
};

export default QuizDashboard;
