import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Field,
  Typo<PERSON>,
  Button,
} from "@mui/material";
import { songService } from "./songService";

const PlaylistSection = ({ selectedPlaylists, setSelectedPlaylists, selectedSongId }) => {
  const [allPlaylists, setAllPlaylists] = useState([]);
  const [searchPlaylistTerm, setSearchPlaylistTerm] = useState("");

  useEffect(() => {
    const fetchPlaylists = async () => {
      try {
        const playlistsData = await songService.fetchPlaylists();
        setAllPlaylists(playlistsData);
      } catch (err) {
        console.error("Failed to load playlists:", err);
      }
    };
    fetchPlaylists();
  }, []);

  useEffect(() => {
    if (selectedSongId) {
      const fetchSongPlaylists = async () => {
        try {
          const playlistIds = await songService.fetchSongPlaylists(selectedSongId);
          setSelectedPlaylists(playlistIds);
        } catch (err) {
          console.error("Failed to fetch song playlists:", err);
        }
      };
      fetchSongPlaylists();
    }
  }, [selectedSongId, setSelectedPlaylists]);

  const filteredPlaylists = allPlaylists.filter((pl) =>
    pl.title.toLowerCase().includes(searchPlaylistTerm.toLowerCase())
  );

  const handleAddPlaylist = (playlist) => {
    if (!selectedPlaylists.includes(playlist.playlist_master_id)) {
      setSelectedPlaylists((prev) => [...prev, playlist.playlist_master_id]);
    }
  };

  const handleRemovePlaylist = async (playlist) => {
    try {
      if (selectedSongId) {
        await songService.removePlaylistFromSong(playlist.playlist_master_id, selectedSongId);
      }
      setSelectedPlaylists((prev) =>
        prev.filter((id) => id !== playlist.playlist_master_id)
      );
    } catch (error) {
      console.error("Failed to remove playlist:", error);
    }
  };

  return (
    <>
      <TextField
        label="Search Playlists"
        value={searchPlaylistTerm}
        onChange={(e) => setSearchPlaylistTerm(e.target.value)}
        fullWidth
        margin="normal"
      />

      <Typography variant="caption" sx={{ display: "block", mb: 1 }}>
        {selectedPlaylists.length} playlist
        {selectedPlaylists.length !== 1 ? "s" : ""} selected
      </Typography>

      <Box
        sx={{
          maxHeight: 250,
          overflowY: "auto",
          border: "1px solid #ccc",
          borderRadius: 2,
          p: 1,
        }}
      >
        {filteredPlaylists.map((playlist) => {
          return (
            <Box
              key={playlist.playlist_master_id}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              p={1}
              borderBottom="1px solid #eee"
            >
              <Typography>{playlist.title}</Typography>
              <Button
                size="small"
                variant="contained"
                color={
                  selectedPlaylists.includes(
                    playlist.playlist_master_id
                  )
                    ? "error"
                    : "primary"
                }
                onClick={() =>
                  selectedPlaylists.includes(
                    playlist.playlist_master_id
                  )
                    ? handleRemovePlaylist(playlist)
                    : handleAddPlaylist(playlist)
                }
              >
                {selectedPlaylists.includes(
                  playlist.playlist_master_id
                )
                  ? "-"
                  : "+"}
              </Button>
            </Box>
          );
        })}
      </Box>

      {filteredPlaylists.length === 0 && (
        <Typography variant="body2" sx={{ mt: 1, color: "gray" }}>
          No playlists found.
        </Typography>
      )}
    </>
  );
};

export default PlaylistSection;