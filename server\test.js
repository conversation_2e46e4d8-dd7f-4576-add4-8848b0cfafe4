const axios = require('axios');

// Query to fetch all artists with their name and imageUrl
const query = `
  query {
    getAllArtists {
      name
      imageUrl
    }
  }
`;

// Making a POST request to the server
axios.post('http://localhost:3000/graphql', {
  query: query
}, {
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 5000  // Set a timeout of 5 seconds
})
.then(response => {
  // Print the full response to verify data
  console.log('Response:', JSON.stringify(response.data, null, 2));
})
.catch(error => {
  if (error.response) {
    console.error('Error Response Data:', error.response.data);
    console.error('Error Response Status:', error.response.status);
    console.error('Error Response Headers:', error.response.headers);
  } else if (error.request) {
    console.error('Error Request:', error.request);
  } else {
    console.error('Error Message:', error.message);
  }
});