const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");
const SpotifyWebApi = require("spotify-web-api-node");

// Initialize Spotify API
const spotifyApi = new SpotifyWebApi({
  clientId: process.env.SPOTIFY_CLIENT_ID,
  clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
});

const MigrateModel = {
  // Helper function to get artist by name
  getArtistByName: (artistName) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT artist_id, name FROM artists_master WHERE name = ?`,
        [artistName],
        (err, row) => {
          if (err) return reject(err);
          resolve(row || null);
        }
      );
    });
  },

  // Helper function to create new artist with Spotify API
  createArtist: async (artistName) => {
    try {
      // Get Spotify access token
      const tokenData = await spotifyApi.clientCredentialsGrant();
      spotifyApi.setAccessToken(tokenData.body.access_token);

      // Search for artist on Spotify
      const result = await spotifyApi.searchArtists(artistName, { limit: 1 });

      if (result.body.artists.items.length > 0) {
        const spotifyArtist = result.body.artists.items[0];

        // Insert artist with Spotify ID
        return new Promise((resolve, reject) => {
          db.run(
            `INSERT INTO artists_master (artist_id, name, image) VALUES (?, ?, ?)`,
            [spotifyArtist.id, spotifyArtist.name, spotifyArtist.images?.[0]?.url || null],
            function (err) {
              if (err) return reject(err);
              resolve(spotifyArtist.id);
            }
          );
        });
      } else {
        // Artist not found on Spotify, create with local ID
        const localArtistId = `local_artist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        return new Promise((resolve, reject) => {
          db.run(
            `INSERT INTO artists_master (artist_id, name) VALUES (?, ?)`,
            [localArtistId, artistName],
            function (err) {
              if (err) return reject(err);
              resolve(localArtistId);
            }
          );
        });
      }
    } catch (error) {
      console.error("Spotify API error during artist creation:", error);

      // Fallback to local artist ID if Spotify API fails
      const localArtistId = `local_artist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO artists_master (artist_id, name) VALUES (?, ?)`,
          [localArtistId, artistName],
          function (err) {
            if (err) return reject(err);
            resolve(localArtistId);
          }
        );
      });
    }
  },

  checkIfMigrated: (spotifyTrackId) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT songs_master_id FROM songs_master 
         WHERE image = (SELECT image FROM ImportSpotifyTrackTemp WHERE spotify_track_id = ?)`,
        [spotifyTrackId],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });
  },

  migrateTrackAndUrls: async (importSpotifyTrackId) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 1. Get the track to migrate
        db.get(
          `SELECT * FROM ImportSpotifyTrackTemp WHERE id = ?`,
          [importSpotifyTrackId],
          (err, track) => {
            if (err || !track) {
              db.run("ROLLBACK");
              return reject(err || new Error("Track not found"));
            }

            // 2. Insert into songs_master
            db.run(
              `INSERT INTO songs_master (name, artist, album, release_year, decade, image, duration, spotify_id)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                track.name,
                track.artist,
                track.album,
                track.release_year,
                track.decade,
                track.image,
                track.duration,
                track.spotify_track_id,
              ],
              function (err) {
                if (err) {
                  db.run("ROLLBACK");
                  return reject(err);
                }
                const newSongId = this.lastID;

                // 3. Get all URLs for this track
                db.all(
                  `SELECT * FROM ImportTrackUrlsTemp WHERE track_id = ?`,
                  [importSpotifyTrackId],
                  (err, urls) => {
                    if (err) {
                      db.run("ROLLBACK");
                      return reject(err);
                    }

                    // 4. Migrate each URL
                    const migrateUrl = (url, callback) => {
                      db.run(
                        `INSERT INTO song_urls (songs_master_id, song_url, url_type_lookup_id)
                         VALUES (?, ?, ?)`,
                        [newSongId, url.song_url, url.url_type_lookup_id],
                        (err) => callback(err)
                      );
                    };

                    const migrateAllUrls = (index) => {
                      if (index >= urls.length) {
                        // 5. Handle artist linking after URLs are migrated
                        handleArtistLinking(track.import_song_id, newSongId, track.artist, (err) => {
                          if (err) {
                            db.run("ROLLBACK");
                            return reject(err);
                          }

                          // 6. Mark the temp track as migrated
                          db.run(
                            `UPDATE ImportSpotifyTrackTemp SET is_migrated = 1 WHERE id = ?`,
                            [importSpotifyTrackId],
                            (err) => {
                              if (err) {
                                db.run("ROLLBACK");
                                return reject(err);
                              }
                              // 7. Delete from ImportSongTableTemp
                              db.run(
                                `DELETE FROM ImportSongTableTemp WHERE id = ?`,
                                [track.import_song_id],
                                (err) => {
                                  if (err) {
                                    db.run("ROLLBACK");
                                    return reject(err);
                                  }
                                  db.run("COMMIT");
                                  return resolve({ migrated: true, newSongId });
                                }
                              );
                            }
                          );
                        });
                        return;
                      }

                      migrateUrl(urls[index], (err) => {
                        if (err) {
                          db.run("ROLLBACK");
                          return reject(err);
                        }
                        migrateAllUrls(index + 1);
                      });
                    };

                    migrateAllUrls(0);
                  }
                );
              }
            );
          }
        );

        // Helper function to handle artist linking
        const handleArtistLinking = async (importSongId, newSongId, trackArtistName, callback) => {
          try {
            // First, handle artists from temp table (if any)
            const tempArtists = await new Promise((resolve, reject) => {
              db.all(
                `SELECT * FROM ImportSpotifyArtistTemp WHERE import_song_id = ?`,
                [importSongId],
                (err, artists) => {
                  if (err) reject(err);
                  else resolve(artists);
                }
              );
            });

            // Process temp artists if they exist
            if (tempArtists.length > 0) {
              for (const artist of tempArtists) {
                // Ensure artist exists in artists_master
                await new Promise((resolve, reject) => {
                  db.run(
                    `INSERT OR IGNORE INTO artists_master (artist_id, name, image)
                     VALUES (?, ?, ?)`,
                    [artist.artist_id, artist.name, artist.image],
                    (err) => {
                      if (err) reject(err);
                      else resolve();
                    }
                  );
                });

                // Link the song to the artist
                await new Promise((resolve, reject) => {
                  db.run(
                    `INSERT OR IGNORE INTO song_artists (songs_master_id, artist_id)
                     VALUES (?, ?)`,
                    [newSongId, artist.artist_id],
                    (err) => {
                      if (err) reject(err);
                      else resolve();
                    }
                  );
                });
              }
            } else {
              // No temp artists, handle the track's artist name
              if (trackArtistName) {
                // Check if artist exists by name
                const existingArtist = await MigrateModel.getArtistByName(trackArtistName);

                if (existingArtist) {
                  // Artist exists - link to existing artist
                  await new Promise((resolve, reject) => {
                    db.run(
                      `INSERT OR IGNORE INTO song_artists (songs_master_id, artist_id)
                       VALUES (?, ?)`,
                      [newSongId, existingArtist.artist_id],
                      (err) => {
                        if (err) reject(err);
                        else resolve();
                      }
                    );
                  });
                } else {
                  // Artist doesn't exist - create new artist with Spotify API
                  try {
                    const newArtistId = await MigrateModel.createArtist(trackArtistName);

                    // Link the song to the new artist
                    await new Promise((resolve, reject) => {
                      db.run(
                        `INSERT OR IGNORE INTO song_artists (songs_master_id, artist_id)
                         VALUES (?, ?)`,
                        [newSongId, newArtistId],
                        (err) => {
                          if (err) reject(err);
                          else resolve();
                        }
                      );
                    });
                  } catch (error) {
                    console.error("Error creating artist:", error);
                    // Continue without linking if artist creation fails
                  }
                }
              }
            }

            callback(null);
          } catch (error) {
            callback(error);
          }
        };
      });
    });
  },

  // migrateAllTracks: async (trackIds = null) => {
  //   // Add optional trackIds parameter
  //   return new Promise((resolve, reject) => {
  //     db.all(
  //       trackIds
  //         ? `SELECT id FROM ImportSpotifyTrackTemp WHERE id IN (${trackIds
  //           .map(() => "?")
  //           .join(",")})`
  //         : `SELECT id FROM ImportSpotifyTrackTemp`,
  //       trackIds || [],
  //       async (err, tracks) => {
  //         if (err) return reject(err);

  //         const results = {
  //           success: [],
  //           failed: [],
  //           errors: [],
  //         };

  //         // Process tracks in series to avoid SQLITE_BUSY errors and Spotify API rate limits
  //         for (let i = 0; i < tracks.length; i++) {
  //           const track = tracks[i];
  //           try {
  //             console.log(`Migrating track ${i + 1}/${tracks.length}: ${track.id}`);

  //             const result = await MigrateModel.migrateTrackAndUrls(track.id);
  //             results.success.push({
  //               trackId: track.id,
  //               newSongId: result.newSongId,
  //             });

  //             // Add a small delay between migrations to avoid Spotify API rate limits
  //             if (i < tracks.length - 1) {
  //               await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
  //             }
  //           } catch (e) {
  //             console.error(`Failed to migrate track ${track.id}:`, e.message);
  //             results.failed.push(track.id);
  //             results.errors.push({
  //               trackId: track.id,
  //               error: e.message,
  //             });

  //             // Continue with next track even if one fails
  //           }
  //         }

  //         console.log(`Migration completed: ${results.success.length} successful, ${results.failed.length} failed`);
  //         resolve(results);
  //       }
  //     );
  //   });
  // },

  migrateAllTracks: async (trackIds = null) => {
    return new Promise((resolve, reject) => {
      db.all(
        trackIds
          ? `SELECT id FROM ImportSpotifyTrackTemp WHERE id IN (${trackIds.map(() => "?").join(",")})`
          : `SELECT id FROM ImportSpotifyTrackTemp`,
        trackIds || [],
        async (err, tracks) => {
          if (err) return reject(err);

          const results = { success: [], failed: [], errors: [] };

          for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];
            try {
              console.log(`Migrating track ${i + 1}/${tracks.length}: ${track.id}`);
              const result = await MigrateModel.migrateTrackAndUrls(track.id);
              results.success.push({ trackId: track.id, newSongId: result.newSongId });
              await new Promise(r => setTimeout(r, 200)); // delay
            } catch (e) {
              console.error(`Failed to migrate track ${track.id}:`, e.message);
              results.failed.push(track.id);
              results.errors.push({ trackId: track.id, error: e.message });
            }
          }

          console.log(`Migration completed: ${results.success.length} successful, ${results.failed.length} failed`);

          // Example: Save results in DB (you can also write to a file or Redis)
          // db.run(
          //   `INSERT INTO MigrationLogs (date, success_count, failed_count, details) VALUES (?, ?, ?, ?)`,
          //   [new Date().toISOString(), results.success.length, results.failed.length, JSON.stringify(results)]
          // );

          resolve(results);
        }
      );
    });
  },


  deleteTempTrack: (id) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // Delete associated URLs first
        db.run(
          `DELETE FROM ImportTrackUrlsTemp WHERE track_id = ?`,
          [id],
          (err) => {
            if (err) {
              db.run("ROLLBACK");
              return reject(err);
            }

            // Then delete the track
            db.run(
              `DELETE FROM ImportSpotifyTrackTemp WHERE id = ?`,
              [id],
              function (err) {
                if (err) {
                  db.run("ROLLBACK");
                  return reject(err);
                }
                db.run("COMMIT");
                resolve({ deleted: this.changes });
              }
            );
          }
        );
      });
    });
  },

  // In your MigrateModel
  getAllMigratedTracks: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT 
        sm.songs_master_id as _id,
        sm.spotify_id as spotify_track_id,
        sm.name as track_name,
        sm.artist as artist_name,
        sm.album as album_name,
        sm.release_year as year,
        sm.duration,
        sm.image,
        sm.created_at as migration_date
       FROM songs_master sm
       WHERE EXISTS (
         SELECT 1 FROM ImportSpotifyTrackTemp ist 
         WHERE ist.image = sm.image
       )`,
        (err, rows) => (err ? reject(err) : resolve(rows))
      );
    });
  },

  getMigratedTrack: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT 
        sm.songs_master_id as _id,
        sm.name as track_name,
        sm.artist as artist_name,
        sm.album as album_name,
        sm.release_year as year,
        sm.duration,
        sm.image,
        sm.created_at as migration_date
       FROM songs_master sm
       WHERE sm.songs_master_id = ?`,
        [id],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });
  },

  deleteMigratedTrack: (id) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 1. Delete song-artist relationships first
        db.run(
          `DELETE FROM song_artists WHERE songs_master_id = ?`,
          [id],
          function (err) {
            if (err) {
              db.run("ROLLBACK");
              return reject(err);
            }

            // 2. Delete song URLs
            db.run(
              `DELETE FROM song_urls WHERE songs_master_id = ?`,
              [id],
              function (err) {
                if (err) {
                  db.run("ROLLBACK");
                  return reject(err);
                }

                // 3. Finally delete the song
                db.run(
                  `DELETE FROM songs_master WHERE songs_master_id = ?`,
                  [id],
                  function (err) {
                    if (err) {
                      db.run("ROLLBACK");
                      return reject(err);
                    }

                    db.run("COMMIT");
                    resolve({ deleted: this.changes });
                  }
                );
              }
            );
          }
        );
      });
    });
  },

  restoreToTemp: (id) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 1. Get the track from songs_master
        db.get(
          `SELECT * FROM songs_master WHERE songs_master_id = ?`,
          [id],
          (err, track) => {
            if (err || !track) {
              db.run("ROLLBACK");
              return reject(err || new Error("Track not found"));
            }

            // 2. Get associated artists before deleting
            db.all(
              `SELECT am.* FROM artists_master am
             JOIN song_artists sa ON am.artist_id = sa.artist_id
             WHERE sa.songs_master_id = ?`,
              [id],
              (err, artists) => {
                if (err) {
                  db.run("ROLLBACK");
                  return reject(err);
                }

                // 2.5 Create a new entry in ImportSongTableTemp to get import_song_id
                db.run(
                  `INSERT OR IGNORE INTO ImportSongTableTemp (song_name, artist_name, status, video_url) 
   VALUES (?, ?, 'pending', NULL)`,
                  [track.name, track.artist],
                  function (err) {
                    if (err) {
                      db.run("ROLLBACK");
                      return reject(err);
                    }

                    const handleImportSongId = (importSongId) => {
                      // 3. Insert into ImportSpotifyTrackTemp
                        db.run(
                          `INSERT INTO ImportSpotifyTrackTemp 
         (import_song_id, spotify_track_id, name, artist, album, release_year, decade, image, duration)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                          importSongId,
                          track.spotify_id,
                          track.name,
                          track.artist,
                          track.album,
                          track.release_year,
                          Math.floor(track.release_year / 10) * 10,
                          track.image,
                          track.duration,
                        ],
                        function (err) {
                          if (err) {
                            db.run("ROLLBACK");
                            return reject(err);
                          }

                          const newTempId = this.lastID;

                          // 4. Restore artists to temp table if they exist
                          if (artists.length > 0) {
                            const restoreArtist = (artist, artistCallback) => {
                              db.run(
                                `INSERT INTO ImportSpotifyArtistTemp 
                               (import_song_id, artist_id, name, image)
                             VALUES (?, ?, ?, ?)`,
                                [importSongId, artist.artist_id, artist.name, artist.image],
                                (err) => artistCallback(err)
                              );
                            };

                            const restoreAllArtists = (index) => {
                              if (index >= artists.length) {
                                // 5. Delete from songs_master after restoring everything
                                db.run(
                                  `DELETE FROM songs_master WHERE songs_master_id = ?`,
                                  [id],
                                  function (err) {
                                    if (err) {
                                      db.run("ROLLBACK");
                                      return reject(err);
                                    }

                                    db.run("COMMIT");
                                    resolve({
                                      id: newTempId,
                                      import_song_id: importSongId,
                                      name: track.name,
                                      artist: track.artist,
                                      artistsRestored: artists.length,
                                    });
                                  }
                                );
                                return;
                              }

                              restoreArtist(artists[index], (err) => {
                                if (err) {
                                  db.run("ROLLBACK");
                                  return reject(err);
                                }
                                restoreAllArtists(index + 1);
                              });
                            };

                            restoreAllArtists(0);
                          } else {
                            // No artists to restore, just delete from songs_master
                            db.run(
                              `DELETE FROM songs_master WHERE songs_master_id = ?`,
                              [id],
                              function (err) {
                                if (err) {
                                  db.run("ROLLBACK");
                                  return reject(err);
                                }

                                db.run("COMMIT");
                                resolve({
                                  id: newTempId,
                                  import_song_id: importSongId,
                                  name: track.name,
                                  artist: track.artist,
                                  artistsRestored: 0,
                                });
                              }
                            );
                          }
                        }
                      );
                    };

                    // If INSERT OR IGNORE did not insert (unique conflict), fetch existing id
                    if (this.changes === 0) {
                      db.get(
                        `SELECT id FROM ImportSongTableTemp WHERE song_name = ? AND artist_name = ?`,
                        [track.name, track.artist],
                        (err, row) => {
                          if (err) {
                            db.run("ROLLBACK");
                            return reject(err);
                          }
                          if (!row) {
                            db.run("ROLLBACK");
                            return reject(new Error("Failed to obtain ImportSongTableTemp id"));
                          }
                          handleImportSongId(row.id);
                        }
                      );
                    } else {
                      // Insert succeeded, use lastID
                      handleImportSongId(this.lastID);
                    }
                  }
                );
              }
            );
          }
        );
      });
    });
  },

  // restoreAllToTemp: (ids = null) => {
  //   return new Promise((resolve, reject) => {
  //     const selectQuery =
  //       ids && ids.length
  //         ? `SELECT songs_master_id FROM songs_master WHERE songs_master_id IN (${ids.map(() => '?').join(',')})`
  //         : `SELECT songs_master_id FROM songs_master`;
  //     const params = ids && ids.length ? ids : [];

  //     db.all(selectQuery, params, async (err, rows) => {
  //       if (err) return reject(err);
  //       if (!rows || rows.length === 0) {
  //         return resolve({
  //           success: [],
  //           failed: [],
  //           errors: [],
  //           message: "No matching tracks found",
  //         });
  //       }

  //       const results = { success: [], failed: [], errors: [] };

  //       // Process sequentially to avoid DB locks
  //       for (let i = 0; i < rows.length; i++) {
  //         const sid = rows[i].songs_master_id;
  //         try {
  //           // restoreToTemp should:
  //           //  1. Insert into ImportSpotifyTrackTemp
  //           //  2. If insert succeeds → delete from songs_master
  //           //  3. If insert fails → throw (and DON'T delete from songs_master)
  //           const result = await MigrateModel.restoreToTemp(sid);

  //           results.success.push({
  //             songs_master_id: sid,
  //             restoredTempId: result.id,
  //             import_song_id: result.import_song_id,
  //           });
  //         } catch (e) {
  //           console.error(
  //             "[restoreAllToTemp] failed for id=",
  //             sid,
  //             e.message || e
  //           );

  //           // keep original record safe in songs_master
  //           results.failed.push(sid);
  //           results.errors.push({
  //             songs_master_id: sid,
  //             error: e.message || String(e),
  //           });
  //         }
  //       }

  //       resolve(results);
  //     });
  //   });
  // },

  restoreAllToTemp: async (trackIds = null) => {
    return new Promise((resolve, reject) => {
      // Get all songs_master ids
      db.all(
        trackIds
          ? `SELECT songs_master_id as id FROM songs_master WHERE songs_master_id IN (${trackIds.map(() => "?").join(",")})`
          : `SELECT songs_master_id as id FROM songs_master`,
        trackIds || [],
        async (err, tracks) => {
          if (err) return reject(err);

          const results = { success: [], failed: [], errors: [] };

          for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];
              try {
              console.log(`Restoring track ${i + 1}/${tracks.length}: ${track.id}`);

              const result = await MigrateModel.restoreToTemp(track.id);

              results.success.push({
                trackId: track.id,
                restoredTempId: result.id,
                import_song_id: result.import_song_id,
              });

              await new Promise(r => setTimeout(r, 200)); // delay
            } catch (e) {
              console.error(`Failed to restore track ${track.id}:`, e.message);
              results.failed.push(track.id);
              results.errors.push({ trackId: track.id, error: e.message });
            }
          }

          console.log(`Restore completed: ${results.success.length} successful, ${results.failed.length} failed`);
          resolve(results);
        }
      );
    });
  },


};

module.exports = MigrateModel;
