const MigrateModel = require("../models/migrateTempToMainModel");

exports.checkIfMigrated = async (req, res) => {
  try {
    const { spotify_track_id } = req.params;
    const result = await MigrateModel.checkIfMigrated(spotify_track_id);
    res.status(200).json({
      migrated: !!result,
      songId: result?.id, // Include the existing song ID if already migrated
    });
  } catch (err) {
    console.error("❌ Migration check error:", err);
    res.status(500).json({ error: "Check failed" });
  }
};

exports.migrateSingle = async (req, res) => {
  const { id } = req.params; // Changed from import_song_id to id to match model
  try {
    const result = await MigrateModel.migrateTrackAndUrls(id);
    res.json({
      message: "✅ Track migrated successfully",
      newSongId: result.newSongId,
      urlsMigrated: result.urlsCount, // You might want to add this to your model
    });
  } catch (err) {
    console.error("❌ Migration error:", err.message);
    res.status(500).json({
      error: "Migration failed",
      details: err.message,
    });
  }
};

exports.migrateAll = async (req, res, next) => {
  try {
    const { track_ids } = req.body || {};

    // Start migration in background (don’t await)
    MigrateModel.migrateAllTracks(track_ids)
      .then(result => {
        console.log("✅ Migration finished:", result.success.length, "success,", result.failed.length, "failed");
        // TODO: Store result in DB, Redis, or file if you want to query later
      })
      .catch(err => {
        console.error("❌ Migration error:", err);
        // TODO: Save error info somewhere persistent if you want to fetch later
      });

    // Respond instantly
    res.json({
      success: true,
      message: "Migration started in background",
      totalTracks: track_ids?.length || "all",
    });

  } catch (err) {
    console.error("❌ Failed to start migration:", err);
    res.status(500).json({
      success: false,
      error: "Migration start failed",
      details: err.message,
    });
  }
};


exports.deleteTempTrack = async (req, res) => {
  const { id } = req.params;
  try {
    const result = await MigrateModel.deleteTempTrack(id);
    if (result.deleted === 0) {
      return res.status(404).json({ error: "Track not found" });
    }
    res.json({
      message: "🗑️ Temporary track deleted successfully",
      deletedId: id,
    });
  } catch (err) {
    console.error("❌ Deletion error:", err.message);
    res.status(500).json({
      error: "Deletion failed",
      details: err.message,
    });
  }
};

// In your migrate controller
exports.getAllMigratedTracks = async (req, res) => {
  try {
    const tracks = await MigrateModel.getAllMigratedTracks();
    res.json(tracks);
  } catch (err) {
    console.error("Error fetching migrated tracks:", err);
    res.status(500).json({ error: "Failed to fetch migrated tracks" });
  }
};

exports.getMigratedTrack = async (req, res) => {
  try {
    const { id } = req.params;
    const track = await MigrateModel.getMigratedTrack(id);
    if (!track) {
      return res.status(404).json({ error: "Track not found" });
    }
    res.json(track);
  } catch (err) {
    console.error("Error fetching migrated track:", err);
    res.status(500).json({ error: "Failed to fetch track" });
  }
};

exports.deleteMigratedTrack = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await MigrateModel.deleteMigratedTrack(id);
    if (result.deleted === 0) {
      return res.status(404).json({ error: "Track not found" });
    }
    res.json({ message: "Track deleted successfully", deletedId: id });
  } catch (err) {
    console.error("Error deleting migrated track:", err);
    res.status(500).json({ error: "Failed to delete track" });
  }
};

exports.restoreToTemp = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await MigrateModel.restoreToTemp(id);
    res.json({
      message: "Track restored to temporary table",
      restoredTrack: result,
    });
  } catch (err) {
    console.error("Error restoring track:", err);
    res.status(500).json({ error: "Failed to restore track" });
  }
};

exports.restoreAllToTemp = async (req, res) => {
  try {
    const { ids } = req.body || {};
    const result = await MigrateModel.restoreAllToTemp(ids);
    res.json({
      message: "Batch restore completed",
      details: result,
    });
  } catch (err) {
    console.error("Error restoring all tracks:", err);
    res.status(500).json({ error: "Failed to restore all tracks" });
  }
}