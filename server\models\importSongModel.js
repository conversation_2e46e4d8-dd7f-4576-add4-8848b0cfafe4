const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

/**
 * Create or update a song import entry.
 * Rules:
 * 1. Same song + artist, different URL → merge URLs into one JSON array in the same row.
 * 2. Same song + artist + URL → ignore (duplicate).
 * 3. Same song title, different artist → insert new row.
 * 4. Same artist, different song → insert new row.
 */

const ImportSongModel = {
  create: ({ song_name, artist_name, video_url = "", status = "pending" }) => {
    return new Promise((resolve, reject) => {
      // Step 1. Check if this song+artist already exists
      db.get(
        `SELECT id, video_url FROM ImportSongTableTemp WHERE song_name = ? AND artist_name = ?`,
        [song_name, artist_name],
        (err, row) => {
          if (err) return reject(err);

          if (!row) {
            // Step 2. No row → insert new as JSON array
            const videoUrlJson = JSON.stringify([video_url]);
            const insertQuery = `
              INSERT INTO ImportSongTableTemp (song_name, artist_name, video_url, status, created_at)
              VALUES (?, ?, ?, ?, datetime('now'))
            `;
            db.run(
              insertQuery,
              [song_name, artist_name, videoUrlJson, status],
              function (err) {
                if (err) return reject(err);
                resolve({
                  inserted: true,
                  updated: false,
                  duplicate: false,
                  id: this.lastID,
                });
              }
            );
          } else {
            // Step 3. Row exists → merge or detect duplicate
            let urls = [];
            try {
              urls = row.video_url ? JSON.parse(row.video_url) : [];
              if (!Array.isArray(urls)) urls = [row.video_url]; // handle legacy single URL
            } catch (e) {
              urls = [row.video_url]; // if not valid JSON, treat as single URL
            }

            if (urls.includes(video_url)) {
              // Step 4. Exact duplicate → log and skip
              console.log("DUPLICATE ENTRY (ignored):", {
                song_name,
                artist_name,
                video_url,
              });
              resolve({
                inserted: false,
                updated: false,
                duplicate: true,
                id: row.id,
              });
            } else {
              // Step 5. New URL for same song+artist → update row
              urls.push(video_url);
              const updatedJson = JSON.stringify(urls);
              const updateQuery = `
                UPDATE ImportSongTableTemp 
                SET video_url = ?, status = ? 
                WHERE id = ?
              `;
              db.run(
                updateQuery,
                [updatedJson, status, row.id],
                function (err) {
                  if (err) return reject(err);
                  resolve({
                    inserted: false,
                    updated: true,
                    duplicate: false,
                    id: row.id,
                  });
                }
              );
            }
          }
        }
      );
    });
  },

  getAll: (options = {}) => {
    return new Promise((resolve, reject) => {
      const {
        page = 1,
        limit = 10,
        search = "",
        status = "",
        sortBy = "created_at",
        sortOrder = "DESC",
        includeAll = false, // New option to allow override if needed
      } = options;

      const offset = (page - 1) * limit;

      // Build WHERE clause
      let whereClause = "";
      const params = [];

      if (search) {
        whereClause = "WHERE (song_name LIKE ? OR artist_name LIKE ?)";
        params.push(`%${search}%`, `%${search}%`);
      }

      if (status) {
        whereClause += whereClause ? " AND status = ?" : "WHERE status = ?";
        params.push(status);
      }

      // Exclude songs already in ImportSpotifyTrackTemp (unless includeAll is true)
      if (!includeAll) {
        const exclusion = `id NOT IN (SELECT import_song_id FROM ImportSpotifyTrackTemp WHERE is_migrated = 0 OR is_migrated IS NULL)`;
        whereClause += whereClause ? ` AND ${exclusion}` : `WHERE ${exclusion}`;
      }

      // Validate sortBy to prevent SQL injection
      const validSortColumns = [
        "id",
        "song_name",
        "artist_name",
        "status",
        "created_at",
      ];
      const validSortBy = validSortColumns.includes(sortBy)
        ? sortBy
        : "created_at";
      const validSortOrder = ["ASC", "DESC"].includes(sortOrder.toUpperCase())
        ? sortOrder.toUpperCase()
        : "DESC";

      // Get total count for pagination
      const countQuery = `SELECT COUNT(*) as total FROM ImportSongTableTemp ${whereClause}`;

      db.get(countQuery, params, (err, countResult) => {
        if (err) return reject(err);

        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);

        // Get paginated results
        const dataQuery = `
          SELECT * FROM ImportSongTableTemp 
          ${whereClause} 
          ORDER BY ${validSortBy} ${validSortOrder} 
          LIMIT ? OFFSET ?
        `;

        const dataParams = [...params, limit, offset];

        db.all(dataQuery, dataParams, (err, rows) => {
          if (err) return reject(err);

          resolve({
            data: rows,
            pagination: {
              currentPage: page,
              totalPages,
              totalItems: total,
              itemsPerPage: limit,
              hasNextPage: page < totalPages,
              hasPreviousPage: page > 1,
            },
          });
        });
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM ImportSongTableTemp WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, { song_name, artist_name, video_url, status }) => {
    return new Promise((resolve, reject) => {
      const updates = [];
      const params = [];

      if (song_name !== undefined) {
        updates.push("song_name = ?");
        params.push(song_name);
      }

      if (artist_name !== undefined) {
        updates.push("artist_name = ?");
        params.push(artist_name);
      }

      if (video_url !== undefined) {
        updates.push("video_url = ?");
        params.push(video_url);
      }

      if (status !== undefined) {
        updates.push("status = ?");
        params.push(status);
      }

      if (updates.length === 0) {
        return reject(new Error("No valid fields to update"));
      }

      params.push(id);
      const query = `UPDATE ImportSongTableTemp SET ${updates.join(
        ", "
      )} WHERE id = ?`;

      db.run(query, params, function (err) {
        if (err) return reject(err);
        resolve({ changes: this.changes });
      });
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportSongTableTemp WHERE id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ changes: this.changes });
        }
      );
    });
  },

  // Get songs with their associated Spotify data
  getSongWithSpotifyData: (id) => {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          s.*,
          GROUP_CONCAT(DISTINCT 
            json_object(
              'id', t.id,
              'spotify_track_id', t.spotify_track_id,
              'name', t.name,
              'artist', t.artist,
              'album', t.album,
              'release_year', t.release_year,
              'duration', t.duration,
              'decade', t.decade,
              'image', t.image
            )
          ) as tracks,
          GROUP_CONCAT(DISTINCT 
            json_object(
              'id', a.id,
              'artist_id', a.artist_id,
              'name', a.name,
              'image', a.image
            )
          ) as artists
        FROM ImportSongTableTemp s
        LEFT JOIN ImportSpotifyTrackTemp t ON s.id = t.import_song_id
        LEFT JOIN ImportSpotifyArtistTemp a ON s.id = a.import_song_id
        WHERE s.id = ?
        GROUP BY s.id
      `;

      db.get(query, [id], (err, row) => {
        if (err) return reject(err);

        if (row) {
          // Parse JSON strings back to arrays
          row.tracks = row.tracks
            ? row.tracks.split(",").map((track) => JSON.parse(track))
            : [];
          row.artists = row.artists
            ? row.artists.split(",").map((artist) => JSON.parse(artist))
            : [];
        }

        resolve(row);
      });
    });
  },
};

module.exports = ImportSongModel;
