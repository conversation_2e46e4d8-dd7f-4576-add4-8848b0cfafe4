import React from "react";
import { <PERSON>, Typography, <PERSON>con<PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import { useSnackbar } from "notistack";
import axios from "axios";

const ArtistResults = ({
  artists,
  selectedArtists,
  onSelectionChange,
  existingSpotifyArtists = [],
  setExistingSpotifyArtists,
}) => {
  const { enqueueSnackbar } = useSnackbar();

  const toggleArtistSelection = (artist) => {
    const isSelected = selectedArtists.some(
      (a) => a.artist_id === artist.artist_id
    );

    let updated;
    if (isSelected) {
      updated = selectedArtists.filter((a) => a.artist_id !== artist.artist_id);
    } else {
      updated = [...selectedArtists, artist];
    }

    onSelectionChange(updated);
  };

  const handleAddSelectedArtists = async () => {
    try {
      for (const artist of selectedArtists) {
        await axios.post(`${process.env.REACT_APP_API_URL}/api/songs/artists`, {
          id: artist.artist_id,
          name: artist.artistName,
          image: artist.image,
        });
      }
      enqueueSnackbar("✅ Artists added to main table!", {
        variant: "success",
      });
      setExistingSpotifyArtists((prev) => [
        ...prev,
        ...selectedArtists.map((a) => ({
          artist_id: a.artist_id,
          artistName: a.artistName,
          image: a.image,
        })),
      ]);
      onSelectionChange([]);
    } catch (err) {
      console.error("Artist save failed", err);
      enqueueSnackbar("❌ Artist save failed", {
        variant: "error",
      });
    }
  };

  return (
    <>
      <Typography variant="h6" sx={{ mt: 4 }} gutterBottom>
        🎤 Matching Artists
      </Typography>
      {artists.length ? (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {artists.map((artist, i) => {
            const isSelected = selectedArtists.some(
              (a) => a.artist_id === artist.artist_id
            );
            const isAlreadySaved = existingSpotifyArtists.some(
              (saved) => saved.artist_id === artist.artist_id
            );
            return (
              <Box
                key={i}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  p: 2,
                  borderRadius: 2,
                  boxShadow: 1,
                  border: isAlreadySaved
                    ? "2px solid #aaa"
                    : isSelected
                    ? "2px solid #388e3c"
                    : "1px solid #ccc",
                  bgcolor: isAlreadySaved ? "#333" : "#000",
                  opacity: isAlreadySaved ? 0.5 : 1,
                }}
              >
                <IconButton
                  disabled={isAlreadySaved}
                  onClick={() => toggleArtistSelection(artist)}
                  color={
                    isAlreadySaved
                      ? "inherit"
                      : isSelected
                      ? "success"
                      : "primary"
                  }
                >
                  {isAlreadySaved ? <CheckIcon /> : <AddIcon />}
                </IconButton>

                {artist.image && (
                  <Box
                    component="img"
                    src={artist.image}
                    alt={artist.artistName}
                    sx={{ width: 80, height: 80, borderRadius: "50%" }}
                  />
                )}
                <Typography variant="subtitle1" fontWeight="bold">
                  {artist.artistName}
                </Typography>
              </Box>
            );
          })}
          <Button
            variant="contained"
            color="success"
            sx={{ mt: 2 }}
            disabled={selectedArtists.length === 0}
            onClick={handleAddSelectedArtists}
          >
            Add Selected Artists
          </Button>
        </Box>
      ) : (
        <Typography color="text.secondary">No artist results found.</Typography>
      )}
    </>
  );
};

export default ArtistResults;
