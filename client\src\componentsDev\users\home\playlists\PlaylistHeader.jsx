// components/PlaylistHeader.jsx
import React from "react";
import { Box, Typography, Container, Skeleton } from "@mui/material";
import { getImageUrl } from "../../../../utils/imageHelper";

function PlaylistHeader({ details }) {
  if (!details) {
    return (
      <Box
        sx={{
          py: 5,
          bgcolor: "background.default",
          color: "white",
          position: "sticky",
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-end",
          alignItems: "center",
          width: "100%",
          minHeight: "400px",
          overflow: "hidden",
          marginTop: "2rem",
        }}
      >
        <Container maxWidth="lg" sx={{ position: "relative", zIndex: 2, pb: 4 }}>
          <Skeleton
            variant="text"
            width="60%"
            height={60}
            sx={{ mx: 'auto', bgcolor: 'grey.800' }}
          />
        </Container>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        py: 5,
        bgcolor: "background.default",
        color: "white",
        textShadow: "1px 1px 2px black",
        position: "sticky",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-end",
        alignItems: "center",
        width: "100%",
        minHeight: "400px",
        overflow: "hidden",
        marginTop: "2rem",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: 100,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${getImageUrl(details.image)})`,
          backgroundSize: "contain",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center top",
          backgroundColor: "background.default",
          zIndex: 1,
          opacity: 0.7,
        }}
      />

      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 2, pb: 4 }}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            textAlign: "center",
            fontWeight: "bold",
            color: "text.primary",
          }}
        >
          {details.title}
        </Typography>

        {details.description && (
          <Typography
            variant="body1"
            sx={{
              textAlign: "center",
              color: "text.secondary",
              mt: 2,
              maxWidth: "600px",
              mx: "auto"
            }}
          >
            {details.description}
          </Typography>
        )}
      </Container>
    </Box>
  );
}

export default PlaylistHeader;