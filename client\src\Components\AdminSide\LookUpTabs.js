import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON>,
  TextField,
  Button,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Container,
  Switch,
  FormControlLabel,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import CommonModal from "./CommonModalComponent";
import Header from "./Header";
import ShowdownTab from "./ShowdownTab";
import axios from "axios";

const STORAGE_KEYS = ["playlist", "url", "file", "genre", "settings", "showdown"];
const TITLES = ["Playlist", "URL", "Files", "Genre", "Settings", "Showdown"];

const FIELD_MAP = {
  playlist: {
    id: "playlist_type_lookup_id",
    name: "playlist_type_name",
    description: "playlist_type_description",
    status: "status",
    createdAt: "created_at",
  },
  url: {
    id: "url_type_lookup_id",
    name: "url_type_name",
    description: "url_type_description",
    status: "status",
    createdAt: "created_at",
  },
  file: {
    id: "file_type_lookup_id",
    name: "file_type_name",
    description: "file_type_description",
    status: "status",
    createdAt: "created_at",
  },
  genre: {
    id: "genre_type_lookup_id",
    name: "genre_type_name",
    description: "genre_type_description",
    status: "status",
    createdAt: "created_at",
  },
};

const fetchItems = async (type, currentShowAllOptions = true) => {
  if (type === 'settings') {
    return [
      {
        id: 'request_options_mode',
        name: 'Request Options Mode',
        description: 'Controls which request options are shown to users',
        value: currentShowAllOptions,
        type: 'toggle'
      }
    ];
  }
  if (type === 'showdown') return []; // Showdown doesn't need fetched items

  const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`);
  return response.data;
};

const createItem = async (type, item) => {
  await axios.post(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`, item);
};

const updateItem = async (type, id, item) => {
  await axios.put(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`, item);
};

const deleteItem = async (type, id) => {
  await axios.delete(`${process.env.REACT_APP_API_URL}/api/lookups/${type}/${id}`);
};

const LookupForm = ({
  items,
  handleClose,
  editIndex,
  handleSave,
  typeIndex,
}) => {
  const type = STORAGE_KEYS[typeIndex];
  const fields = FIELD_MAP[type];
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [status, setStatus] = useState(true);

  useEffect(() => {
    if (editIndex !== null && items[editIndex]) {
      setName(items[editIndex][fields.name] || "");
      setDescription(items[editIndex][fields.description] || "");
      setStatus(items[editIndex][fields.status] === 1);
    } else {
      setName("");
      setDescription("");
      setStatus(true);
    }
  }, [editIndex, items, fields]);

  const handleSaveClick = () => {
    if (!name.trim()) return;
    const now = new Date().toISOString();
    const newItem = {
      [fields.name]: name,
      [fields.description]: description,
      [fields.status]: status ? 1 : 0,
      [fields.createdAt]: now,
    };
    handleSave(newItem);
    handleClose();
  };

  return (
    <Box>
      <TextField
        label="Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
        fullWidth
        margin="dense"
      />
      <TextField
        label="Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        fullWidth
        margin="dense"
      />
      <FormControlLabel
        control={
          <Switch
            checked={status}
            onChange={(e) => setStatus(e.target.checked)}
          />
        }
        label="Active"
      />
      <Button onClick={handleSaveClick} variant="contained" sx={{ mt: 1 }}>
        {editIndex !== null ? "Update" : "Add"}
      </Button>
    </Box>
  );
};

const LookupTabs = () => {
  const [tab, setTab] = useState(0);
  const [data, setData] = useState([[], [], [], [], [], []]); // Added empty array for showdown
  const [openModal, setOpenModal] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [showAllOptions, setShowAllOptions] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      await loadSettings();
    };
    loadData();
  }, []);

  useEffect(() => {
    const loadData = async () => {
      const loadedData = await Promise.all(
        STORAGE_KEYS.map((type) => fetchItems(type, showAllOptions))
      );
      setData(loadedData);
    };
    loadData();
  }, [showAllOptions]);

  const loadSettings = async () => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/settings/public/request-options-mode`
      );
      setShowAllOptions(response.data.showAllOptions);
    } catch (error) {
      console.error("Failed to load settings:", error);
    }
  };

  const handleSettingsToggle = async (newValue) => {
    try {
      await axios.put(
        `${process.env.REACT_APP_API_URL}/api/settings/request-options-mode`,
        { showAllOptions: newValue },
        { withCredentials: true }
      );
      setShowAllOptions(newValue);

      const updatedSettings = [
        {
          id: 'request_options_mode',
          name: 'Request Options Mode',
          description: 'Controls which request options are shown to users',
          value: newValue,
          type: 'toggle'
        }
      ];

      setData((prevData) => {
        const updatedData = [...prevData];
        updatedData[4] = updatedSettings;
        return updatedData;
      });
    } catch (error) {
      console.error("Failed to update setting:", error);
    }
  };

  const handleDelete = async (typeIndex, itemIndex) => {
    const type = STORAGE_KEYS[typeIndex];
    const fields = FIELD_MAP[type];
    const itemId = data[typeIndex][itemIndex][fields.id];
    await deleteItem(type, itemId);
    const updated = data[typeIndex].filter((_, i) => i !== itemIndex);
    setItems(typeIndex, updated);
  };

  const handleSave = async (newItem) => {
    const type = STORAGE_KEYS[tab];
    const fields = FIELD_MAP[type];

    const payload = {
      name: newItem[fields.name],
      description: newItem[fields.description],
      status: newItem[fields.status],
      createdAt: newItem[fields.createdAt],
    };

    if (editIndex !== null) {
      const itemId = data[tab][editIndex][fields.id];
      await updateItem(type, itemId, payload);
    } else {
      await createItem(type, payload);
    }

    const loadedData = await fetchItems(type, showAllOptions);
    setData((prevData) => {
      const updatedData = [...prevData];
      updatedData[tab] = loadedData;
      return updatedData;
    });
    handleClose();
  };

  const handleClose = () => {
    setOpenModal(false);
    setEditIndex(null);
  };

  const setItems = (index, newItems) => {
    const updatedData = [...data];
    updatedData[index] = newItems;
    setData(updatedData);
  };

  const handleStatusToggle = async (
    e,
    index,
    tab,
    data,
    setItems,
    updateItem,
    type
  ) => {
    const fields = FIELD_MAP[type];
    const updated = [...data[tab]];

    updated[index] = {
      ...updated[index],
      [fields.status]: e.target.checked ? 1 : 0,
    };

    const item = updated[index];

    const payload = {
      name: item[fields.name],
      description: item[fields.description],
      status: item[fields.status],
      createdAt: item[fields.createdAt],
    };

    try {
      await updateItem(type, item[fields.id], payload);
      setItems(tab, updated);
    } catch (error) {
      console.error("Failed to update item status:", error);
    }
  };

  return (
    <>
      <Header />
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box>
          <Tabs value={tab} onChange={(e, newValue) => setTab(newValue)}>
            {TITLES.map((label, index) => (
              <Tab 
                key={label} 
                label={`${label} ${index === 4 || index === 5 ? '' : 'Type'}`} 
              />
            ))}
          </Tabs>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 2,
              pb: 2,
              borderBottom: "1px solid",
            }}
          >
            <Typography variant="h4">
              {TITLES[tab]} {tab === 4 || tab === 5 ? '' : 'Type'}
            </Typography>
            {tab !== 4 && tab !== 5 && (
              <Button
                variant="contained"
                color="success"
                onClick={() => setOpenModal(true)}
              >
                Create New {TITLES[tab]} Type
              </Button>
            )}
          </Box>
          
          {tab === 5 ? (
            <ShowdownTab />
          ) : (
            <List>
              {data[tab].map((item, index) => {
                if (tab === 4) {
                  return (
                    <ListItem key={item.id || index}>
                      <ListItemText
                        primary={item.name}
                        secondary={item.description}
                      />
                      <ListItemSecondaryAction>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={item.value}
                              onChange={(e) =>
                                handleSettingsToggle(e.target.checked)
                              }
                            />
                          }
                          label={item.value ? 'Show All Options' : 'Show Only "Sing for Me"'}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                }

                const type = STORAGE_KEYS[tab];
                const fields = FIELD_MAP[type];
                return (
                  <ListItem key={item[fields.id] || index}>
                    <ListItemText
                      primary={`${item[fields.name] || "Unnamed"} (${item[fields.status] === 1 ? "Active" : "Inactive"
                        })`}
                      secondary={
                        <>
                          {item[fields.description] || "No description available"}{" "}
                          <br />
                          <strong>Created:</strong>{" "}
                          {item[fields.createdAt]
                            ? new Date(item[fields.createdAt]).toLocaleString()
                            : "Not specified"}
                        </>
                      }
                    />
                    <ListItemSecondaryAction sx={{ display: "flex", gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={item[fields.status] === 1}
                            onChange={(e) =>
                              handleStatusToggle(
                                e,
                                index,
                                tab,
                                data,
                                setItems,
                                updateItem,
                                STORAGE_KEYS[tab]
                              )
                            }
                          />
                        }
                        label=""
                      />
                      <IconButton
                        edge="end"
                        onClick={() => {
                          setEditIndex(index);
                          setOpenModal(true);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleDelete(tab, index)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                );
              })}
            </List>
          )}

          {tab !== 4 && tab !== 5 && (
            <CommonModal
              open={openModal}
              onClose={handleClose}
              title={
                editIndex !== null
                  ? `Edit ${TITLES[tab]} Type`
                  : `Create ${TITLES[tab]} Type`
              }
            >
              <LookupForm
                typeIndex={tab}
                items={data[tab]}
                setItems={setItems}
                handleClose={handleClose}
                editIndex={editIndex}
                setEditIndex={setEditIndex}
                handleSave={handleSave}
              />
            </CommonModal>
          )}
        </Box>
      </Container>
    </>
  );
};

export default LookupTabs;