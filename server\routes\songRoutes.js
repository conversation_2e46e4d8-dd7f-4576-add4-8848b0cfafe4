const express = require("express");
const router = express.Router();
const songController = require("../controllers/songController");

// getting all artists
router.get("/artists", songController.getAllArtists);

// Save Artist
router.post("/artists", songController.saveArtist);

// Get Song by Artist ID
router.get('/artist/:artistID', songController.getByArtistId);

router.get('/artist-detail/:artistID', songController.getArtistById);

// Popular Songs
router.get("/popular", songController.getPopularSongs);

router.post("/", songController.create);
router.get("/", songController.getAll);
router.get("/:id", songController.getById);
router.put("/:id", songController.update);
router.delete("/:id", songController.remove);

// Search API
router.get("/search", songController.searchSongs);



module.exports = router;
