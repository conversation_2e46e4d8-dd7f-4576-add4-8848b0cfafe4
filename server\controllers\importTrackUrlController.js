const ImportTrackUrlModel = require("../models/importTrackUrlModel");

exports.addTrackUrl = async (req, res) => {
  const { track_id, song_url, url_type_lookup_id } = req.body;

  if (!track_id || !song_url || !url_type_lookup_id) {
    return res
      .status(400)
      .json({
        error: "track_id, song_url, and url_type_lookup_id are required",
      });
  }

  try {
    const result = await ImportTrackUrlModel.addUrl(
      track_id,
      song_url,
      url_type_lookup_id || "YouTube"
    );
    res.status(200).json({ message: "✅ Track URL saved", id: result.id });
  } catch (err) {
    if (err.code === "SQLITE_CONSTRAINT") {
      res.status(409).json({
        error: "Duplicate: This URL already exists for this track and type",
      });
    } else {
      console.error("❌ Add Track URL error:", err);
      res.status(500).json({ error: "Failed to save track URL" });
    }
  }
};

exports.getTrackUrls = async (req, res) => {
  const { id: track_id } = req.params;

  if (!track_id) {
    return res.status(400).json({ error: "track_id is required" });
  }

  try {
    const urls = await ImportTrackUrlModel.getUrlsByTrackId(track_id);
    res.status(200).json(urls);
  } catch (err) {
    console.error("❌ Get Track URLs error:", err);
    res.status(500).json({ error: "Failed to fetch URLs" });
  }
};

exports.deleteTrackUrl = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: "URL id is required" });
  }

  try {
    const result = await ImportTrackUrlModel.deleteUrlById(id);
    if (result.changes === 0) {
      return res.status(404).json({ error: "URL not found" });
    }

    res
      .status(200)
      .json({ message: "✅ Track URL deleted", deleted: result.changes });
  } catch (err) {
    console.error("❌ Delete URL error:", err);
    res.status(500).json({ error: "Failed to delete URL" });
  }
};
