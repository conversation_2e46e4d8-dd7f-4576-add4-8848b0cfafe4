const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SettingsModel = {
  // Create or update a setting
  upsert: (setting_key, setting_value, setting_description = null) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO app_settings (setting_key, setting_value, setting_description, created_at, updated_at)
        VALUES (?, ?, ?, datetime('now'), datetime('now'))
        ON CONFLICT(setting_key) DO UPDATE SET
          setting_value = excluded.setting_value,
          setting_description = excluded.setting_description,
          updated_at = datetime('now')
      `;
      db.run(query, [setting_key, setting_value, setting_description], function (err) {
        if (err) return reject(err);
        resolve({ 
          setting_key, 
          setting_value, 
          setting_description,
          changes: this.changes,
          lastID: this.lastID 
        });
      });
    });
  },

  // Get a specific setting by key
  getByKey: (setting_key) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM app_settings WHERE setting_key = ?`,
        [setting_key],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  // Get all settings
  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM app_settings ORDER BY setting_key`,
        [],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },

  // Delete a setting
  delete: (setting_key) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM app_settings WHERE setting_key = ?`,
        [setting_key],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  // Initialize default settings
  initializeDefaults: () => {
    return new Promise(async (resolve, reject) => {
      try {
        // Check if settings already exist
        const existingSettings = await SettingsModel.getAll();
        
        if (existingSettings.length === 0) {
          // Create default settings
          await SettingsModel.upsert(
            'request_options_mode',
            'true',
            'Controls which request options are shown to users'
          );
          
          console.log('Default app settings initialized');
        }
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }
};

module.exports = SettingsModel;
