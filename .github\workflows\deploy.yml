name: Deploy to Azure

on:
  push:
    branches:
      - main

jobs:
  backend-deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout code
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: Set up SSH agent
      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      # Step 3: Deploy backend (Node.js server)
      - name: Deploy backend
        run: |
          ssh -o StrictHostKeyChecking=no -T jukeboxadmin@*************** << 'EOF'
          cd ~/projects/imjukebox/server
          git pull origin main
          npm install
          pm2 restart "server-app" || pm2 start npm --name "server-app" -- start
          EOF

  frontend-deploy:
    runs-on: ubuntu-latest
    needs: backend-deploy # Optional: Run frontend deployment only after backend is complete

    steps:
      # Step 1: Checkout code
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 2: Set up SSH agent
      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      # Step 3: Deploy frontend (React client)
      - name: Deploy frontend
        run: |
          ssh -o StrictHostKeyChecking=no -T jukeboxadmin@*************** << 'EOF'
          cd ~/projects/imjukebox/client
          git pull origin main
          npm install
          npm run build
          pm2 restart "react-app" || pm2 start npm --name "react-app" -- start
          EOF
