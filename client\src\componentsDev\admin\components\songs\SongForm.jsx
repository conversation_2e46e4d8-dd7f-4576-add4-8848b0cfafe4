import React from "react";
import {
  TextField,
  <PERSON><PERSON>,
  <PERSON>, 
} from "@mui/material";
import GenreSection from "./GenreSection";
import PlaylistSection from "./PlaylistSection";
import UrlSection from "./UrlSection";
import FileSection from "./FileSection";
import YouTubeSection from "./YouTubeSection";
import ImageUpload from "./ImageUpload";

const SongForm = ({
  songData,
  setSongData,
  selectedPlaylists,
  setSelectedPlaylists,
  selectedGenres,
  setSelectedGenres,
  songUrls,
  setSongUrls,
  songFiles,
  setSongFiles,
  tempUrls,
  setTempUrls,
  tempFiles,
  setTempFiles,
  isDuplicate,
  editingIndex,
  selectedSongId,
  handleChange,
  handleSave,
}) => {
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <TextField
        label="Song Name"
        name="name"
        value={songData.name}
        onChange={handleChange}
        fullWidth
        error={isDuplicate}
        helperText={isDuplicate ? "This song already exists." : ""}
      />
      <TextField
        label="Album"
        name="album"
        value={songData.album}
        onChange={handleChange}
        fullWidth
      />
      <TextField
        label="Artist"
        name="artist"
        value={songData.artist}
        onChange={handleChange}
        fullWidth
      />
      <TextField
        label="release_year"
        name="release_year"
        value={songData.release_year}
        onChange={handleChange}
      />
      <TextField
        label="Decade"
        name="decade"
        value={songData.decade}
        onChange={handleChange}
      />
      <TextField
        label="Duration"
        name="duration"
        value={songData.duration}
        onChange={handleChange}
        fullWidth
        placeholder="mm:ss"
      />

      <GenreSection
        selectedGenres={selectedGenres}
        setSelectedGenres={setSelectedGenres}
      />

      <ImageUpload
        songData={songData}
        setSongData={setSongData}
      />

      <PlaylistSection
        selectedPlaylists={selectedPlaylists}
        setSelectedPlaylists={setSelectedPlaylists}
        selectedSongId={selectedSongId}
      />

      <UrlSection
        songUrls={songUrls}
        setSongUrls={setSongUrls}
        tempUrls={tempUrls}
        setTempUrls={setTempUrls}
        editingIndex={editingIndex}
        selectedSongId={selectedSongId}
      />

      <YouTubeSection
        songData={songData}
        selectedSongId={selectedSongId}
        songUrls={songUrls}
        setSongUrls={setSongUrls}
      />

      <FileSection
        songFiles={songFiles}
        setSongFiles={setSongFiles}
        tempFiles={tempFiles}
        setTempFiles={setTempFiles}
        editingIndex={editingIndex}
        selectedSongId={selectedSongId}
      />

      <Button variant="contained" color="success" onClick={handleSave}>
        {editingIndex !== null ? "Update Song" : "Add Song"}
      </Button>
    </Box>
  );
};

export default SongForm;