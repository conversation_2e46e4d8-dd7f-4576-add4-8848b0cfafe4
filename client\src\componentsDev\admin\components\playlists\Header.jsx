import React from 'react';
import { AppBar, Toolbar, Typography, Box, Button } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import MusicNoteIcon from '@mui/icons-material/MusicNote';

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    { label: 'Dashboard', path: '/dashboard' },
    { label: 'All Playlists', path: '/playlists' },
    { label: 'All Songs', path: '/songs' },
    { label: 'Settings', path: '/settings' },
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <AppBar 
      position="static" 
      sx={{ 
        bgcolor: '#1e1e1e', 
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)' 
      }}
    >
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <MusicNoteIcon sx={{ mr: 2, color: '#1db954' }} />
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              fontWeight: 'bold',
              background: 'linear-gradient(45deg, #1db954, #1ed760)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              cursor: 'pointer',
            }}
            onClick={() => navigate('/')}
          >
            SongQ
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {navigationItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              onClick={() => navigate(item.path)}
              sx={{
                color: isActive(item.path) ? '#1db954' : 'rgba(255, 255, 255, 0.8)',
                fontWeight: isActive(item.path) ? 'bold' : 'normal',
                borderBottom: isActive(item.path) ? '2px solid #1db954' : 'none',
                borderRadius: 0,
                '&:hover': {
                  backgroundColor: 'rgba(29, 185, 84, 0.1)',
                  color: '#1db954',
                },
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;