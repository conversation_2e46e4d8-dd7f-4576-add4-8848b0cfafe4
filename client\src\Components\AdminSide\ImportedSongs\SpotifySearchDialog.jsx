import React, { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import TrackResults from "./TrackResults";
import ArtistResults from "./ArtistResults";
import { useSnackbar } from "notistack";
import axios from "axios";

const SpotifySearchDialog = ({
  open,
  onClose,
  searchResult,
  onSave,
  spotifySearchSongId,
}) => {
  const [selectedTracks, setSelectedTracks] = useState([]);
  const [selectedArtists, setSelectedArtists] = useState([]);
  const [existingSpotifyTracks, setExistingSpotifyTracks] = useState([]);
  const [existingSpotifyArtists, setExistingSpotifyArtists] = useState([]);

  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    const fetchExistingTracks = async () => {
      try {
        const res = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/import/spotify/tracks/all`
        );
        setExistingSpotifyTracks(res.data || []);
      } catch (err) {
        console.error("Error fetching existing Spotify tracks", err);
      }
    };

    const fetchExistingArtists = async () => {
      try {
        const res = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/songs/artists`
        );
        setExistingSpotifyArtists(res.data || []);
      } catch (err) {
        console.error("Error fetching existing Spotify artists", err);
      }
    };

    if (open) {
      fetchExistingArtists();
      fetchExistingTracks(); // You already had this
    }
  }, [open]);

  const handleMarkAsDone = async () => {
    try {
      await axios.put(
        `${process.env.REACT_APP_API_URL}/api/import/songs/${spotifySearchSongId}`,
        {
          status: "completed",
        }
      );
      enqueueSnackbar("✅ Spotify track saved!", { variant: "success" });
      onClose();
      onSave();
    } catch (err) {
      console.error("Save failed", err);
      enqueueSnackbar("❌ Save failed", { variant: "error" });
    }
  };

  const handleClose = () => {
    setSelectedTracks([]);
    setSelectedArtists([]);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle
        sx={{ fontWeight: "bold", bgcolor: "#1976d2", color: "white" }}
      >
        🎧 Spotify Search Results
      </DialogTitle>

      <DialogContent dividers sx={{ py: 2 }}>
        <TrackResults
          tracks={searchResult?.trackResults || []}
          selectedTracks={selectedTracks}
          onSelectionChange={setSelectedTracks}
          originalSong={searchResult?.originalSong}
          onSave={onSave}
          existingSpotifyTracks={existingSpotifyTracks}
          setExistingSpotifyTracks={setExistingSpotifyTracks}
        />

        <ArtistResults
          artists={searchResult?.artistResults || []}
          selectedArtists={selectedArtists}
          onSelectionChange={setSelectedArtists}
          originalSong={searchResult?.originalSong}
          existingSpotifyArtists={existingSpotifyArtists}
          setExistingSpotifyArtists={setExistingSpotifyArtists}
        />
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={handleClose} variant="outlined">
          Close
        </Button>
        <Button variant="contained" color="success" onClick={handleMarkAsDone}>
          MARK AS DONE
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SpotifySearchDialog;
