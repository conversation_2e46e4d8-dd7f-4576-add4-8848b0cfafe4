import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Container,
  Box,
  Paper,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Snackbar,
  Alert,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Header from "./Header";
import { styled } from "@mui/system";

const StyledPaper = styled(Paper)({
  borderRadius: "10px",
  overflow: "hidden",
  width: "100%",
  height: 250,
  position: "relative",
  transition: "transform 0.15s ease-in-out",
  "&:hover": {
    transform: "scale(1.05)",
  },
  "& img": {
    width: "100%",
    height: "100%",
    objectFit: "cover",
  },
});

const PlaylistTitle = styled(Typography)({
  fontWeight: 700,
  fontSize: "1rem",
  color: "#fff",
  textAlign: "center",
  marginTop: "8px",
});

const AdminPlaylist = () => {
  const [playlists, setPlaylists] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [playlistUrl, setPlaylistUrl] = useState("");
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [newTitle, setNewTitle] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const navigate = useNavigate();

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const fetchPlaylists = async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlist`
      );
      const data = await response.json();
      setPlaylists(data);
    } catch (error) {
      console.error("Failed to fetch playlists:", error);
    }
  };

  useEffect(() => {
    fetchPlaylists();
  }, []);

  const handlePlaylistClick = (playlist) => {
    navigate(`/playlists/${playlist.playlist_master_id}`);
  };

  const handleMenuOpen = (event, playlist) => {
    setAnchorEl(event.currentTarget);
    setSelectedPlaylist(playlist);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = () => {
    setNewTitle(selectedPlaylist.title);
    setOpenEditDialog(true);
    handleMenuClose();
  };

  const handleEditConfirm = async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlist/${selectedPlaylist.playlist_master_id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ title: newTitle }),
        }
      );

      if (response.ok) {
        const updatedPlaylist = await response.json();
        setPlaylists((prevPlaylists) =>
          prevPlaylists.map((p) =>
            p.playlist_master_id === selectedPlaylist.playlist_master_id
              ? { ...p, title: updatedPlaylist.title }
              : p
          )
        );

        setSnackbarMessage("Title updated successfully!");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);

        setOpenEditDialog(false);
        setSelectedPlaylist(null);
      } else {
        setSnackbarMessage("Failed to update playlist.");
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    } catch (error) {
      setSnackbarMessage("Error updating playlist.");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  const handleDeleteClick = () => {
    setOpenDeleteDialog(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlist/${selectedPlaylist.playlist_master_id}`,
        {
          method: "DELETE",
        }
      );
      if (response.ok) {
        setPlaylists(
          playlists.filter(
            (p) => p.playlist_master_id !== selectedPlaylist.playlist_master_id
          )
        );
        setOpenDeleteDialog(false);
        setSelectedPlaylist(null);
      } else {
        console.error("Failed to delete playlist:", response.statusText);
      }
    } catch (error) {
      console.error("Failed to delete playlist:", error);
    }
  };

  const handleDeleteCancel = () => {
    setOpenDeleteDialog(false);
    setSelectedPlaylist(null);
  };

  const handleAddPlaylist = async () => {
    if (!playlistUrl) return;

    const playlistIdMatch = playlistUrl.match(/playlist\/([a-zA-Z0-9]+)(\?|$)/);
    if (!playlistIdMatch) {
      console.error("Invalid playlist URL");
      return;
    }

    const playlistId = playlistIdMatch[1];

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${playlistId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch playlist details");
      }
      const newPlaylistDetails = await response.json();

      // Check for duplicates in the songs of the new playlist
      const duplicateSongs = newPlaylistDetails.duplicates || [];

      if (duplicateSongs.length > 0) {
        const duplicateSongNames = duplicateSongs
          .map((song) => `${song.name} by ${song.artist}`)
          .join(", ");
        setSnackbarMessage(
          `The following songs already exist in your database and will not be added: ${duplicateSongNames}.`
        );
        setSnackbarSeverity("info");
        setSnackbarOpen(true);
      }
      setPlaylists((prevPlaylists) => [...prevPlaylists, newPlaylistDetails]);
      setPlaylistUrl("");
    } catch (error) {
      console.error("Error fetching playlist:", error);
    }
  };

  const handleLogin = () => {
    window.location.href = `${process.env.REACT_APP_API_URL}/login`; // Ensure this points to the correct backend server
  }

  return (
    <Container>
      <Header />
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2vh' }}>
        <Button variant="contained" color="primary" onClick={handleLogin}>
          Login with Spotify
        </Button>
      </div>
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Playlists
        </Typography>
        <Box display="flex" alignItems="center" gap={2} mb={3}>
          <TextField
            label="Playlist URL"
            value={playlistUrl}
            onChange={(e) => setPlaylistUrl(e.target.value)}
            variant="outlined"
            size="small"
            sx={{ flexGrow: 1, minWidth: 300 }}
          />
          <Button
            onClick={handleAddPlaylist}
            variant="contained"
            sx={{
              backgroundColor: "#00bfa5",
              "&:hover": { backgroundColor: "#008c74" },
            }}
          >
            Add Playlist
          </Button>
        </Box>
        <Box display="flex" flexWrap="wrap" gap={2}>
          {playlists.map((playlist) => (
            <Box
              key={playlist.id}
              sx={{ width: 250, mb: 4, position: "relative" }}
            >
              <StyledPaper elevation={3}>
                <img
                  src={playlist.image}
                  alt={playlist.title}
                  onClick={() => handlePlaylistClick(playlist)}
                />
                <IconButton
                  aria-label="settings"
                  sx={{
                    position: "absolute",
                    top: 10,
                    right: 10,
                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.8)",
                    },
                  }}
                  onClick={(event) => handleMenuOpen(event, playlist)}
                >
                  <MoreVertIcon />
                </IconButton>
              </StyledPaper>
              <PlaylistTitle variant="h6">{playlist.title}</PlaylistTitle>
            </Box>
          ))}
        </Box>
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEditClick}>Edit</MenuItem>
        <MenuItem onClick={handleDeleteClick}>Delete</MenuItem>
      </Menu>

      <Dialog open={openDeleteDialog} onClose={handleDeleteCancel}>
        <DialogTitle>Delete Playlist</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the playlist "
            {selectedPlaylist?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="secondary">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)}>
        <DialogTitle>Edit Playlist</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Playlist Title"
            type="text"
            fullWidth
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleEditConfirm} color="secondary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default AdminPlaylist;
