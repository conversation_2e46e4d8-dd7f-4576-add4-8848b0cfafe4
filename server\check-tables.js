const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database/app.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking database tables...\n');

// List all tables
db.all("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", (err, tables) => {
    if (err) {
        console.error('Error listing tables:', err);
        return;
    }
    
    console.log('📋 All tables in database:');
    tables.forEach(table => {
        console.log(`   - ${table.name}`);
    });
    
    // Check specific lookup tables
    const lookupTables = ['difficulty_level_lookup', 'question_type_lookup', 'answer_type_lookup'];
    
    console.log('\n🔍 Checking lookup tables:');
    
    let checkCount = 0;
    lookupTables.forEach(tableName => {
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name=?", [tableName], (err, result) => {
            if (err) {
                console.error(`Error checking ${tableName}:`, err);
            } else {
                console.log(`   ${tableName}: ${result ? '✅ EXISTS' : '❌ MISSING'}`);
            }
            
            checkCount++;
            if (checkCount === lookupTables.length) {
                // Check if old difficulty_master table exists
                db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='difficulty_master'", (err, result) => {
                    if (result) {
                        console.log('\n⚠️  OLD TABLE FOUND:');
                        console.log('   difficulty_master: ✅ EXISTS (should be migrated)');
                    }
                    
                    // Show sample data from difficulty_level_lookup if it exists
                    db.all("SELECT * FROM difficulty_level_lookup LIMIT 5", (err, rows) => {
                        if (!err && rows.length > 0) {
                            console.log('\n📊 Sample data from difficulty_level_lookup:');
                            rows.forEach(row => {
                                console.log(`   ID: ${row.difficulty_id}, Name: ${row.level_name}, Status: ${row.status}`);
                            });
                        } else if (err) {
                            console.log('\n❌ Error reading difficulty_level_lookup:', err.message);
                        } else {
                            console.log('\n📊 difficulty_level_lookup table is empty');
                        }
                        
                        // Check answer_type_lookup data
                        db.all("SELECT * FROM answer_type_lookup LIMIT 5", (err, rows) => {
                            if (!err && rows.length > 0) {
                                console.log('\n📊 Sample data from answer_type_lookup:');
                                rows.forEach(row => {
                                    console.log(`   ID: ${row.answer_type_id}, Type: ${row.type_name}, Display: ${row.display_name}, Status: ${row.status}`);
                                });
                            } else if (err) {
                                console.log('\n❌ Error reading answer_type_lookup:', err.message);
                            } else {
                                console.log('\n📊 answer_type_lookup table is empty');
                            }
                            
                            db.close();
                        });
                    });
                });
            }
        });
    });
});
