// useMode.js
import { createContext, useState, useEffect, useContext } from "react";
import { useLocation } from "react-router-dom";

// Create context
const RequestModeContext = createContext();

// Provider component
export function RequestModeProvider({ children }) {
  const location = useLocation();
  const [requestMode, setRequestMode] = useState(null);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const modeFromUrl = searchParams.get("jukebox")
      ? "jukebox"
      : searchParams.get("openmic")
      ? "openmic"
      : null;

    if (modeFromUrl) {
      setRequestMode(modeFromUrl);
      sessionStorage.setItem("requestMode", modeFromUrl);
    } else {
      const stored = sessionStorage.getItem("requestMode");
      if (stored) setRequestMode(stored);
    }
  }, [location.search]);

  return (
    <RequestModeContext.Provider value={requestMode}>
      {children}
    </RequestModeContext.Provider>
  );
}

// Hook to use mode
export function useMode() {
  const mode = useContext(RequestModeContext);
  return mode || "default";
}
