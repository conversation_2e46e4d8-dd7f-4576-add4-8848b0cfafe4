import { Box, Typography, Container } from "@mui/material";
import { getImageUrl } from "../../utils/imageHelper";

function PlaylistHeader({ details }) {
  return (
    <Box
      sx={{
        py: 5,
        bgcolor: "background.paper",
        color: "white",
        textShadow: "1px 1px 2px black",
        position: "sticky",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-end",
        alignItems: "center",
        width: "100%",
        minHeight: "400px",
        overflow: "hidden",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: 100,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${getImageUrl(details.image)})`,
          backgroundSize: "contain",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center top",
          backgroundColor: "background.default",
          zIndex: 1,
        }}
      />
      <Container maxWidth="lg" sx={{ position: "relative", zIndex: 2, pb: 4 }}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            textAlign: "center",
            fontWeight: "bold",
            color: "text.primary",
          }}
        >
          {details.title}
        </Typography>
      </Container>
    </Box>
  );
}

export default PlaylistHeader;
