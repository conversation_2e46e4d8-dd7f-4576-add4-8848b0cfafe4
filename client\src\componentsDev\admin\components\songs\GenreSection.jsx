import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  IconButton,
  Modal,
  Button,
  Chip,
  Autocomplete,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import { songService } from "./songService";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  maxHeight: "90vh",
  overflowY: "auto",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
};

const GenreSection = ({ selectedGenres, setSelectedGenres }) => {
  const [genres, setGenres] = useState([]);
  const [openGenreModal, setOpenGenreModal] = useState(false);
  const [tempSelectedGenres, setTempSelectedGenres] = useState([]);

  useEffect(() => {
    const fetchGenres = async () => {
      try {
        const genresData = await songService.fetchGenres();
        setGenres(genresData);
      } catch (error) {
        console.error("Failed to fetch genres:", error);
      }
    };
    fetchGenres();
  }, []);

  const handleOpenGenreModal = () => {
    setTempSelectedGenres(selectedGenres);
    setOpenGenreModal(true);
  };

  const handleCloseGenreModal = () => {
    setOpenGenreModal(false);
  };

  const saveSelectedGenres = async () => {
    setSelectedGenres(tempSelectedGenres);
    setOpenGenreModal(false);
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="subtitle1" gutterBottom>
        Genres
      </Typography>
      <TextField
        fullWidth
        variant="outlined"
        placeholder={
          selectedGenres.length === 0 ? "Select genres" : ""
        }
        InputProps={{
          startAdornment: selectedGenres.length > 0 && (
            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
                gap: 0.5,
                maxWidth: "calc(100% - 48px)",
              }}
            >
              {selectedGenres.map((genre) => (
                <Chip
                  key={genre.genre_type_lookup_id}
                  label={genre.genre_type_name}
                  size="small"
                  onDelete={() => {
                    setSelectedGenres((prev) =>
                      prev.filter(
                        (g) =>
                          g.genre_type_lookup_id !==
                          genre.genre_type_lookup_id
                      )
                    );
                  }}
                />
              ))}
            </Box>
          ),
          endAdornment: (
            <IconButton
              onClick={handleOpenGenreModal}
              edge="end"
              sx={{
                position: "absolute",
                right: "20px",
              }}
            >
              <AddIcon />
            </IconButton>
          ),
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            flexWrap: "nowrap",
            minHeight: "40px",
            alignItems: "center",
            paddingRight: "40px",
          },
        }}
      />

      <Modal open={openGenreModal} onClose={handleCloseGenreModal}>
        <Box sx={modalStyle}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              mb: 2,
            }}
          >
            <Typography variant="h6">Select Genres</Typography>
            <IconButton onClick={handleCloseGenreModal}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Autocomplete
            multiple
            options={genres}
            getOptionLabel={(option) => option.genre_type_name}
            value={tempSelectedGenres}
            onChange={(event, newValue) => {
              setTempSelectedGenres(newValue);
            }}
            renderInput={(params) => (
              <TextField {...params} label="Search genres" fullWidth />
            )}
            sx={{ mb: 2 }}
          />

          <Button
            variant="contained"
            onClick={saveSelectedGenres}
            fullWidth
          >
            Save Genres
          </Button>
        </Box>
      </Modal>
    </Box>
  );
};

export default GenreSection;