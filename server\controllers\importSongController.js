const ImportSongModel = require("../models/importSongModel");
const XLSX = require("xlsx");
const fs = require("fs");

exports.uploadExcel = async (req, res) => {
  try {
    const filePath = req.file.path;
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(sheet, {
      defval: "",
      raw: false,
      range: 0,
    });

    let inserted = 0;
    let skipped = 0;

    for (let row of data) {
      let song_name = row["Song Name"]?.trim();
      let artist_name = row["Artist Name"]?.trim();
      let video_url = row["Video URL"]?.trim() || "";

      if (!song_name || !artist_name) {
        skipped++;
        continue;
      }

      const result = await ImportSongModel.create({
        song_name,
        artist_name,
        video_url,
        status: "pending",
      });
      if (result.inserted) {
        inserted++;
      } else {
        skipped++;
      }
    }

    // Clean up uploaded file
    fs.unlinkSync(filePath);

    res.status(200).json({
      message: "Import successful",
      inserted,
      skipped,
    });
  } catch (error) {
    console.error("❌ Excel import error:", error);
    res.status(500).json({ error: "Failed to process Excel file" });
  }
};

exports.getAllImportedSongs = async (req, res) => {
  try {
    // Extract pagination and filtering parameters from query string
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || "";
    const status = req.query.status || "";
    const sortBy = req.query.sortBy || "created_at";
    const sortOrder = req.query.sortOrder || "DESC";

    // Validate pagination parameters
    if (page < 1) {
      return res.status(400).json({ error: "Page must be greater than 0" });
    }

    if (limit < 1 || limit > 500) {
      return res.status(400).json({ error: "Limit must be between 1 and 100" });
    }

    // Validate status if provided
    if (status && !["pending", "completed", "disabled"].includes(status)) {
      return res
        .status(400)
        .json({
          error: "Status must be 'pending', 'completed', or 'disabled'",
        });
    }

    // Validate sort parameters
    const validSortColumns = [
      "id",
      "song_name",
      "artist_name",
      "status",
      "created_at",
    ];
    if (!validSortColumns.includes(sortBy)) {
      return res
        .status(400)
        .json({
          error: `Invalid sortBy field. Valid options: ${validSortColumns.join(
            ", "
          )}`,
        });
    }

    if (!["ASC", "DESC"].includes(sortOrder.toUpperCase())) {
      return res
        .status(400)
        .json({ error: "SortOrder must be 'ASC' or 'DESC'" });
    }

    const options = {
      page,
      limit,
      search,
      status,
      sortBy,
      sortOrder,
    };

    const result = await ImportSongModel.getAll(options);

    res.status(200).json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("❌ Get all songs error:", error);
    res.status(500).json({ error: "Failed to fetch imported songs" });
  }
};

exports.getSongById = async (req, res) => {
  try {
    const { id } = req.params;
    const song = await ImportSongModel.getById(id);

    if (!song) {
      return res.status(404).json({ error: "Song not found" });
    }

    res.status(200).json(song);
  } catch (error) {
    console.error("❌ Get song by ID error:", error);
    res.status(500).json({ error: "Failed to fetch song" });
  }
};

exports.updateSong = async (req, res) => {
  try {
    const { id } = req.params;
    const { song_name, artist_name, video_url, status } = req.body;

    // Validate that at least one field is provided
    if (!song_name && !artist_name && !video_url && !status) {
      return res
        .status(400)
        .json({
          error:
            "At least one field (song_name, artist_name, video_url, or status) is required",
        });
    }

    // Validate status if provided
    if (status && !["pending", "completed"].includes(status)) {
      return res
        .status(400)
        .json({ error: "Status must be 'pending', 'completed'" });
    }

    const result = await ImportSongModel.update(id, {
      song_name,
      artist_name,
      video_url,
      status,
    });

    if (result.changes === 0) {
      return res.status(404).json({ error: "Song not found" });
    }

    res.status(200).json({
      message: "Song updated successfully",
      changes: result.changes,
    });
  } catch (error) {
    console.error("❌ Update song error:", error);
    res.status(500).json({ error: "Failed to update song" });
  }
};

exports.deleteSong = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await ImportSongModel.delete(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: "Song not found" });
    }

    res.status(200).json({
      message: "Song deleted successfully",
      changes: result.changes,
    });
  } catch (error) {
    console.error("❌ Delete song error:", error);
    res.status(500).json({ error: "Failed to delete song" });
  }
};

exports.getSongWithSpotifyData = async (req, res) => {
  try {
    const { id } = req.params;
    const songData = await ImportSongModel.getSongWithSpotifyData(id);

    if (!songData) {
      return res.status(404).json({ error: "Song not found" });
    }

    res.status(200).json(songData);
  } catch (error) {
    console.error("❌ Get song with Spotify data error:", error);
    res.status(500).json({ error: "Failed to fetch song with Spotify data" });
  }
};
