import React, { useState, useEffect } from 'react';
import {
    <PERSON>ton,
    Typography,
    Box,
    Container,
    Snackbar,
    Alert,
} from '@mui/material';
// import Header from '../Header';
import axios from 'axios';
import LoginLogoutComponent from './SpotifyAuth';
import PlaylistFormModal from './PlaylistFormModal';
import PullPlaylistModal from './PullPlaylistModal';
import PlaylistItem from './PlaylistItem';

const PlaylistMaster = () => {
    // State management
    const [playlists, setPlaylists] = useState([]);
    const [availableSongs, setAvailableSongs] = useState([]);
    const [playlistTypes, setPlaylistTypes] = useState([]);
    const [loading, setLoading] = useState(false);

    // Modal states
    const [createModalOpen, setCreateModalOpen] = useState(false);
    const [pullModalOpen, setPullModalOpen] = useState(false);
    const [editingPlaylist, setEditingPlaylist] = useState(null);

    // Notification state
    const [snackbar, setSnackbar] = useState({
        open: false,
        message: '',
        severity: 'success',
    });

    // Fetch all required data
    const fetchAllData = async () => {
        setLoading(true);
        try {
            const [songsRes, typesRes, playlistsRes] = await Promise.all([
                axios.get(`${process.env.REACT_APP_API_URL}/api/songs`),
                axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/playlist`),
                axios.get(`${process.env.REACT_APP_API_URL}/api/playlist`),
            ]);

            // Fetch songs for each playlist
            const playlistsWithSongs = await Promise.all(
                playlistsRes.data.map(async (playlist) => {
                    try {
                        const res = await axios.get(
                            `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlist.playlist_master_id}/songs`
                        );
                        return { ...playlist, songs: res.data || [] };
                    } catch (error) {
                        console.error(`Error fetching songs for playlist ${playlist.playlist_master_id}:`, error);
                        return { ...playlist, songs: [] };
                    }
                })
            );

            setAvailableSongs(songsRes.data);
            setPlaylistTypes(typesRes.data.filter((type) => type.status === 1));
            setPlaylists(playlistsWithSongs);
        } catch (error) {
            console.error('Error fetching data:', error);
            showSnackbar('Failed to load data', 'error');
        } finally {
            setLoading(false);
        }
    };

    // Load data on component mount
    useEffect(() => {
        fetchAllData();
    }, []);

    // Snackbar helper function
    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity,
        });
    };

    const handleCloseSnackbar = () => {
        setSnackbar(prev => ({ ...prev, open: false }));
    };

    // Modal handlers
    const handleOpenCreateModal = () => {
        setEditingPlaylist(null);
        setCreateModalOpen(true);
    };

    const handleCloseCreateModal = () => {
        setCreateModalOpen(false);
        setEditingPlaylist(null);
    };

    const handleOpenPullModal = () => {
        setPullModalOpen(true);
    };

    const handleClosePullModal = () => {
        setPullModalOpen(false);
    };

    // CRUD operations
    const handleSavePlaylist = async () => {
        try {
            await fetchAllData();
            showSnackbar(
                editingPlaylist ? 'Playlist updated successfully!' : 'Playlist created successfully!'
            );
        } catch (error) {
            showSnackbar('Failed to save playlist', 'error');
        }
    };

    const handleEditPlaylist = (playlist) => {
        setEditingPlaylist(playlist);
        setCreateModalOpen(true);
    };

    const handleDeletePlaylist = async (playlistId) => {
        if (!window.confirm('Are you sure you want to delete this playlist?')) {
            return;
        }

        try {
            await axios.delete(`${process.env.REACT_APP_API_URL}/api/playlist/${playlistId}`);
            await fetchAllData();
            showSnackbar('Playlist deleted successfully!');
        } catch (error) {
            console.error('Error deleting playlist:', error);
            showSnackbar('Failed to delete playlist', 'error');
        }
    };

    const handlePlaylistPulled = async (newPlaylist) => {
        await fetchAllData();
        showSnackbar('Playlist pulled successfully!');
    };

    return (
        <>
            {/* <Header /> */}
            <Container
                sx={{
                    mt: 2,
                    pb: 4,
                    bgcolor: '#121212',
                    minHeight: '90vh',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 2,
                }}
            >
                {/* Header Section */}
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mt: 2,
                        pb: 2,
                        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                        flexWrap: 'wrap',
                        gap: 2,
                    }}
                >
                    <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                        All Playlists ({playlists.length})
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>

                        <Button
                            variant="contained"
                            color="success"
                            onClick={handleOpenCreateModal}
                            disabled={loading}
                        >
                            Create New Playlist
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleOpenPullModal}
                            disabled={loading}
                        >
                            Pull Playlist
                        </Button>
                        <LoginLogoutComponent />
                    </Box>
                </Box>

                {/* Loading State */}
                {loading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Typography variant="h6" sx={{ color: 'white' }}>
                            Loading playlists...
                        </Typography>
                    </Box>
                )}

                {/* Empty State */}
                {!loading && playlists.length === 0 && (
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            mt: 8,
                            textAlign: 'center',
                        }}
                    >
                        <Typography variant="h5" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                            No playlists found
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.5)', mb: 3 }}>
                            Create your first playlist or pull one from Spotify to get started!
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                            <Button
                                variant="contained"
                                color="success"
                                onClick={handleOpenCreateModal}
                            >
                                Create Playlist
                            </Button>
                            <Button
                                variant="outlined"
                                color="primary"
                                onClick={handleOpenPullModal}
                            >
                                Pull from Spotify
                            </Button>
                        </Box>
                    </Box>
                )}

                {/* Playlists List */}
                {!loading && playlists.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                        {playlists.map((playlist) => (
                            <PlaylistItem
                                key={playlist.playlist_master_id}
                                playlist={playlist}
                                onEdit={handleEditPlaylist}
                                onDelete={handleDeletePlaylist}
                            />
                        ))}
                    </Box>
                )}

                {/* Create/Edit Playlist Modal */}
                <PlaylistFormModal
                    open={createModalOpen}
                    onClose={handleCloseCreateModal}
                    onSave={handleSavePlaylist}
                    editingPlaylist={editingPlaylist}
                    availableSongs={availableSongs}
                    playlistTypes={playlistTypes}
                />

                {/* Pull Playlist Modal */}
                <PullPlaylistModal
                    open={pullModalOpen}
                    onClose={handleClosePullModal}
                    onPlaylistAdded={handlePlaylistPulled}
                />

                {/* Snackbar for notifications */}
                <Snackbar
                    open={snackbar.open}
                    autoHideDuration={4000}
                    onClose={handleCloseSnackbar}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                >
                    <Alert
                        onClose={handleCloseSnackbar}
                        severity={snackbar.severity}
                        sx={{ width: '100%' }}
                    >
                        {snackbar.message}
                    </Alert>
                </Snackbar>
            </Container>
        </>
    );
};

export default PlaylistMaster;