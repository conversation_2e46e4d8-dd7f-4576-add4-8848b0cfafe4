import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Alert,
  <PERSON>ack,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as <PERSON>rrorIcon,
  PlayArrow as TestIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as DuplicateIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';

const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  marginBottom: '16px',
}));

const TestButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  backgroundColor: '#4CAF50',
  color: '#fff',
  '&:hover': {
    backgroundColor: '#45a049',
  },
}));

const QuestionManagementTest = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const tests = {
    'API Connectivity Test': async () => {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions`);
      if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const data = await response.json();
      return {
        message: `Successfully connected to API. Found ${data.length} questions.`,
        data: { questionsCount: data.length, status: response.status }
      };
    },

    'Question List Data Integrity Test': async () => {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions`);
      const questions = await response.json();
      
      if (questions.length === 0) {
        return { message: 'No questions found - create some questions first', data: {} };
      }

      const firstQuestion = questions[0];
      const requiredFields = [
        'question_id', 'question_text', 'song_name', 'level_name', 
        'time_limit_seconds', 'created_at', 'answer_type', 'difficulty_id'
      ];
      
      const missingFields = requiredFields.filter(field => !(field in firstQuestion));
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      return {
        message: `Data integrity verified. All ${requiredFields.length} required fields present.`,
        data: { 
          sampleQuestion: firstQuestion.question_text?.substring(0, 50) + '...',
          fieldsChecked: requiredFields.length 
        }
      };
    },

    'Question Detail Fetch Test': async () => {
      const listResponse = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions`);
      const questions = await listResponse.json();
      
      if (questions.length === 0) {
        throw new Error('No questions available for detail test');
      }

      const questionId = questions[0].question_id;
      const detailResponse = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/${questionId}`);
      
      if (!detailResponse.ok) {
        throw new Error(`Failed to fetch question details: ${detailResponse.status}`);
      }

      const questionDetail = await detailResponse.json();
      
      if (!questionDetail.answers || !Array.isArray(questionDetail.answers)) {
        throw new Error('Question detail missing answers array');
      }

      return {
        message: `Question detail fetch successful. Found ${questionDetail.answers.length} answers.`,
        data: { 
          questionId, 
          answersCount: questionDetail.answers.length,
          hasAnswers: questionDetail.answers.length > 0
        }
      };
    },

    'Statistics API Test': async () => {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/stats`);
      if (!response.ok) throw new Error(`Stats API failed: ${response.status}`);
      
      const stats = await response.json();
      const requiredStats = ['total_questions', 'by_difficulty', 'average_time_limit'];
      const missingStats = requiredStats.filter(stat => !(stat in stats));
      
      if (missingStats.length > 0) {
        throw new Error(`Missing stats: ${missingStats.join(', ')}`);
      }

      return {
        message: `Statistics API working. Total questions: ${stats.total_questions}`,
        data: { 
          totalQuestions: stats.total_questions,
          avgTimeLimit: stats.average_time_limit,
          difficultiesCount: stats.by_difficulty?.length || 0
        }
      };
    },

    'Update Endpoint Test': async () => {
      // This test checks if the update endpoint exists and responds correctly
      const listResponse = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions`);
      const questions = await listResponse.json();
      
      if (questions.length === 0) {
        throw new Error('No questions available for update test');
      }

      const questionId = questions[0].question_id;
      
      // Try to fetch the question for editing (this tests the GET endpoint)
      const getResponse = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/${questionId}`);
      if (!getResponse.ok) {
        throw new Error(`GET endpoint failed: ${getResponse.status}`);
      }

      // Test if PUT endpoint exists (we won't actually update, just check if it responds)
      const testResponse = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/${questionId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}) // Empty body to trigger validation error
      });

      // We expect a 400 error for invalid payload, which means the endpoint exists
      if (testResponse.status === 404) {
        throw new Error('PUT endpoint not found');
      }

      return {
        message: 'Update endpoint is available and responding correctly.',
        data: { 
          getStatus: getResponse.status,
          putStatus: testResponse.status,
          endpointExists: testResponse.status !== 404
        }
      };
    },

    'Lookup Data Integration Test': async () => {
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        fetch(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        fetch(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`),
        fetch(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`)
      ]);

      if (!difficulties.ok || !questionTypes.ok || !answerTypes.ok) {
        throw new Error('One or more lookup endpoints failed');
      }

      const [diffData, qtData, atData] = await Promise.all([
        difficulties.json(),
        questionTypes.json(),
        answerTypes.json()
      ]);

      const activeDifficulties = diffData.filter(item => item.status === 1);
      const activeQuestionTypes = qtData.filter(item => item.status === 1);
      const activeAnswerTypes = atData.filter(item => item.status === 1);

      if (activeDifficulties.length === 0 || activeQuestionTypes.length === 0 || activeAnswerTypes.length === 0) {
        throw new Error('Missing active lookup data required for question creation');
      }

      return {
        message: `Lookup integration verified. Found ${activeDifficulties.length} difficulties, ${activeQuestionTypes.length} question types, ${activeAnswerTypes.length} answer types.`,
        data: {
          difficulties: activeDifficulties.length,
          questionTypes: activeQuestionTypes.length,
          answerTypes: activeAnswerTypes.length
        }
      };
    }
  };

  const runTest = async (testName) => {
    setIsRunning(true);
    try {
      const result = await tests[testName]();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, ...result }
      }));
      enqueueSnackbar(`✅ ${testName} passed`, { variant: 'success' });
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, message: error.message, error: error.toString() }
      }));
      enqueueSnackbar(`❌ ${testName} failed: ${error.message}`, { variant: 'error' });
    } finally {
      setIsRunning(false);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    for (const testName of Object.keys(tests)) {
      await runTest(testName);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
  };

  const getOverallStatus = () => {
    const results = Object.values(testResults);
    if (results.length === 0) return 'not-run';
    const allPassed = results.every(result => result.success);
    const anyFailed = results.some(result => !result.success);
    
    if (allPassed) return 'success';
    if (anyFailed) return 'error';
    return 'partial';
  };

  const overallStatus = getOverallStatus();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ color: '#fff', mb: 3, fontWeight: 600 }}>
        Question Management System Test Suite
      </Typography>

      <StyledCard>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ color: '#fff' }}>
              Comprehensive Functionality Tests
            </Typography>
            <Stack direction="row" spacing={2}>
              <TestButton
                onClick={runAllTests}
                disabled={isRunning}
                startIcon={<TestIcon />}
              >
                {isRunning ? 'Running Tests...' : 'Run All Tests'}
              </TestButton>
            </Stack>
          </Box>

          {overallStatus !== 'not-run' && (
            <Alert 
              severity={overallStatus === 'success' ? 'success' : overallStatus === 'error' ? 'error' : 'warning'}
              sx={{ mb: 3 }}
            >
              {overallStatus === 'success' && '🎉 All tests passed! Question management system is fully functional.'}
              {overallStatus === 'error' && '⚠️ Some tests failed. Check individual results below.'}
              {overallStatus === 'partial' && '⏳ Tests in progress...'}
            </Alert>
          )}

          <List>
            {Object.entries(tests).map(([testName, testFn], index) => {
              const result = testResults[testName];
              return (
                <React.Fragment key={testName}>
                  <ListItem>
                    <ListItemIcon>
                      {result ? (
                        result.success ? (
                          <CheckIcon sx={{ color: '#4CAF50' }} />
                        ) : (
                          <ErrorIcon sx={{ color: '#F44336' }} />
                        )
                      ) : (
                        <TestIcon sx={{ color: '#ccc' }} />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" sx={{ color: '#fff' }}>
                          {testName}
                        </Typography>
                      }
                      secondary={
                        result && (
                          <Box mt={1}>
                            <Typography variant="body2" sx={{ color: result.success ? '#4CAF50' : '#F44336' }}>
                              {result.message}
                            </Typography>
                            {result.data && Object.keys(result.data).length > 0 && (
                              <Box mt={1}>
                                {Object.entries(result.data).map(([key, value]) => (
                                  <Chip
                                    key={key}
                                    label={`${key}: ${value}`}
                                    size="small"
                                    sx={{ 
                                      mr: 1, 
                                      mb: 0.5,
                                      backgroundColor: '#333',
                                      color: '#ccc'
                                    }}
                                  />
                                ))}
                              </Box>
                            )}
                          </Box>
                        )
                      }
                    />
                    <Button
                      size="small"
                      onClick={() => runTest(testName)}
                      disabled={isRunning}
                      sx={{ color: '#4CAF50' }}
                    >
                      Run Test
                    </Button>
                  </ListItem>
                  {index < Object.keys(tests).length - 1 && <Divider sx={{ borderColor: '#333' }} />}
                </React.Fragment>
              );
            })}
          </List>
        </CardContent>
      </StyledCard>

      <StyledCard>
        <CardContent>
          <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
            Manual Testing Checklist
          </Typography>
          <List>
            {[
              { icon: <ViewIcon />, text: 'Test View Question Details dialog', color: '#2196F3' },
              { icon: <EditIcon />, text: 'Test Edit Question functionality', color: '#FF9800' },
              { icon: <DeleteIcon />, text: 'Test Delete Question with confirmation', color: '#F44336' },
              { icon: <DuplicateIcon />, text: 'Test Duplicate Question feature', color: '#4CAF50' }
            ].map((item, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  {React.cloneElement(item.icon, { sx: { color: item.color } })}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body2" sx={{ color: '#fff' }}>
                      {item.text}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </StyledCard>
    </Box>
  );
};

export default QuestionManagementTest;
