import React, { useState, useRef } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import axios from 'axios';

const PullPlaylistModal = ({ open, onClose, onPlaylistAdded }) => {
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [playlistPreview, setPlaylistPreview] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const processingRef = useRef(false);

  const extractSpotifyPlaylistId = (url) => {
    const match = url.match(/playlist\/([a-zA-Z0-9]+)(\?|$)/);
    return match ? match[1] : null;
  };

  const handlePullPlaylist = async () => {
    if (isProcessing || processingRef.current) return;

    const playlistId = extractSpotifyPlaylistId(playlistUrl);
    if (!playlistId) {
      setError('Invalid Spotify playlist URL');
      return;
    }

    try {
      setIsProcessing(true);
      processingRef.current = true;
      setLoading(true);
      setError('');
      setSuccess('');

      console.log('Starting playlist pull for ID:', playlistId);

      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${playlistId}`
      );

      const newPlaylist = response.data;
      setPlaylistPreview(newPlaylist); // show preview details

      if (newPlaylist.duplicates && newPlaylist.duplicates.length > 0) {
        const duplicateNames = newPlaylist.duplicates
          .map(song => `${song.name} by ${song.artist}`)
          .join(', ');
        setSuccess(
          `Playlist added successfully! Skipped ${newPlaylist.duplicates.length} duplicates: ${duplicateNames}`
        );
      } else {
        setSuccess('Playlist added successfully!');
      }

      if (onPlaylistAdded && typeof onPlaylistAdded === 'function') {
        onPlaylistAdded(newPlaylist);
        handleClose();
      }

      setTimeout(() => {
        if (!processingRef.current) return;
        handleClose();
      }, 2000);

    } catch (error) {
      console.error('Error pulling playlist:', error);
      setError('Failed to pull playlist. Please try again.');
    } finally {
      setLoading(false);
      setIsProcessing(false);
      processingRef.current = false;
    }
  };

  const handleClose = () => {
    setPlaylistUrl('');
    setPlaylistPreview(null);
    setError('');
    setSuccess('');
    setLoading(false);
    setIsProcessing(false);
    processingRef.current = false;
    if (onClose && typeof onClose === 'function') onClose();
  };

  const handleDialogClose = (event, reason) => {
    if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
      if (!isProcessing && !loading) handleClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleDialogClose}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={isProcessing || loading}
    >
      <DialogTitle>Pull Playlist from Spotify</DialogTitle>

      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Spotify Playlist URL"
            placeholder="https://open.spotify.com/playlist/..."
            value={playlistUrl}
            onChange={(e) => setPlaylistUrl(e.target.value)}
            fullWidth
            disabled={loading || isProcessing}
          />

          {error && (
            <Alert severity="error" onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {(loading || isProcessing) && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
              <CircularProgress />
              <Typography variant="body2" sx={{ ml: 2, alignSelf: 'center' }}>
                {isProcessing ? 'Processing playlist...' : 'Loading...'}
              </Typography>
            </Box>
          )}

          {playlistPreview && !loading && !isProcessing && (
            <Box sx={{ border: '1px solid #ddd', borderRadius: 1, p: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                {playlistPreview.images && playlistPreview.images[0] && (
                  <img
                    src={playlistPreview.images[0].url}
                    alt={playlistPreview.name}
                    style={{ width: 80, height: 80, borderRadius: 4 }}
                  />
                )}
                <Box>
                  <Typography variant="h6">{playlistPreview.name}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {playlistPreview.owner?.display_name} • {playlistPreview.tracks?.total} tracks
                  </Typography>
                  {playlistPreview.description && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {playlistPreview.description}
                    </Typography>
                  )}
                </Box>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Typography variant="body2" color="textSecondary">
                Imported successfully into your music database.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          disabled={loading || isProcessing}
        >
          Cancel
        </Button>
        <Button
          onClick={handlePullPlaylist}
          variant="contained"
          disabled={loading || isProcessing || !playlistUrl.trim()}
          color="success"
        >
          {(loading || isProcessing) ? 'Pulling...' : 'Pull Playlist'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PullPlaylistModal;
