import React, { useState } from "react";
import {
  Box,
  Table,
  TableContainer,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
  Paper,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  Tooltip,
  Chip,
} from "@mui/material";
import {
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  Restore as RestoreIcon,
} from "@mui/icons-material";
import axios from "axios";

const MigratedTracksTable = ({ tracks, fetchTracks }) => {
  const [loadingStates, setLoadingStates] = useState({
    delete: {},
    restore: {},
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: "",
    message: "",
    onConfirm: null,
    loading: false,
  });
  const [viewDialog, setViewDialog] = useState({
    open: false,
    track: null,
  });

  // View track details
  const handleViewTrack = (track) => {
    setViewDialog({
      open: true,
      track: track,
    });
  };

  // Delete migrated track (remove from main database)
  const handleDelete = async (id) => {
    setConfirmDialog({
      open: true,
      title: "Delete Migrated Track",
      message:
        "Are you sure you want to delete this migrated track from the main database? This action cannot be undone.",
      onConfirm: async () => {
        setConfirmDialog((prev) => ({ ...prev, loading: true }));
        setLoadingStates((prev) => ({
          ...prev,
          delete: { ...prev.delete, [id]: true },
        }));

        try {
          await axios.delete(
            `${process.env.REACT_APP_API_URL}/api/migrate/migrated/${id}`
          );
          setSnackbar({
            open: true,
            message: "Migrated track deleted successfully",
            severity: "success",
          });
          fetchTracks();
        } catch (error) {
          setSnackbar({
            open: true,
            message: error.response?.data?.error || "Deletion failed",
            severity: "error",
          });
        } finally {
          setLoadingStates((prev) => ({
            ...prev,
            delete: { ...prev.delete, [id]: false },
          }));
          setConfirmDialog((prev) => ({
            ...prev,
            open: false,
            loading: false,
          }));
        }
      },
    });
  };

  // Restore track to temp (move back to temp table)
  const handleRestore = async (id) => {
    setConfirmDialog({
      open: true,
      title: "Restore Track",
      message:
        "Are you sure you want to restore this track back to the temporary table?",
      onConfirm: async () => {
        setConfirmDialog((prev) => ({ ...prev, loading: true }));
        setLoadingStates((prev) => ({
          ...prev,
          restore: { ...prev.restore, [id]: true },
        }));

        try {
          await axios.post(
            `${process.env.REACT_APP_API_URL}/api/migrate/restore/${id}`
          );
          setSnackbar({
            open: true,
            message: "Track restored to temporary table successfully",
            severity: "success",
          });
          fetchTracks();
        } catch (error) {
          setSnackbar({
            open: true,
            message: error.response?.data?.error || "Restoration failed",
            severity: "error",
          });
        } finally {
          setLoadingStates((prev) => ({
            ...prev,
            restore: { ...prev.restore, [id]: false },
          }));
          setConfirmDialog((prev) => ({
            ...prev,
            open: false,
            loading: false,
          }));
        }
      },
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (tracks.length === 0) {
    return (
      <Paper elevation={2} sx={{ p: 2, textAlign: "center" }}>
        <Typography variant="subtitle1">No migrated tracks found</Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Tracks will appear here after they are successfully migrated from the
          Temp tab.
        </Typography>
      </Paper>
    );
  }

  return (
    <>
      <Typography variant="h6" gutterBottom sx={{ color: "white", padding: 2 }}>
        Migrated Tracks ({tracks.length})
      </Typography>
      <Paper
        sx={{
          bgcolor: "#121212",
          color: "white",
          width: "100%",
          overflow: "hidden",
        }}
      >
        <TableContainer sx={{ width: "100%", overflowX: "auto" }}>
          <Table sx={{ minWidth: 900 }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: "#1DB954" }}>
                <TableCell
                  sx={{
                    color: "white",
                    fontWeight: "bold",
                    width: "18%",
                    whiteSpace: "nowrap",
                  }}
                >
                  Track Name
                </TableCell>
                <TableCell
                  sx={{ color: "white", fontWeight: "bold", width: "15%" }}
                >
                  Artist
                </TableCell>
                <TableCell
                  sx={{ color: "white", fontWeight: "bold", width: "15%" }}
                >
                  Album
                </TableCell>
                <TableCell
                  sx={{ color: "white", fontWeight: "bold", width: "8%" }}
                >
                  Year
                </TableCell>
                <TableCell
                  sx={{ color: "white", fontWeight: "bold", width: "10%" }}
                >
                  Duration
                </TableCell>
                <TableCell
                  sx={{
                    color: "white",
                    fontWeight: "bold",
                    width: "16%",
                    whiteSpace: "nowrap",
                  }}
                >
                  Migration Date
                </TableCell>
                <TableCell
                  sx={{ color: "white", fontWeight: "bold", width: "8%" }}
                >
                  Status
                </TableCell>
                <TableCell
                  sx={{
                    color: "white",
                    fontWeight: "bold",
                    width: "10%",
                    whiteSpace: "nowrap",
                  }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>

            <TableBody>
              {tracks.map((track) => (
                <TableRow key={track._id} hover>
                  <TableCell sx={{ color: "white" }}>
                    {track.track_name}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {track.artist_name}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {track.album_name}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>{track.year}</TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {track.duration}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {formatDate(track.migration_date)}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label="Migrated"
                      color="success"
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          onClick={() => handleViewTrack(track)}
                          color="primary"
                          size="small"
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Restore to Temp">
                        <IconButton
                          onClick={() => handleRestore(track._id)}
                          color="secondary"
                          disabled={loadingStates.restore[track._id]}
                          size="small"
                        >
                          {loadingStates.restore[track._id] ? (
                            <CircularProgress size={20} />
                          ) : (
                            <RestoreIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Permanently">
                        <IconButton
                          onClick={() => handleDelete(track._id)}
                          color="error"
                          disabled={loadingStates.delete[track._id]}
                          size="small"
                        >
                          {loadingStates.delete[track._id] ? (
                            <CircularProgress size={20} />
                          ) : (
                            <DeleteIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* View Track Dialog */}
        <Dialog
          open={viewDialog.open}
          onClose={() => setViewDialog({ open: false, track: null })}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Track Details</DialogTitle>
          <DialogContent dividers>
            {viewDialog.track && (
              <>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Track Name:</strong> {viewDialog.track.track_name}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Artist:</strong> {viewDialog.track.artist_name}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Album:</strong> {viewDialog.track.album_name}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Year:</strong> {viewDialog.track.year}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Duration:</strong> {viewDialog.track.duration}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Migration Date:</strong>{" "}
                  {formatDate(viewDialog.track.migration_date)}
                </Typography>
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Spotify ID:</strong>{" "}
                  {viewDialog.track.spotify_track_id || "N/A"}
                </Typography>
                {/* <Typography variant="subtitle1">
                  <strong>ISRC:</strong> {viewDialog.track.isrc || "N/A"}
                </Typography> */}
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setViewDialog({ open: false, track: null })}
              color="primary"
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Confirmation Dialog */}
        <Dialog
          open={confirmDialog.open}
          onClose={() =>
            !confirmDialog.loading &&
            setConfirmDialog({ ...confirmDialog, open: false })
          }
        >
          <DialogTitle>{confirmDialog.title}</DialogTitle>
          <DialogContent>
            <Typography>{confirmDialog.message}</Typography>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() =>
                !confirmDialog.loading &&
                setConfirmDialog({ ...confirmDialog, open: false })
              }
              disabled={confirmDialog.loading}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDialog.onConfirm}
              color="primary"
              disabled={confirmDialog.loading}
            >
              {confirmDialog.loading ? (
                <CircularProgress size={24} />
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Paper>
    </>
  );
};

export default MigratedTracksTable;
