import React, { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Button,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { STORAGE_KEYS, FIELD_MAP } from "./constants";

const LookupForm = ({
  items,
  handleClose,
  editIndex,
  handleSave,
  typeIndex,
}) => {
  const type = STORAGE_KEYS[typeIndex];
  const fields = FIELD_MAP[type];
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [status, setStatus] = useState(true);

  useEffect(() => {
    if (editIndex !== null && items[editIndex]) {
      setName(items[editIndex][fields.name] || "");
      setDescription(items[editIndex][fields.description] || "");
      setStatus(items[editIndex][fields.status] === 1);
    } else {
      setName("");
      setDescription("");
      setStatus(true);
    }
  }, [editIndex, items, fields]);

  const handleSaveClick = () => {
    if (!name.trim()) return;
    const now = new Date().toISOString();
    const newItem = {
      [fields.name]: name,
      [fields.description]: description,
      [fields.status]: status ? 1 : 0,
      [fields.createdAt]: now,
    };
    handleSave(newItem);
    handleClose();
  };

  return (
    <Box>
      <TextField
        label="Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
        fullWidth
        margin="dense"
      />
      <TextField
        label="Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        fullWidth
        margin="dense"
      />
      <FormControlLabel
        control={
          <Switch
            checked={status}
            onChange={(e) => setStatus(e.target.checked)}
          />
        }
        label="Active"
      />
      <Button onClick={handleSaveClick} variant="contained" sx={{ mt: 1 }}>
        {editIndex !== null ? "Update" : "Add"}
      </Button>
    </Box>
  );
};

export default LookupForm;