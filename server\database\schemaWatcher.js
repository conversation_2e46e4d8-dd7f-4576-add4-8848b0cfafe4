const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const SchemaSynchronizer = require('./schemaSynchronizer');

class SchemaWatcher {
  constructor(dbPath, schemaPath, options = {}) {
    this.dbPath = dbPath;
    this.schemaPath = schemaPath;
    this.options = {
      autoSync: options.autoSync !== false, // Default to true
      debounceMs: options.debounceMs || 2000, // Wait 2 seconds after last change
      enableWatcher: options.enableWatcher !== false, // Default to true
      ...options
    };
    
    this.watcher = null;
    this.debounceTimer = null;
    this.synchronizer = new SchemaSynchronizer(dbPath, schemaPath);
    this.isProcessing = false;
  }

  async initialize() {
    try {
      await this.synchronizer.initialize();
      
      // Perform initial schema check on startup
      console.log('🔍 Performing initial schema check...');
      await this.performSchemaCheck();
      
      // Start file watcher if enabled
      if (this.options.enableWatcher) {
        this.startWatcher();
      }
      
      console.log('✅ Schema watcher initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize schema watcher:', error);
      throw error;
    }
  }

  startWatcher() {
    if (this.watcher) {
      console.log('⚠️  Schema watcher already running');
      return;
    }

    console.log(`👀 Starting schema file watcher for: ${this.schemaPath}`);
    
    this.watcher = chokidar.watch(this.schemaPath, {
      persistent: true,
      ignoreInitial: true, // Don't trigger on startup
      awaitWriteFinish: {
        stabilityThreshold: 1000,
        pollInterval: 100
      }
    });

    this.watcher.on('change', (filePath) => {
      console.log(`📝 Schema file changed: ${filePath}`);
      this.handleSchemaChange();
    });

    this.watcher.on('error', (error) => {
      console.error('❌ Schema watcher error:', error);
    });

    console.log('✅ Schema file watcher started');
  }

  handleSchemaChange() {
    // Clear existing timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set new timer to debounce rapid changes
    this.debounceTimer = setTimeout(() => {
      this.performSchemaCheck();
    }, this.options.debounceMs);

    console.log(`⏱️  Schema change detected - will check in ${this.options.debounceMs}ms`);
  }

  async performSchemaCheck() {
    if (this.isProcessing) {
      console.log('⏳ Schema check already in progress - skipping');
      return;
    }

    this.isProcessing = true;

    try {
      console.log('🔍 Checking schema for changes...');
      
      const result = await this.synchronizer.checkAndSync();
      
      if (result.migrationNeeded) {
        console.log('✅ Schema migration completed successfully');
        console.log(`📊 Migration changes: ${result.changes.length} operations`);
        
        // Emit event for other parts of the application
        this.emit('migrationCompleted', result);
      } else {
        console.log('ℹ️  No schema changes detected');
      }

      return result;

    } catch (error) {
      console.error('❌ Schema check failed:', error);
      this.emit('migrationFailed', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  async forceSync() {
    console.log('🔄 Forcing schema synchronization...');
    return await this.performSchemaCheck();
  }

  stopWatcher() {
    if (this.watcher) {
      console.log('🛑 Stopping schema file watcher...');
      this.watcher.close();
      this.watcher = null;
      console.log('✅ Schema file watcher stopped');
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  async getSchemaStatus() {
    try {
      const schemaContent = fs.readFileSync(this.schemaPath, 'utf8');
      const newSchemaHash = this.synchronizer.migrationManager.calculateSchemaHash(schemaContent);
      const currentSchemaHash = await this.synchronizer.migrationManager.getCurrentSchemaHash();
      
      return {
        schemaPath: this.schemaPath,
        dbPath: this.dbPath,
        currentHash: currentSchemaHash,
        fileHash: newSchemaHash,
        inSync: currentSchemaHash === newSchemaHash,
        watcherActive: !!this.watcher,
        lastModified: fs.statSync(this.schemaPath).mtime
      };
    } catch (error) {
      console.error('❌ Failed to get schema status:', error);
      throw error;
    }
  }

  async getMigrationHistory() {
    try {
      return new Promise((resolve, reject) => {
        const query = `SELECT * FROM schema_migrations ORDER BY id DESC LIMIT 10`;
        this.synchronizer.migrationManager.db.all(query, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    } catch (error) {
      console.error('❌ Failed to get migration history:', error);
      throw error;
    }
  }

  // Simple event emitter functionality
  emit(event, data) {
    if (this.listeners && this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  on(event, callback) {
    if (!this.listeners) {
      this.listeners = {};
    }
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  async close() {
    console.log('🔄 Closing schema watcher...');
    this.stopWatcher();
    await this.synchronizer.close();
    console.log('✅ Schema watcher closed');
  }
}

module.exports = SchemaWatcher;
