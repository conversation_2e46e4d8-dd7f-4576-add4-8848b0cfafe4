import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton
} from "@mui/material";
import MenuItem from "@mui/material/MenuItem";
import SwapVertIcon from '@mui/icons-material/SwapVert';

const EditSongDialog = ({ song, open, onClose, onSave }) => {
  const [editForm, setEditForm] = useState({
    song_name: "",
    artist_name: "",
    status: "",
  }); // Initialize status

  useEffect(() => {
    if (song) {
      setEditForm({
        song_name: song.song_name,
        artist_name: song.artist_name,
        status: song.status, // Ensure status is set
      });
    }
  }, [song]);

  const handleSave = () => {
    onSave(editForm);
  };

  const handleInputChange = (field, value) => {
    setEditForm((prev) => ({ ...prev, [field]: value }));
  };

  // Swap song name and artist name
  const handleSwap = () => {
    setEditForm((prev) => ({
      ...prev,
      song_name: prev.artist_name,
      artist_name: prev.song_name,
    }));
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Edit Song</DialogTitle>
      <DialogContent sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <TextField
          label="Song Name"
          value={editForm.song_name}
          onChange={(e) => handleInputChange("song_name", e.target.value)}
          fullWidth
        />
        {/* Swap Icon Button */}
        <IconButton
          onClick={handleSwap}
          sx={{ alignSelf: "center" }}
          color="primary"
        >
          <SwapVertIcon fontSize="large" />
        </IconButton>
        <TextField
          label="Artist Name"
          value={editForm.artist_name}
          onChange={(e) => handleInputChange("artist_name", e.target.value)}
          fullWidth
        />
        <TextField
          label="Status"
          select
          value={editForm.status}
          onChange={(e) => handleInputChange("status", e.target.value)}
          fullWidth
        >
          <MenuItem value="pending">Pending</MenuItem>
          <MenuItem value="completed">Completed</MenuItem>
        </TextField>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" onClick={handleSave}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditSongDialog;
