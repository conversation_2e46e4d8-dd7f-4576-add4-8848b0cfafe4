const express = require('express');
const router = express.Router();
const { getDatabaseManager } = require('../database');

// GET /api/schema/status - Get current schema synchronization status
router.get('/status', async (req, res) => {
  try {
    const dbManager = getDatabaseManager();
    const status = await dbManager.getSchemaStatus();
    
    res.json({
      success: true,
      data: {
        ...status,
        message: status.inSync ? 'Schema is synchronized' : '<PERSON>hem<PERSON> needs synchronization'
      }
    });
  } catch (error) {
    console.error('Error getting schema status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get schema status',
      details: error.message
    });
  }
});

// GET /api/schema/history - Get migration history
router.get('/history', async (req, res) => {
  try {
    const dbManager = getDatabaseManager();
    const history = await dbManager.getMigrationHistory();
    
    res.json({
      success: true,
      data: history,
      count: history.length
    });
  } catch (error) {
    console.error('Error getting migration history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get migration history',
      details: error.message
    });
  }
});

// POST /api/schema/sync - Force schema synchronization
router.post('/sync', async (req, res) => {
  try {
    const dbManager = getDatabaseManager();
    
    console.log('🔄 Manual schema synchronization requested');
    const result = await dbManager.forceSchemaSync();
    
    res.json({
      success: true,
      data: result,
      message: result.migrationNeeded ? 
        'Schema synchronization completed successfully' : 
        'Schema was already synchronized'
    });
  } catch (error) {
    console.error('Error during manual schema sync:', error);
    res.status(500).json({
      success: false,
      error: 'Schema synchronization failed',
      details: error.message
    });
  }
});

// GET /api/schema/info - Get detailed schema information
router.get('/info', async (req, res) => {
  try {
    const dbManager = getDatabaseManager();
    const db = dbManager.getDatabase();
    
    // Get all tables
    const tables = await new Promise((resolve, reject) => {
      db.all(`SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows.map(row => row.name));
      });
    });

    // Get table details
    const tableDetails = {};
    for (const tableName of tables) {
      const columns = await new Promise((resolve, reject) => {
        db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
      
      const rowCount = await new Promise((resolve, reject) => {
        db.get(`SELECT COUNT(*) as count FROM ${tableName}`, (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      tableDetails[tableName] = {
        columns: columns.length,
        rows: rowCount,
        columnDetails: columns
      };
    }

    res.json({
      success: true,
      data: {
        totalTables: tables.length,
        tables: tableDetails,
        tableNames: tables
      }
    });
  } catch (error) {
    console.error('Error getting schema info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get schema information',
      details: error.message
    });
  }
});

// POST /api/schema/validate - Validate schema file
router.post('/validate', async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const dbManager = getDatabaseManager();
    const schemaPath = path.join(__dirname, '../database/schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      return res.status(404).json({
        success: false,
        error: 'Schema file not found'
      });
    }

    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    
    // Basic validation
    const issues = [];
    
    // Check for basic SQL syntax issues
    if (!schemaContent.trim()) {
      issues.push('Schema file is empty');
    }
    
    if (!schemaContent.includes('CREATE TABLE')) {
      issues.push('No CREATE TABLE statements found');
    }

    // Check for common issues
    const lines = schemaContent.split('\n');
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (trimmed.includes('CREATE TABLE') && !trimmed.includes('(')) {
        const nextLine = lines[index + 1];
        if (!nextLine || !nextLine.trim().startsWith('(')) {
          issues.push(`Line ${index + 1}: CREATE TABLE statement may be malformed`);
        }
      }
    });

    res.json({
      success: true,
      data: {
        valid: issues.length === 0,
        issues: issues,
        lineCount: lines.length,
        size: schemaContent.length,
        lastModified: fs.statSync(schemaPath).mtime
      }
    });
  } catch (error) {
    console.error('Error validating schema:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate schema',
      details: error.message
    });
  }
});

// GET /api/schema/backup - Create database backup
router.post('/backup', async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const dbManager = getDatabaseManager();
    const dbPath = path.join(__dirname, '../../app.db');
    const backupDir = path.join(__dirname, '../../backups');
    
    // Ensure backup directory exists
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `app_backup_${timestamp}.db`);
    
    // Copy database file
    fs.copyFileSync(dbPath, backupPath);
    
    const stats = fs.statSync(backupPath);
    
    res.json({
      success: true,
      data: {
        backupPath: backupPath,
        size: stats.size,
        created: stats.birthtime,
        message: 'Database backup created successfully'
      }
    });
  } catch (error) {
    console.error('Error creating backup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create backup',
      details: error.message
    });
  }
});

module.exports = router;
