import { useEffect, useState, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import PlaylistSection from "./PlaylistSection";
import SongListSection from "./SongListSection";
import ArtistListSection from "./ArtistListSection";
import NavigationBar from "./NavigationBar";
import {
  Box,
  Typography,
  CircularProgress,
  Collapse,
  IconButton,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { Element, scroller } from "react-scroll";
import TabNavigation from "./TabNavigation";
import debounce from "lodash/debounce";
import PopularSongListSection from "./PopularSongListSection";
import { useMode } from "../../utils/useMode";

const HomePage2 = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const initialTab = queryParams.get("activeTab") || "home";
  // const isJukebox = queryParams.has("jukebox");
  // const isOpenMic = queryParams.has("openmic");

  const [playlists, setPlaylists] = useState([]);
  const [artists, setArtists] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState(initialTab);
  const [sectionOffsets, setSectionOffsets] = useState([]);
  const [showPlaylists, setShowPlaylists] = useState(true);
  const [showPopularSongs, setShowPopularSongs] = useState(true);
  const [showArtists, setShowArtists] = useState(true);
  const [showAllSongs, setShowAllSongs] = useState(true);
  const [songs, setSongs] = useState([]);
  const [popularSongs, setPopularSongs] = useState([]);
  const [dataFetched, setDataFetched] = useState(false);

  const mode = useMode(); // "jukebox", "openmic", or "default"

  // Memoize all fetch functions to prevent re-creation on every render
  const fetchPlaylists = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlist`
      );
      const data = await response.json();

      // Filter playlists directly using songs_count
      const playlistsWithSongs = data.filter((p) => p.songs_count > 0);
      setPlaylists(playlistsWithSongs);
    } catch (error) {
      console.error("Failed to fetch playlists:", error);
      setError("Failed to fetch playlists");
    }
  }, []);

  const fetchArtists = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/songs/artists`
      );
      const data = await response.json();

      // Just filter directly using songs_count
      const artistsWithSongs = data.filter((artist) => artist.songs_count > 0);
      setArtists(artistsWithSongs);
    } catch (error) {
      console.error("Failed to fetch artists:", error);
      setError("Failed to fetch artists");
    }
  }, []);

  const fetchPopularSongs = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/songs/popular`
      );
      const data = await response.json();

      const uniqueSongs = Array.from(
        new Map(data.map((song) => [song.songs_master_id, song])).values()
      );

      setPopularSongs(uniqueSongs);
    } catch (error) {
      console.error("Failed to fetch popular songs:", error);
      setPopularSongs([]);
    }
  }, []);

  const fetchSongs = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/songs`
      );
      const data = await response.json();
      if (Array.isArray(data)) {
        setSongs(data);
      } else {
        setSongs([]);
      }
    } catch (error) {
      console.error("Failed to fetch songs:", error);
      setSongs([]);
    }
  }, []);

  // Single useEffect for initial data fetching with flag to prevent re-runs
  useEffect(() => {
    if (dataFetched) return; // Prevent re-fetching

    const fetchAllData = async () => {
      setLoading(true);
      try {
        // Run all fetch operations in parallel for better performance
        await Promise.all([
          fetchPlaylists(),
          fetchArtists(),
          fetchSongs(),
          fetchPopularSongs(),
        ]);
        setDataFetched(true); // Mark as fetched
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, [
    fetchPlaylists,
    fetchArtists,
    fetchSongs,
    fetchPopularSongs,
    dataFetched,
  ]);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const initialTab = queryParams.get("activeTab") || "home";
    setActiveTab(initialTab);
    scroller.scrollTo(initialTab, {
      duration: 500,
      smooth: true,
      offset: -80,
    });
  }, [location.search]);

  useEffect(() => {
    const calculateOffsets = () => {
      const sections = [
        { id: "home", offset: document.getElementById("home")?.offsetTop || 0 },
        {
          id: "playlists",
          offset: document.getElementById("playlists")?.offsetTop || 0,
        },
        {
          id: "popularSongs",
          offset: document.getElementById("popularSongs")?.offsetTop || 0,
        },
        {
          id: "artists",
          offset: document.getElementById("artists")?.offsetTop || 0,
        },
        {
          id: "songs",
          offset: document.getElementById("songs")?.offsetTop || 0,
        },
      ];
      setSectionOffsets(sections);
    };

    // Only calculate offsets if playlists are loaded
    if (playlists.length > 0) {
      calculateOffsets();
      window.addEventListener("resize", calculateOffsets);
      return () => {
        window.removeEventListener("resize", calculateOffsets);
      };
    }
  }, [playlists]);

  // Separate useEffect for scroll handling
  useEffect(() => {
    if (sectionOffsets.length === 0) return;

    const handleScroll = debounce(() => {
      const scrollPosition = window.scrollY + 80;

      for (let i = 0; i < sectionOffsets.length; i++) {
        const current = sectionOffsets[i];
        const next = sectionOffsets[i + 1];

        if (
          scrollPosition >= current.offset &&
          (!next || scrollPosition < next.offset)
        ) {
          const newTab = current.id;
          if (activeTab !== newTab) {
            setActiveTab(newTab);
          }
          break;
        }
      }
    }, 10);

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [sectionOffsets, activeTab]);

  const handleTabClick = (tab) => {
    const sectionId = tab;
    setActiveTab(tab);
    scroller.scrollTo(sectionId, {
      duration: 500,
      smooth: true,
      offset: -80,
    });
  };

  const handlePlaylistSelect = (playlistId) => {
    navigate(`/playlist/${playlistId}`);
  };

  const handleSelectSongByArtist = (artistID) => {
    navigate(`/artist/${artistID}`);
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Box sx={{ textAlign: "center", mt: 4 }}>{error}</Box>;
  }

  return (
    // <Box sx={{ bgcolor: "red" }}>
    <Box sx={{ bgcolor: "background.default" }}>
      <NavigationBar handleTabClick={handleTabClick} />
      <Element name="home" id="home">
        <Box
          sx={{
            px: 4,
            mb: { md: 8, sx: 0 },
            paddingTop: { xs: "130px", md: "130px" },
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            alignItems: "center",
            width: "100%",
          }}
        >
          <Box
            sx={{
              width: { md: "50%" },
              borderRadius: "5px",
              padding: "10px 20px 20px 20px",
            }}
          >
            {mode === "jukebox" ? (
              <img
                src="./jukebox_logo.png"
                alt="Iron Mountain Logo"
                style={{
                  width: "320px",
                  height: "auto",
                  zIndex: 2,
                  borderRadius: "5px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginInline: "auto",
                }}
              />
            ) : mode === "openmic" ? (
              <img
                src="./openmic_logo.png"
                alt="Iron Mountain Logo"
                style={{
                  width: "320px",
                  height: "auto",
                  zIndex: 2,
                  borderRadius: "5px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginInline: "auto",
                }}
              />
            ) : (
              <img
                src="./assets/HomePageBackground.png"
                alt="Iron Mountain Logo"
                style={{
                  width: "320px",
                  height: "auto",
                  zIndex: 2,
                  borderRadius: "5px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginInline: "auto",
                }}
              />
            )}
          </Box>
          <Box
            sx={{
              position: "relative",
              zIndex: 0,
              widht: "100%",
            }}
          >
            <Typography
              variant="h3"
              sx={{
                mb: 2,
                color: "white",
                fontSize: { xs: "2rem", md: "3rem" },
                fontWeight: "700",
                lineHeight: { xs: "38px", md: "57.6px" },
                textShadow: "2px 2px 4px rgba(0, 0, 0, 0.7)",
                fontFamily: "Barlow Semi Condensed",
                width: "fit-content",
              }}
            >
              {/* {mode === "jukebox"
                ? "Welcome To The Live Music Juke Box"
                : mode === "openmic"
                  ? "Welcome To Open Mic Hour"
                  : "Welcome to Iron Mountain Music"} */}
              {mode === "jukebox" || mode === "openmic"
                ? ""
                : "Welcome to Iron Mountain Music"}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                mb: 4,
                color: "#D6D6D6",
                fontSize: { xs: "1rem", md: "1.25rem" },
                fontWeight: "400",
                opacity: 0.9,
                fontFamily: "Nunito",
                lineHeight: { xs: "23.28px", md: "27.38px" },
              }}
            >
              {mode === "jukebox"
                ? `Make the band play your way. Request the songs you love, hear them played live, and own the soundtrack to your night.`
                : mode === "openmic"
                ? "Pick It. Sing It. Own It. Choose the songs you love with the Live Music Jukebox—then take the mic yourself during Open Mic Hour."
                : `Browse our collection of ${
                    songs.length > 0 ? songs.length : "..."
                  } songs from various artists and genres.`}
            </Typography>
          </Box>
        </Box>
      </Element>
      <Element name="playlists" id="playlists">
        <Box
          sx={{
            position: "sticky",
            top: 90,
            zIndex: 1,
            backgroundColor: "background.default",
            py: 2,
            marginInline: "auto",
            width: "98% !important",
            px: 2,
            display: showPlaylists ? "block" : "none",
          }}
        ></Box>
        <Box
          sx={{
            px: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            onClick={() => setShowPlaylists((prev) => !prev)}
            sx={{
              position: "sticky",
              top: 110,
              zIndex: 1,
              backgroundColor: "background.default",
              py: showPlaylists ? 1 : 0.5,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "24px",
              marginBottom: showPlaylists ? "32px" : "8px",
              width: "100% !important",
              border: "1px solid #966300",
              paddingLeft: "15px",
              borderRadius: "5px",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "24px",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                style={{ flexShrink: 0 }}
              >
                <path
                  fill="none"
                  stroke="#966300"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                />
              </svg>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "20px",
                  fontWeight: "bold",
                  color: "#FFF",
                  lineHeight: "1.2",
                }}
              >
                Playlists{" "}
                {playlists.length !== null ? `(${playlists.length})` : ""}
              </Typography>
            </Box>

            <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
              {showPlaylists ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          <Collapse in={showPlaylists} timeout="auto" unmountOnExit>
            <PlaylistSection
              playlists={playlists}
              onPlaylistSelect={handlePlaylistSelect}
            />
          </Collapse>
        </Box>
      </Element>
      <Element name="popularSongs" id="popularSongs">
        <Box
          sx={{
            position: "sticky",
            top: 90,
            zIndex: 1,
            backgroundColor: "background.default",
            py: 2,
            display: "block",
            marginInline: "auto",
            width: "98% !important",
            px: 2,
          }}
        ></Box>
        <Box
          sx={{
            px: 2,
            mb: 0,
            marginTop: showPopularSongs ? "2rem" : "0rem",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            onClick={() => setShowPopularSongs((prev) => !prev)}
            sx={{
              position: "sticky",
              top: 110,
              zIndex: 1,
              backgroundColor: "background.default",
              py: showPopularSongs ? 1 : 0.5,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "24px",
              marginBottom: showPopularSongs ? "32px" : "8px",
              width: "100% !important",
              border: "1px solid #966300",
              paddingLeft: "15px",
              borderRadius: "5px",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "24px",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                style={{ flexShrink: 0 }}
              >
                <path
                  fill="none"
                  stroke="#966300"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                />
              </svg>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "20px",
                  fontWeight: "bold",
                  color: "#FFF",
                  lineHeight: "1.2",
                }}
              >
                Popular Songs{" "}
                {popularSongs.length > 0 ? `(${popularSongs.length})` : ""}
              </Typography>
            </Box>
            <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
              {showPopularSongs ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          <Collapse in={showPopularSongs} timeout="auto" unmountOnExit>
            <PopularSongListSection songs={popularSongs} />
          </Collapse>
        </Box>
      </Element>
      <Element name="artists" id="artists">
        <Box
          sx={{
            position: "sticky",
            top: 90,
            zIndex: 1,
            backgroundColor: "background.default",
            py: 2,
            display: "block",
            marginInline: "auto",
            width: "98% !important",
            px: 2,
          }}
        ></Box>{" "}
        {/* New Artists Section */}
        <Box
          sx={{
            px: 2,
            mb: 3,
            marginTop: showArtists ? "2rem" : "0rem",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            onClick={() => setShowArtists((prev) => !prev)}
            sx={{
              position: "sticky",
              top: 110,
              zIndex: 1,
              backgroundColor: "background.default",
              py: showArtists ? 1 : 0.5,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "24px",
              marginBottom: showArtists ? "32px" : "8px",
              width: "100% !important",
              border: "1px solid #966300",
              paddingLeft: "15px",
              borderRadius: "5px",
              marginInline: "auto",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "24px",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                style={{ flexShrink: 0 }}
              >
                <path
                  fill="none"
                  stroke="#966300"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                />
              </svg>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "20px",
                  fontWeight: "bold",
                  color: "#FFF",
                  lineHeight: "1.2",
                }}
              >
                Artists {artists.length !== null ? `(${artists.length})` : ""}
              </Typography>
            </Box>
            <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
              {showArtists ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          <Collapse in={showArtists} timeout="auto" unmountOnExit>
            <ArtistListSection
              artists={artists}
              onArtistSelect={handleSelectSongByArtist}
            />{" "}
          </Collapse>
          {/* New component for artists */}
        </Box>
      </Element>
      <Element name="songs" id="songs">
        <Box
          sx={{
            position: "sticky",
            top: 90,
            zIndex: 1,
            backgroundColor: "background.default",
            py: 2,
            marginInline: "auto",
            width: "98% !important",
            px: 2,
            display: showAllSongs ? "block" : "none",
          }}
        ></Box>
        <Box
          sx={{
            px: 2,
            mb: 3,
            marginTop: showAllSongs ? "2rem" : "0rem",
            display: "flex",
            flexDirection: "column",
            marginBottom: "5rem",
          }}
        >
          <Box
            onClick={() => setShowAllSongs((prev) => !prev)}
            sx={{
              position: "sticky",
              top: 110,
              zIndex: 1,
              backgroundColor: "background.default",
              py: showAllSongs ? 1 : 0.5,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "24px",
              marginBottom: showAllSongs ? "32px" : "35px",
              width: "100% !important",
              border: "1px solid #966300",
              paddingLeft: "15px",
              borderRadius: "5px",
              marginInline: "auto",
              cursor: "pointer",
              transition: "all 0.3s ease",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "24px",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                style={{ flexShrink: 0 }}
              >
                <path
                  fill="none"
                  stroke="#966300"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                />
              </svg>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "20px",
                  fontWeight: "bold",
                  color: "#FFF",
                  lineHeight: "1.2",
                }}
              >
                All Songs {songs.length > 0 ? `(${songs.length})` : ""}
              </Typography>
            </Box>
            <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
              {showAllSongs ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          <Collapse in={showAllSongs} timeout="auto" unmountOnExit>
            <SongListSection songs={songs} />
          </Collapse>
        </Box>
      </Element>
      <TabNavigation activeTab={activeTab} handleTabClick={handleTabClick} />
    </Box>
  );
};

export default HomePage2;
