import React from "react";
import { <PERSON>, Typo<PERSON>, <PERSON>con<PERSON><PERSON><PERSON>, Button } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import { useSnackbar } from "notistack";
import axios from "axios";

const TrackResults = ({
  tracks,
  selectedTracks,
  onSelectionChange,
  originalSong,
  onSave,
  existingSpotifyTracks = [],
  setExistingSpotifyTracks,
}) => {
  const { enqueueSnackbar } = useSnackbar();

  const toggleTrackSelection = (track) => {
    const isSelected = selectedTracks.some((t) => t.id === track.id);
    if (isSelected) {
      onSelectionChange(selectedTracks.filter((t) => t.id !== track.id));
    } else {
      onSelectionChange([...selectedTracks, track]);
    }
  };

  const handleAddSelectedTracks = async () => {
    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL}/api/import/spotify/add-spotify-tracks`,
        {
          import_song_id: originalSong.id,
          tracks: selectedTracks,
        }
      );
      enqueueSnackbar("✅ Tracks added to temp table!", {
        variant: "success",
      });
      const newSavedTracks = selectedTracks.map((t) => ({
        spotify_track_id: t.id,
        name: t.name,
      }));
      setExistingSpotifyTracks((prev) => [...prev, ...newSavedTracks]);
      onSelectionChange([]);
      onSave();
    } catch (err) {
      console.error("Error saving tracks:", err);
      enqueueSnackbar("❌ Failed to add tracks", {
        variant: "error",
      });
    }
  };

  return (
    <>
      <Typography variant="h6" gutterBottom>
        🎵 Matching Tracks
      </Typography>
      {tracks.length ? (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {tracks.map((track, i) => {
            const isSelected = selectedTracks.some((t) => t.id === track.id);
            const isAlreadySaved = existingSpotifyTracks.some(
              (saved) => saved.spotify_track_id === track.id
            );
            return (
              <Box
                key={i}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  p: 2,
                  borderRadius: 2,
                  border: isAlreadySaved
                    ? "2px solid #aaa"
                    : isSelected
                    ? "2px solid #388e3c"
                    : "1px solid #ccc",
                  bgcolor: isAlreadySaved ? "#333" : "#000",
                  opacity: isAlreadySaved ? 0.5 : 1,
                }}
              >
                <IconButton
                  color={
                    isAlreadySaved
                      ? "inherit"
                      : isSelected
                      ? "success"
                      : "primary"
                  }
                  disabled={isAlreadySaved}
                  onClick={() => toggleTrackSelection(track)}
                >
                  {isAlreadySaved ? <CheckIcon /> : <AddIcon />}
                </IconButton>
                {track.image && (
                  <Box
                    component="img"
                    src={track.image}
                    alt="album-cover"
                    sx={{ width: 80, height: 80, borderRadius: 1 }}
                  />
                )}
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {track.name}
                  </Typography>
                  <Typography variant="body2">
                    Artist: {track.artist}
                  </Typography>
                  <Typography variant="body2">
                    Album: {track.album} ({track.release_year}) | Duration:{" "}
                    {track.duration} | Decade: {track.decade}
                  </Typography>
                </Box>
              </Box>
            );
          })}
          <Button
            variant="contained"
            color="success"
            sx={{ mt: 2, alignSelf: "flex-start" }}
            disabled={selectedTracks.length === 0}
            onClick={handleAddSelectedTracks}
          >
            Add Selected Tracks
          </Button>
        </Box>
      ) : (
        <Typography color="text.secondary">No track results found.</Typography>
      )}
    </>
  );
};

export default TrackResults;
