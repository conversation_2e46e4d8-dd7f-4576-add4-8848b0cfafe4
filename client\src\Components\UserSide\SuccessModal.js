import React from "react";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  DialogTitle,
  IconButton,
  Box,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { useSongCart } from "../../context/SongCartContext";

const SuccessModal = ({ open, onClose, limitReached, requesterName }) => {
  const { clearCart } = useSongCart();

  const handleClose = () => {
    clearCart();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      slotProps={{
        backdrop: {
          sx: {
            backdropFilter: "blur(8px)",
            backgroundColor: "rgba(0, 0, 0, 0.4)",
          },
        },
      }}
      sx={{ px: 2, border: "1px solid" }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#000000",
          color: "#FFFFFF",
          fontFamily: "Nunito",
          fontSize: "32px",
          fontWeight: 700,
          lineHeight: "40px",
          letterSpacing: "0.2px",
          mb: 1,
        }}
      >
        {limitReached ? "Request Limit Reached" : "Request Successful"}
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            color: "#FFFFFF",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          backgroundColor: "#000000",
          color: "#FFFFFF",
          padding: "24px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
          gap: 2,
        }}
      >
        {limitReached ? (
          <Typography
            sx={{
              fontWeight: 600,
              fontSize: "16px",
              lineHeight: "24px",
              letterSpacing: "0.2px",
              marginTop: "15px",
            }}
          >
            You've reached the maximum of 10 requests per hour. Please wait
            before making more requests.
          </Typography>
        ) : (
          <>
            <CheckCircleIcon sx={{ fontSize: 50, color: "#16a34a", mt: 2 }} />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                fontSize: "24px",
                lineHeight: "28px",
                letterSpacing: "0.2px",
                mb: 1,
              }}
            >
              Request Sent
            </Typography>
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: "16px",
                lineHeight: "24px",
                letterSpacing: "0.2px",
              }}
            >
              Thank you{" "}
              <span style={{ color: "#976609" }}>{requesterName}</span>, your
              song request has been sent successfully!
              <br />
              Enjoy your music!
              {/* <br />
              If you want, you can make another song request! */}
            </Typography>
          </>
        )}

        <Box sx={{ width: "100%", mt: 3, display: "flex", gap: 1 }}>
          <Button
            onClick={handleClose}
            variant="contained"
            sx={{
              flexGrow: 1,
              backgroundColor: "#976609",
              color: "#FFFFFF",
              fontWeight: 600,
              fontSize: "16px",
              lineHeight: "24px",
              letterSpacing: "0.2px",
              borderRadius: "8px",
              py: 1.5,
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#b5840f",
              },
            }}
          >
            Close
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessModal;
