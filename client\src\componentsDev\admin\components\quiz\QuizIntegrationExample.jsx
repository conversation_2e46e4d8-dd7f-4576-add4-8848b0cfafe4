import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper
} from '@mui/material';
import {
  Quiz as QuizIcon,
  CheckCircle as CheckIcon,
  Code as CodeIcon,
  // Integration as IntegrationIcon,
  PlayArrow as PlayIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { 
  QuizDashboard, 
  QuizQuestionForm, 
  QuizQuestionManagement,
  useQuizQuestions,
  QUIZ_QUESTION_TYPES,
  formatTimeLimit
} from './index';

// Styled components
const ExampleContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#121212',
  minHeight: '100vh',
  padding: '24px',
}));

const CodeBlock = styled(Paper)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '8px',
  padding: '16px',
  fontFamily: '<PERSON>, <PERSON>sol<PERSON>, "Courier New", monospace',
  fontSize: '14px',
  color: '#fff',
  overflow: 'auto',
  '& pre': {
    margin: 0,
    whiteSpace: 'pre-wrap',
  },
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  height: '100%',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: '#4CAF50',
    transform: 'translateY(-2px)',
  },
}));

const QuizIntegrationExample = () => {
  const [activeExample, setActiveExample] = useState('dashboard');
  const { data: questions = [], isLoading } = useQuizQuestions();

  const examples = {
    dashboard: {
      title: 'Full Dashboard Integration',
      description: 'Complete quiz management dashboard with all features',
      component: <QuizDashboard />,
      code: `import { QuizDashboard } from './components/quiz';

function AdminPanel() {
  return (
    <div>
      <h1>Admin Dashboard</h1>
      <QuizDashboard />
    </div>
  );
}`
    },
    form: {
      title: 'Standalone Form',
      description: 'Quiz question creation form as a standalone component',
      component: (
        <QuizQuestionForm 
          onSuccess={(question) => {
            console.log('Question created:', question);
            alert('Question created successfully!');
          }}
        />
      ),
      code: `import { QuizQuestionForm } from './components/quiz';

function CreateQuestionPage() {
  const handleSuccess = (newQuestion) => {
    console.log('Question created:', newQuestion);
    // Redirect or show success message
    navigate('/admin/quiz/questions');
  };

  return (
    <QuizQuestionForm 
      onSuccess={handleSuccess}
      editingQuestion={null}
    />
  );
}`
    },
    management: {
      title: 'Question Management',
      description: 'List and manage existing quiz questions',
      component: <QuizQuestionManagement />,
      code: `import { QuizQuestionManagement } from './components/quiz';

function ManageQuestionsPage() {
  return (
    <div>
      <h1>Manage Quiz Questions</h1>
      <QuizQuestionManagement />
    </div>
  );
}`
    },
    hooks: {
      title: 'Using Hooks',
      description: 'Custom implementation using quiz hooks',
      component: <HooksExample />,
      code: `import { useQuizQuestions, useCreateQuizQuestion } from './components/quiz';

function CustomQuizComponent() {
  const { data: questions, isLoading } = useQuizQuestions();
  const createMutation = useCreateQuizQuestion();

  const handleCreate = async (questionData) => {
    try {
      await createMutation.mutateAsync(questionData);
      alert('Question created!');
    } catch (error) {
      alert('Error creating question');
    }
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <h2>{questions.length} Questions</h2>
      {/* Your custom UI here */}
    </div>
  );
}`
    }
  };

  return (
    <ExampleContainer>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 2 }}>
          Quiz Components Integration Examples
        </Typography>
        <Typography variant="h6" sx={{ color: '#ccc' }}>
          Learn how to integrate the quiz question management system into your application
        </Typography>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <FeatureCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <QuizIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                {questions.length}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Total Questions
              </Typography>
            </CardContent>
          </FeatureCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <FeatureCard>
            <CardContent sx={{ textAlign: 'center' }}>
              {/* <IntegrationIcon sx={{ fontSize: 40, color: '#2196F3', mb: 1 }} /> */}
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                4
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Components
              </Typography>
            </CardContent>
          </FeatureCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <FeatureCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <CodeIcon sx={{ fontSize: 40, color: '#FF9800', mb: 1 }} />
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                12
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                API Hooks
              </Typography>
            </CardContent>
          </FeatureCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <FeatureCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <SettingsIcon sx={{ fontSize: 40, color: '#9C27B0', mb: 1 }} />
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                {QUIZ_QUESTION_TYPES.length}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Question Types
              </Typography>
            </CardContent>
          </FeatureCard>
        </Grid>
      </Grid>

      {/* Integration Guide */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <FeatureCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckIcon sx={{ color: '#4CAF50' }} />
                Quick Setup Steps
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <Chip label="1" size="small" sx={{ backgroundColor: '#4CAF50', color: '#fff' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Install Dependencies" 
                    secondary="Material-UI, React Query, Notistack"
                    sx={{ '& .MuiListItemText-primary': { color: '#fff' }, '& .MuiListItemText-secondary': { color: '#ccc' } }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Chip label="2" size="small" sx={{ backgroundColor: '#2196F3', color: '#fff' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Import Components" 
                    secondary="Choose from Dashboard, Form, or Management"
                    sx={{ '& .MuiListItemText-primary': { color: '#fff' }, '& .MuiListItemText-secondary': { color: '#ccc' } }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Chip label="3" size="small" sx={{ backgroundColor: '#FF9800', color: '#fff' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Configure API" 
                    secondary="Set REACT_APP_API_URL environment variable"
                    sx={{ '& .MuiListItemText-primary': { color: '#fff' }, '& .MuiListItemText-secondary': { color: '#ccc' } }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Chip label="4" size="small" sx={{ backgroundColor: '#9C27B0', color: '#fff' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Start Creating" 
                    secondary="Begin adding quiz questions to your app"
                    sx={{ '& .MuiListItemText-primary': { color: '#fff' }, '& .MuiListItemText-secondary': { color: '#ccc' } }}
                  />
                </ListItem>
              </List>
            </CardContent>
          </FeatureCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <FeatureCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                Available Question Types
              </Typography>
              {QUIZ_QUESTION_TYPES.map((type, index) => (
                <Box key={type.value} mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body1" sx={{ color: '#fff', fontWeight: 600 }}>
                      {type.label}
                    </Typography>
                    <Chip 
                      label={`${type.minAnswers}-${type.maxAnswers} answers`} 
                      size="small" 
                      sx={{ backgroundColor: '#333', color: '#ccc' }}
                    />
                  </Box>
                  <Typography variant="body2" sx={{ color: '#ccc', mb: 1 }}>
                    {type.value === 'multiple_choice' && 'Allow multiple correct answers with 2-6 options'}
                    {type.value === 'true_false' && 'Simple true or false questions'}
                    {type.value === 'fill_blank' && 'Text input for exact answer matching'}
                  </Typography>
                  {index < QUIZ_QUESTION_TYPES.length - 1 && <Divider sx={{ borderColor: '#333' }} />}
                </Box>
              ))}
            </CardContent>
          </FeatureCard>
        </Grid>
      </Grid>

      {/* Example Selector */}
      <Box mb={3}>
        <Typography variant="h5" sx={{ color: '#fff', mb: 2, fontWeight: 600 }}>
          Live Examples
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          {Object.entries(examples).map(([key, example]) => (
            <Button
              key={key}
              variant={activeExample === key ? 'contained' : 'outlined'}
              onClick={() => setActiveExample(key)}
              startIcon={<PlayIcon />}
              sx={{
                borderRadius: '8px',
                textTransform: 'none',
                ...(activeExample === key ? {
                  backgroundColor: '#4CAF50',
                  color: '#fff',
                } : {
                  borderColor: '#333',
                  color: '#ccc',
                  '&:hover': {
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                  },
                }),
              }}
            >
              {example.title}
            </Button>
          ))}
        </Box>
      </Box>

      {/* Active Example */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <FeatureCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 1 }}>
                {examples[activeExample].title}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc', mb: 3 }}>
                {examples[activeExample].description}
              </Typography>
              <Box sx={{ 
                border: '1px solid #333', 
                borderRadius: '8px', 
                overflow: 'hidden',
                backgroundColor: '#121212'
              }}>
                {examples[activeExample].component}
              </Box>
            </CardContent>
          </FeatureCard>
        </Grid>

        <Grid item xs={12} lg={4}>
          <FeatureCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <CodeIcon sx={{ color: '#4CAF50' }} />
                Implementation Code
              </Typography>
              <CodeBlock>
                <pre>{examples[activeExample].code}</pre>
              </CodeBlock>
            </CardContent>
          </FeatureCard>
        </Grid>
      </Grid>

      {/* Footer */}
      <Box mt={4} textAlign="center">
        <Alert 
          severity="info" 
          sx={{ 
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            border: '1px solid #2196F3',
            '& .MuiAlert-message': { color: '#fff' }
          }}
        >
          <Typography variant="body1">
            <strong>Ready to integrate?</strong> Check out the README.md file for detailed documentation and best practices.
          </Typography>
        </Alert>
      </Box>
    </ExampleContainer>
  );
};

// Example component using hooks
function HooksExample() {
  const { data: questions = [], isLoading } = useQuizQuestions();

  if (isLoading) {
    return (
      <Box p={3} textAlign="center">
        <Typography sx={{ color: '#ccc' }}>Loading questions...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
        Custom Quiz Questions Display
      </Typography>
      <Typography variant="body2" sx={{ color: '#ccc', mb: 3 }}>
        This example shows how to use the useQuizQuestions hook to build custom UI
      </Typography>
      
      {questions.length === 0 ? (
        <Alert severity="info" sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)' }}>
          No questions available. Create some questions first!
        </Alert>
      ) : (
        <Grid container spacing={2}>
          {questions.slice(0, 3).map((question) => (
            <Grid item xs={12} md={4} key={question.question_id}>
              <Card sx={{ backgroundColor: '#1A1A1A', border: '1px solid #333' }}>
                <CardContent>
                  <Typography variant="body1" sx={{ color: '#fff', mb: 1 }}>
                    {question.question_text?.substring(0, 50)}...
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Chip 
                      label={question.level_name} 
                      size="small" 
                      sx={{ backgroundColor: '#4CAF50', color: '#fff' }}
                    />
                    <Chip 
                      label={formatTimeLimit(question.time_limit_seconds)} 
                      size="small" 
                      sx={{ backgroundColor: '#2196F3', color: '#fff' }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
}

export default QuizIntegrationExample;
