import React, { useState, useEffect } from "react";
import {
  <PERSON>po<PERSON>,
  Grid,
  Card,
  CardContent,
  Container,
  Box,
  Button,
  CircularProgress,
  Avatar,
} from "@mui/material";

const INITIAL_ARTIST_COUNT = 20;
const ARTISTS_PER_PAGE = 10;

const ArtistListSection = ({ artists, onArtistSelect }) => {
  const [visibleArtists, setVisibleArtists] = useState([]);
  const [visibleCount, setVisibleCount] = useState(INITIAL_ARTIST_COUNT);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    setVisibleArtists(artists.slice(0, INITIAL_ARTIST_COUNT));
    setVisibleCount(INITIAL_ARTIST_COUNT);
  }, [artists]);

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      const newCount = visibleCount + ARTISTS_PER_PAGE;
      setVisibleArtists(artists.slice(0, newCount));
      setVisibleCount(newCount);
      setLoadingMore(false);
    }, 800);
  };

  const hasMore = visibleArtists.length < artists.length;

  return (
    <Container
      maxWidth="xl"
      sx={{
        mt: 0,
        bgcolor: "background.default",
        py: 2,
      }}
    >
      <Grid container spacing={2} alignItems="stretch">
        {visibleArtists.map((artist) => (
          <Grid
            item
            key={artist.artist_id}
            xs={6}
            sm={4}
            md={3}
            lg={2}
            sx={{
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Card
              onClick={() => onArtistSelect(artist.artist_id)}
              sx={{
                background: "none",
                color: "#fff",
                cursor: "pointer",
                width: "100%",
                maxWidth: 180,
                transition: "0.3s",
                "&:hover": {
                  transform: "scale(1.03)",
                  boxShadow: "0px 4px 12px rgba(0,0,0,0.3)",
                },
              }}
            >
              <CardContent
                sx={{
                  textAlign: "center",
                  py: 2,
                }}
              >
                {artist.image ? (
                  <Avatar
                    src={artist.image}
                    alt={artist.artistName}
                    sx={{
                      width: 100,
                      height: 100,
                      margin: "0 auto 10px",
                      border: "2px solid white",
                    }}
                  />
                ) : (
                  <Avatar
                    sx={{
                      width: 100,
                      height: 100,
                      margin: "0 auto 10px",
                      bgcolor: "#444",
                      color: "#fff",
                    }}
                  >
                    {artist.artistName?.[0]?.toUpperCase() || "A"}
                  </Avatar>
                )}
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: { xs: "1rem", md: "1.1rem" },
                    fontWeight: 500,
                  }}
                >
                  {artist.artistName}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {hasMore && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 1, mb: 1 }}>
          <Button
            variant="contained"
            onClick={handleLoadMore}
            disabled={loadingMore}
            sx={{
              background: "linear-gradient(45deg, #ffa040, #ff8f00)",
              color: "#fff",
              fontWeight: "bold",
              fontSize: "1rem",
              px: 4,
              py: 1,
              borderRadius: "2rem",
              boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
              transition: "all 0.3s ease-in-out",
              "&:hover": {
                background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                transform: "scale(1.05)",
              },
            }}
          >
            {loadingMore ? (
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CircularProgress size={20} thickness={5} color="inherit" />
                <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                  Loading...
                </Typography>
              </Box>
            ) : (
              "Show More Artists"
            )}
          </Button>
        </Box>
      )}
    </Container>
  );
};

export default ArtistListSection;
