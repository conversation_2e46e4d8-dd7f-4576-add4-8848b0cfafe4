import { Box } from '@mui/material'
import React, { useEffect, useState } from 'react'
import Navbar from '../navigation/Navbar'
import { useLocation } from 'react-router-dom';
import { scroller } from 'react-scroll';
import HomeMaster from '../home/<USER>';

const Home = () => {

    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const initialTab = queryParams.get("activeTab") || "home";
    const [activeTab, setActiveTab] = useState(initialTab);


    const handleTabClick = (tab) => {
        const sectionId = tab;
        setActiveTab(tab);
        scroller.scrollTo(sectionId, {
            duration: 500,
            smooth: true,
            offset: -80,
        });
    };


    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const initialTab = queryParams.get("activeTab") || "home";
        setActiveTab(initialTab);
        scroller.scrollTo(initialTab, {
            duration: 500,
            smooth: true,
            offset: -80,
        });
    }, [location.search]);


    return (
        <Box sx={{ bgcolor: "background.default" }}>
            <Navbar handleTabClick={handleTabClick} />
            <HomeMaster />
        </Box>
    )
}

export default Home