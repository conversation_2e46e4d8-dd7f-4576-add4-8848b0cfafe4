import { useState, useEffect, useCallback, useRef } from "react";
import {
    Box,
    Typography,
    Tabs,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    Chip,
    Button,
    CircularProgress,
    useMediaQuery,
    useTheme,
    Tooltip,
} from "@mui/material";
import {
    CheckCircle,
    Close,
    MusicNote,
    Person,
    Message,
    AccessTime,
    RadioButtonUnchecked,
    TimerOff,
    YouTube,
} from "@mui/icons-material";
import { timeSince } from "../../utils/timeSince";
import Header from "./Header";
import io from "socket.io-client";

// Status colors and config
const statusConfig = {
    "Song-Q": {
        color: "#FFA726", // Orange
        icon: <AccessTime color="warning" />,
    },
    Played: {
        color: "#4CAF50", // Green
        icon: <CheckCircle color="success" />,
    },
    "Not Played": {
        color: "#F44336", // Red
        icon: <TimerOff color="error" />,
    },
};

// Action type colors
const actionTypeColors = {
    singForMe: "#2196F3", // Blue
    singWithYou: "#4CAF50", // Green
    singAlone: "#9C27B0", // Purple
};

const COOLDOWN_MS = 60 * 60 * 1000; // 1 hour

const AdminRequests = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
    const [selectedTab, setSelectedTab] = useState("Song-Q");
    const [requests, setRequests] = useState([]);
    const [selectedSong, setSelectedSong] = useState(null);
    const [openModal, setOpenModal] = useState(false);
    const [loading, setLoading] = useState(true);
    const [currentTime, setCurrentTime] = useState(Date.now());

    const socketRef = useRef(null);
    const isInitialMount = useRef(true);

    const getRequestStatus = useCallback((request) => {
        const now = new Date();
        const expiresAt = new Date(request.expires_at);
        if (request.status === "Fulfilled") return "Played";
        if (now >= expiresAt) return "Not Played";
        return "Song-Q";
    }, []);

    const fetchRequests = useCallback(async () => {
        setLoading(true);
        try {
            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests`,
                {
                    headers: {
                        'Cache-Control': 'no-cache'
                    }
                }
            );
            const data = await response.json();

            if (response.ok) {
                const requestsWithSongs = await Promise.all(
                    data.map(async (req) => {
                        try {
                            const songRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/songs/${req.songs_master_id}`
                            );
                            const songData = await songRes.json();

                            // Fetch URLs and files count
                            const urlsRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${req.songs_master_id}`
                            );
                            const urlsData = await urlsRes.json();

                            const filesRes = await fetch(
                                `${process.env.REACT_APP_API_URL}/api/song-files/song/${req.songs_master_id}`
                            );
                            const filesData = await filesRes.json();

                            // Filter for YouTube URLs specifically
                            const youtubeLinks = urlsData.filter((url) =>
                                url.song_url && (
                                    url.song_url.includes('youtube.com') ||
                                    url.song_url.includes('youtu.be') ||
                                    (url.url_type_name && url.url_type_name.toLowerCase().includes('youtube'))
                                ));

                            const validFiles = filesData.filter((file) => file.file);

                            return {
                                ...req,
                                song_name: songData.name || "Unknown Song",
                                artist: songData.artist || "Unknown Artist",
                                displayStatus: getRequestStatus(req),
                                hasYoutubeLinks: youtubeLinks.length > 0,
                                youtubeCount: youtubeLinks.length,
                                hasFiles: validFiles.length > 0,
                                fileCount: validFiles.length,
                            };
                        } catch {
                            return {
                                ...req,
                                song_name: "Unknown Song",
                                artist: "Unknown Artist",
                                displayStatus: getRequestStatus(req),
                                hasYoutubeLinks: false,
                                youtubeCount: 0,
                                hasFiles: false,
                                fileCount: 0,
                            };
                        }
                    })
                );

                setRequests(requestsWithSongs);
            }
        } catch (error) {
            console.error("Error fetching requests:", error);
        } finally {
            setLoading(false);
        }
    }, [getRequestStatus]);

    useEffect(() => {
        // Initialize socket connection only once
        if (!socketRef.current) {
            socketRef.current = io(process.env.REACT_APP_API_URL, {
                reconnectionAttempts: 5,
                reconnectionDelay: 1000,
            });

            socketRef.current.on("new-song-request", (newRequest) => {
                setRequests(prev => {
                    const exists = prev.some(req => req.song_request_id === newRequest.song_request_id);
                    if (!exists) {
                        return [{
                            ...newRequest,
                            displayStatus: getRequestStatus(newRequest)
                        }, ...prev];
                    }
                    return prev;
                });
            });

            socketRef.current.on("song-request-updated", (updatedRequest) => {
                setRequests(prev => prev.map(req =>
                    req.song_request_id === updatedRequest.song_request_id ? {
                        ...updatedRequest,
                        displayStatus: getRequestStatus(updatedRequest)
                    } : req
                ));
            });
        }

        // Initial data fetch
        if (isInitialMount.current) {
            fetchRequests();
            isInitialMount.current = false;
        }

        // Update current time every minute for expiration checks
        const timeInterval = setInterval(() => {
            setCurrentTime(Date.now());
        }, 60000);

        // Update request statuses periodically (every 5 minutes)
        const statusUpdateInterval = setInterval(() => {
            setRequests(prev => prev.map(req => ({
                ...req,
                displayStatus: getRequestStatus(req)
            })));
        }, 5 * 60 * 1000);

        return () => {
            clearInterval(timeInterval);
            clearInterval(statusUpdateInterval);
            if (socketRef.current) {
                socketRef.current.disconnect();
                socketRef.current = null;
            }
        };
    }, [fetchRequests, getRequestStatus]);

    const handleStatusChange = async (requestId, songMasterId, currentStatus) => {
        const newStatus = currentStatus === "Song-Q" ? "Played" : "Song-Q";
        try {
            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests/${requestId}/song/${songMasterId}`,
                {
                    method: "PATCH",
                    headers: { "Content-Type": "application/json" }, 
                    body: JSON.stringify({
                        status: newStatus === "Played" ? "Fulfilled" : "Pending"
                    }),
                }
            );

            if (!response.ok) {
                throw new Error(await response.text());
            }

            const updatedRequest = await response.json();

            // Update local state immediately for better UX
            setRequests(prev => prev.map(req =>
                req.song_request_id === requestId ? {
                    ...req,
                    status: newStatus === "Played" ? "Fulfilled" : "Pending",
                    displayStatus: newStatus,
                    fulfilled_at: newStatus === "Played" ? new Date().toISOString() : null
                } : req
            ));

            // Notify other clients via socket
            if (socketRef.current) {
                socketRef.current.emit("song-request-updated", updatedRequest);
            }

        } catch (error) {
            console.error("Error updating status:", error);
        }
    };

    const handleOpenDetails = async (songId, request) => {
        try {
            const songRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/songs/${songId}`
            );
            const songData = await songRes.json();

            const urlsRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songId}`
            );
            const urlsData = await urlsRes.json();

            const filesRes = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-files/song/${songId}`
            );
            const filesData = await filesRes.json();

            setSelectedSong({
                ...songData,
                request: request || requests.find(req => req.songs_master_id === songId),
                urls: urlsData,
                files: filesData
            });
            setOpenModal(true);
        } catch (error) {
            console.error("Error loading song info:", error);
        }
    };

    // Filter requests by selected tab
    const filteredRequests = requests.filter((req) =>
        selectedTab === "All" || req.displayStatus === selectedTab
    );

    // Group requests by song
    const groupedRequests = filteredRequests.reduce((acc, req) => {
        const existing = acc.find(g => g.songs_master_id === req.songs_master_id);
        if (existing) {
            existing.count++;
            existing.requests.push(req);
        } else {
            acc.push({
                songs_master_id: req.songs_master_id,
                song_name: req.song_name,
                artist: req.artist,
                displayStatus: req.displayStatus,
                action_type: req.action_type,
                count: 1,
                requests: [req],
                hasYoutubeLinks: req.hasYoutubeLinks,
                youtubeCount: req.youtubeCount,
                hasFiles: req.hasFiles,
                fileCount: req.fileCount,
                created_at: req.created_at
            });
        }
        return acc;
    }, []);

    // Sort by most recent first
    groupedRequests.sort((a, b) =>
        new Date(b.requests[0].created_at) - new Date(a.requests[0].created_at)
    );

    return (
        <Box sx={{ p: isMobile ? 1 : 3, bgcolor: "#121212", minHeight: "100vh" }}>

            <Typography variant="h5" gutterBottom sx={{ color: "white", mb: 2 }}>
                Song Requests
            </Typography>

            <Tabs
                value={selectedTab}
                onChange={(e, newValue) => setSelectedTab(newValue)}
                sx={{
                    mb: 2,
                    "& .MuiTabs-indicator": {
                        backgroundColor: statusConfig[selectedTab]?.color || "#1db954",
                    }
                }}
            >
                <Tab label="Song-Q" value="Song-Q" sx={{ color: "white" }} />
                <Tab label="Played" value="Played" sx={{ color: "white" }} />
                <Tab label="Not Played" value="Not Played" sx={{ color: "white" }} />
                {/* <Tab label="All" value="All" sx={{ color: "white" }} /> */}
            </Tabs>

            {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
                    <CircularProgress />
                </Box>
            ) : groupedRequests.length === 0 ? (
                <Typography sx={{ color: "white", textAlign: "center", mt: 4 }}>
                    No requests found
                </Typography>
            ) : (
                <TableContainer component={Paper} sx={{ bgcolor: "#1e1e1e" }}>
                    <Table>
                        <TableHead>
                            <TableRow sx={{ bgcolor: "#2c2c2c" }}>
                                {/* <TableCell sx={{ color: "white", fontWeight: "bold" }}>Name</TableCell> */}
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Song</TableCell>
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Artist</TableCell>
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Action Type</TableCell>
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Requests</TableCell>
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Status</TableCell>
                                <TableCell sx={{ color: "white", fontWeight: "bold" }}>Action</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {groupedRequests.map((group, index) => {
                                const timePassed = currentTime - new Date(group.requests[0].created_at).getTime();
                                const timeLeft = COOLDOWN_MS - timePassed;
                                const isExpiringSoon = timeLeft > 0 && timeLeft < 30 * 60 * 1000;

                                return (
                                    <TableRow
                                        key={index}
                                        hover
                                        onClick={() => handleOpenDetails(group.songs_master_id, group)}
                                        sx={{
                                            cursor: "pointer",
                                            "&:hover": { bgcolor: "#282828" }
                                        }}
                                    >
                                        <TableCell sx={{ color: "white" }}>
                                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                <MusicNote sx={{ fontSize: 20 }} />
                                                {group.song_name}
                                            </Box>
                                        </TableCell>
                                        <TableCell sx={{ color: "white" }}>{group.artist}</TableCell>
                                        <TableCell>
                                            <Chip
                                                label={group.action_type || "N/A"}
                                                size="small"
                                                sx={{
                                                    bgcolor: actionTypeColors[group.action_type] || "#9e9e9e",
                                                    color: "white"
                                                }}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Chip
                                                label={group.count}
                                                color="primary"
                                                size="small"
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                <Chip
                                                    label={group.displayStatus}
                                                    size="small"
                                                    sx={{
                                                        bgcolor: statusConfig[group.displayStatus]?.color || "#9e9e9e",
                                                        color: "white"
                                                    }}
                                                />
                                                {group.displayStatus === "Song-Q" && timeLeft > 0 && (
                                                    <Tooltip title={`Expires in ${Math.ceil(timeLeft / (60 * 1000))} minutes`}>
                                                        <Chip
                                                            label={`${Math.ceil(timeLeft / (60 * 1000))}m`}
                                                            size="small"
                                                            color={isExpiringSoon ? "warning" : "default"}
                                                            variant="outlined"
                                                        />
                                                    </Tooltip>
                                                )}
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <IconButton
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleStatusChange(
                                                        group.requests[0].song_request_id,
                                                        group.songs_master_id,
                                                        group.displayStatus
                                                    );
                                                }}
                                                size="small"
                                                sx={{ color: statusConfig[group.displayStatus]?.color }}
                                            >
                                                {group.displayStatus === "Played" ? (
                                                    <CheckCircle />
                                                ) : (
                                                    <RadioButtonUnchecked />
                                                )}
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}

            {/* Details Dialog */}
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(false)}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        bgcolor: "#1e1e1e",
                        color: "white",
                    },
                }}
            >
                <DialogTitle sx={{ bgcolor: "#2c2c2c", display: "flex", justifyContent: "space-between" }}>
                    <Typography variant="h6">
                        {selectedSong?.name} {selectedSong?.release_year && `(${selectedSong.release_year})`}
                    </Typography>
                    <IconButton onClick={() => setOpenModal(false)} sx={{ color: "white" }}>
                        <Close />
                    </IconButton>
                </DialogTitle>

                <DialogContent dividers sx={{ bgcolor: "#121212" }}>
                    <Box mb={2}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Artist: <Typography component="span" color="text.secondary">{selectedSong?.artist || "N/A"}</Typography>
                        </Typography>
                    </Box>

                    <Divider sx={{ borderColor: "#333", my: 2 }} />

                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Requests ({selectedSong?.request?.requests?.length || 1})
                    </Typography>

                    {selectedSong?.request?.requests?.map((req, i) => (
                        <Box key={i} sx={{ mb: 2, p: 2, bgcolor: "#1a1a1a", borderRadius: 1 }}>
                            <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                    <Person fontSize="small" />
                                    <Typography>{req.requester_name}</Typography>
                                </Box>
                                <Chip
                                    label={req.action_type || "N/A"}
                                    size="small"
                                    sx={{
                                        bgcolor: actionTypeColors[req.action_type] || "#9e9e9e",
                                        color: "white"
                                    }}
                                />
                            </Box>

                            {req.dedication_msg && (
                                <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
                                    <Message fontSize="small" />
                                    <Typography sx={{ fontStyle: "italic" }}>
                                        "{req.dedication_msg}"
                                    </Typography>
                                </Box>
                            )}

                            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                <Typography variant="caption" color="text.secondary">
                                    Requested: {timeSince(req.created_at)}
                                </Typography>
                                {req.fulfilled_at && (
                                    <Typography variant="caption" color="#4caf50">
                                        Fulfilled: {timeSince(req.fulfilled_at)}
                                    </Typography>
                                )}
                            </Box>
                        </Box>
                    ))}

                    <Divider sx={{ borderColor: "#333", my: 2 }} />

                    {/* YouTube Links */}
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                            YouTube Links ({selectedSong?.urls?.length || 0})
                        </Typography>
                        {selectedSong?.urls?.length > 0 ? (
                            selectedSong.urls.map((url, i) => (
                                <Box key={i} sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                    <YouTube sx={{ color: "red" }} />
                                    <a
                                        href={url.song_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        style={{ color: "#1db954", wordBreak: "break-all" }}
                                    >
                                        {url.url_type_name || "YouTube Link"}
                                    </a>
                                </Box>
                            ))
                        ) : (
                            <Typography variant="body2" color="text.secondary">
                                No YouTube links available
                            </Typography>
                        )}
                    </Box>

                    {/* Song Files */}
                    <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                            Song Files ({selectedSong?.files?.length || 0})
                        </Typography>
                        {selectedSong?.files?.length > 0 ? (
                            selectedSong.files.map((file, i) => (
                                <Box key={i} sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                                    <MusicNote sx={{ color: "skyblue" }} />
                                    <a
                                        href={`${process.env.REACT_APP_API_URL}/${file.file}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        style={{ color: "#1db954", wordBreak: "break-all" }}
                                    >
                                        {file.file_type_name || file.file.split("\\").pop()}
                                    </a>
                                </Box>
                            ))
                        ) : (
                            <Typography variant="body2" color="text.secondary">
                                No song files available
                            </Typography>
                        )}
                    </Box>
                </DialogContent>

                <DialogActions sx={{ bgcolor: "#2c2c2c" }}>
                    <Button
                        onClick={() => setOpenModal(false)}
                        variant="contained"
                        sx={{ bgcolor: "#1db954", "&:hover": { bgcolor: "#1ed760" } }}
                    >
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default AdminRequests;