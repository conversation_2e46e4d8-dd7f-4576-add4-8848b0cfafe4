import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Box, Grid, IconButton, Typography, Avatar } from "@mui/material";
import SongCard from "./SongCard";
import RequestHandler from "./RequestHandler";
import ArrowCircleUpOutlinedIcon from "@mui/icons-material/ArrowCircleUpOutlined";
import NavigationBar from "./NavigationBar";
import TabNavigation from "./TabNavigation";
import { getDeviceId } from "../../utils/cookieUtils";
import { useSongCart } from "../../context/SongCartContext";

const ArtistSongsPage = () => {
  const navigate = useNavigate();
  const { artistID } = useParams();
  const [songs, setSongs] = useState([]);
  const [artist, setArtist] = useState({});
  // const [artistName, setArtistName] = useState("");
  const [selectedSong, setSelectedSong] = useState({});
  const [openRequestModal, setOpenRequestModal] = useState(false);
  const [activeTab, setActiveTab] = useState("artists");
  const [activeRequests, setActiveRequests] = useState({});
  const [step, setStep] = useState(1);
  const deviceId = getDeviceId();
  const { cart } = useSongCart();

  const fetchActiveRequests = useCallback( async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`
      );
      const data = await response.json();
      setActiveRequests(data);
    } catch (error) {
      console.error("Error fetching active requests:", error);
    }
  }, [deviceId]);

  useEffect(() => {
    const fetchSongsByArtist = async () => {
      try {
        const response = await fetch(
          `${process.env.REACT_APP_API_URL}/api/songs/artist/${artistID}`
        );
        const data = await response.json();
        setSongs(data);
      } catch (error) {
        console.error("Error fetching artist songs:", error);
      }
    };

    const fetchArtistDetails = async () => {
      try {
        const response = await fetch(
          `${process.env.REACT_APP_API_URL}/api/songs/artist-detail/${artistID}`
        );
        const data = await response.json();
        setArtist({ name: data.name, image: data.image });
      } catch (error) {
        console.error("Error fetching artist details:", error);
      }
    };

    if (artistID) {
      fetchSongsByArtist();
      fetchActiveRequests();
      fetchArtistDetails();
    }
  }, [artistID, deviceId, fetchActiveRequests]);

  const handleRequestOpen = (song) => {
    if (cart.length >= 3) {
      setStep(3);
    } else {
      setSelectedSong(song);
      setStep(1);
    }
    setOpenRequestModal(true);
  };

  const handleRequestSuccess = () => {
    fetchActiveRequests();
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    navigate(`/?activeTab=${tab}`);
  };

  return (
    <Box sx={{ bgcolor: "background.default", color: "text.primary", pb: 4 }}>
      <NavigationBar />
      {artist && (
        <Box sx={{ textAlign: "center", py: 4, mt: 8 }}>
          <Avatar
            alt={artist.name}
            src={artist.image}
            sx={{ width: 120, height: 120, mx: "auto", mb: 2 }}
          />
          <Typography variant="h4" fontWeight="bold">
            Songs by {artist.name}
          </Typography>
        </Box>
      )}

      <Box sx={{ padding: "16px" }}>
        <Grid container spacing={3}>
          {songs.map((song) => (
            <Grid item xs={12} sm={6} md={4} key={song.songs_master_id}>
              <SongCard
                song={song}
                activeRequests={activeRequests}
                onRequestOpen={handleRequestOpen}
                cart={cart}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      <RequestHandler
        selectedSong={selectedSong}
        setSelectedSong={setSelectedSong}
        openRequestModal={openRequestModal}
        setOpenRequestModal={setOpenRequestModal}
        onRequestSuccess={handleRequestSuccess}
        step={step}
        setStep={setStep}
      />

      <IconButton
        onClick={scrollToTop}
        sx={{
          position: "fixed",
          bottom: "20px",
          right: "20px",
          zIndex: 9999,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
        }}
      >
        <ArrowCircleUpOutlinedIcon />
      </IconButton>

      <TabNavigation activeTab={activeTab} handleTabClick={handleTabClick} />
    </Box>
  );
};

export default ArtistSongsPage;
