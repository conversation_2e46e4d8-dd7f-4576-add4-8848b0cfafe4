const db = require('./db');

// Seed a few songs for testing if none exist
db.get('SELECT COUNT(1) as c FROM songs_master', [], (err, row) => {
  if (err) return console.error('Seed count error:', err.message);
  if (row && row.c === 0) {
    const stmt = db.prepare(`
      INSERT INTO songs_master (name, artist, album, release_year, decade, image, duration, spotify_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    const samples = [
      ['<PERSON><PERSON> <PERSON>', '<PERSON><PERSON>', 'Aashiqui 2', 2013, '2010s', null, '04:22', null],
      ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 2003, '2000s', null, '05:21', null],
      ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Di<PERSON> Se..', 1998, '1990s', null, '06:52', null],
    ];
    db.serialize(() => {
      db.run('BEGIN');
      for (const s of samples) stmt.run(s);
      db.run('COMMIT');
      console.log('Seeded sample songs for quiz admin.');
    });
  }
});

module.exports = {};