import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Stack
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckIcon,
  RadioButtonUnchecked as UncheckedIcon,
  AccessTime as TimeIcon,
  Quiz as QuizIcon,
  MusicNote as MusicIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    backgroundColor: '#1A1A1A',
    border: '1px solid #333',
    borderRadius: '12px',
    maxWidth: '600px',
    width: '100%',
  },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  backgroundColor: '#121212',
  color: '#fff',
  borderBottom: '1px solid #333',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '16px 24px',
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  color: '#fff',
  padding: '24px',
}));

const InfoCard = styled(Box)(({ theme }) => ({
  backgroundColor: '#121212',
  border: '1px solid #333',
  borderRadius: '8px',
  padding: '16px',
  marginBottom: '16px',
}));

const AnswerItem = styled(ListItem)(({ theme, iscorrect }) => ({
  backgroundColor: iscorrect === 'true' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(255, 255, 255, 0.05)',
  border: `1px solid ${iscorrect === 'true' ? '#4CAF50' : '#333'}`,
  borderRadius: '8px',
  marginBottom: '8px',
  '& .MuiListItemIcon-root': {
    minWidth: '40px',
  },
}));

const QuestionViewDialog = ({ open, onClose, question, answers = [] }) => {
  if (!open) return null;
  if (!question) return null;

  const getDifficultyColor = (level) => {
    const colors = {
      'Easy': '#4CAF50',
      'easy': '#4CAF50',
      'Medium': '#FF9800',
      'Hard': '#F44336',
      'Very Hard': '#9C27B0',
      'Expert': '#E91E63'
    };
    return colors[level] || '#2196F3';
  };

  const formatTimeLimit = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  return (
    <StyledDialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <StyledDialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <QuizIcon sx={{ color: '#4CAF50' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Question Details
          </Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: '#ccc' }}>
          <CloseIcon />
        </IconButton>
      </StyledDialogTitle>

      <StyledDialogContent>
        {/* Question Text */}
        <InfoCard>
          <Typography variant="subtitle2" sx={{ color: '#4CAF50', mb: 1, fontWeight: 600 }}>
            Question
          </Typography>
          <Typography variant="body1" sx={{ color: '#fff', lineHeight: 1.6 }}>
            {question.question_text}
          </Typography>
        </InfoCard>

        {/* Question Metadata */}
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={2} mb={3}>
          <InfoCard>
            <Stack spacing={2}>
              <Box display="flex" alignItems="center" gap={1}>
                <MusicIcon sx={{ color: '#2196F3', fontSize: 20 }} />
                <Typography variant="subtitle2" sx={{ color: '#2196F3', fontWeight: 600 }}>
                  Song
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ color: '#fff' }}>
                {question.song_name}
              </Typography>
              {question.artist && (
                <Box display="flex" alignItems="center" gap={1}>
                  <PersonIcon sx={{ color: '#ccc', fontSize: 16 }} />
                  <Typography variant="caption" sx={{ color: '#ccc' }}>
                    {question.artist}
                  </Typography>
                </Box>
              )}
            </Stack>
          </InfoCard>

          <InfoCard>
            <Stack spacing={2}>
              <Box display="flex" alignItems="center" gap={1}>
                <QuizIcon sx={{ color: '#FF9800', fontSize: 20 }} />
                <Typography variant="subtitle2" sx={{ color: '#FF9800', fontWeight: 600 }}>
                  Configuration
                </Typography>
              </Box>
              
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="caption" sx={{ color: '#ccc' }}>
                  Difficulty:
                </Typography>
                <Chip
                  label={question.level_name}
                  size="small"
                  sx={{
                    backgroundColor: getDifficultyColor(question.level_name),
                    color: '#fff',
                    fontWeight: 600,
                    fontSize: '0.75rem'
                  }}
                />
              </Box>

              <Box display="flex" alignItems="center" gap={1}>
                <TimeIcon sx={{ color: '#ccc', fontSize: 16 }} />
                <Typography variant="caption" sx={{ color: '#ccc' }}>
                  Time Limit:
                </Typography>
                <Typography variant="body2" sx={{ color: '#fff' }}>
                  {formatTimeLimit(question.time_limit_seconds)}
                </Typography>
              </Box>

              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="caption" sx={{ color: '#ccc' }}>
                  Answer Type:
                </Typography>
                <Typography variant="body2" sx={{ color: '#fff' }}>
                  {question.answer_type_display || question.answer_type || 'Multiple Choice'}
                </Typography>
              </Box>
            </Stack>
          </InfoCard>
        </Box>

        {/* Answers Section */}
        <Box>
          <Typography variant="subtitle2" sx={{ color: '#4CAF50', mb: 2, fontWeight: 600 }}>
            Answer Options
          </Typography>
          
          {answers.length > 0 ? (
            <List sx={{ padding: 0 }}>
              {answers.map((answer, index) => (
                <AnswerItem key={answer.answer_id || index} iscorrect={answer.is_correct ? 'true' : 'false'}>
                  <ListItemIcon>
                    {answer.is_correct ? (
                      <CheckIcon sx={{ color: '#4CAF50' }} />
                    ) : (
                      <UncheckedIcon sx={{ color: '#ccc' }} />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ color: '#fff' }}>
                        {answer.answer_text}
                      </Typography>
                    }
                    secondary={
                      answer.is_correct && (
                        <Typography variant="caption" sx={{ color: '#4CAF50' }}>
                          Correct Answer
                        </Typography>
                      )
                    }
                  />
                </AnswerItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" sx={{ color: '#ccc', fontStyle: 'italic' }}>
              No answers available
            </Typography>
          )}
        </Box>

        {/* Metadata */}
        <Divider sx={{ my: 3, borderColor: '#333' }} />
        <Box display="flex" alignItems="center" gap={2}>
          <CalendarIcon sx={{ color: '#ccc', fontSize: 16 }} />
          <Typography variant="caption" sx={{ color: '#ccc' }}>
            Created: {formatDate(question.created_at)}
          </Typography>
          {question.updated_at && question.updated_at !== question.created_at && (
            <>
              <Typography variant="caption" sx={{ color: '#ccc' }}>
                •
              </Typography>
              <Typography variant="caption" sx={{ color: '#ccc' }}>
                Updated: {formatDate(question.updated_at)}
              </Typography>
            </>
          )}
        </Box>
      </StyledDialogContent>

      <DialogActions sx={{ backgroundColor: '#121212', borderTop: '1px solid #333', p: 2 }}>
        <Button onClick={onClose} sx={{ color: '#ccc' }}>
          Close
        </Button>
      </DialogActions>
    </StyledDialog>
  );
};

export default QuestionViewDialog;
