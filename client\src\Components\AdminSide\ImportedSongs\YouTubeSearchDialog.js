import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  Typography,
} from "@mui/material";
import axios from "axios";

const YouTubeSearchDialog = ({ open, onClose, song }) => {
  const [loading, setLoading] = useState(false);
  const [videos, setVideos] = useState([]);

  useEffect(() => {
    if (open && song) {
      fetchYouTubeVideos();
    }
  }, [open, song]);

  const fetchYouTubeVideos = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/youtube/search`,
        {
          params: {
            song: song.song_name,
            artist: song.artist_name,
            searchTerms: 'karaoke' // Default to karaoke for this dialog
          },
        }
      );
      setVideos(res.data);
    } catch (error) {
      console.error("YouTube fetch error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ bgcolor: "#ff0000", color: "white" }}>
        🎬 YouTube Results for: {song?.song_name} - {song?.artist_name}
      </DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <CircularProgress />
        ) : videos.length === 0 ? (
          <Typography>No videos found.</Typography>
        ) : (
          <List>
            {videos.map((video) => (
              <ListItem
                key={video.videoId}
                component="a"
                href={video.url}
                target="_blank"
                rel="noopener noreferrer"
                button
              >
                <ListItemText primary={video.title} />
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default YouTubeSearchDialog;