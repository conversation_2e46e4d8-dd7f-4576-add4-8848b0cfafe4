const SongUrlModel = require("../models/songUrlModel");

const SongUrlController = {
  create: async (req, res) => {
    try {
      const id = await SongUrlModel.create(req.body);
      res.status(201).json(id);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getAll: async (req, res) => {
    try {
      const urls = await SongUrlModel.getAll();
      res.json(urls);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getById: async (req, res) => {
    try {
      const url = await SongUrlModel.getById(req.params.id);
      if (!url) return res.status(404).json({ error: "Not found" });
      res.json(url);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  update: async (req, res) => {
    try {
      const updated = await SongUrlModel.update(req.params.id, req.body);
      res.json(updated);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  delete: async (req, res) => {
    try {
      const deleted = await SongUrlModel.delete(req.params.id);
      res.json(deleted);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },

  getUrlsBySongId: async (req, res) => {
    const { songs_master_id } = req.query;
    try {
      const urls = await SongUrlModel.getBySongId(songs_master_id);
      res.json(urls);
    } catch (error) {
       console.error(error);
      res.status(500).json({ error: "Failed to fetch URLs" });
    }
  },
};

module.exports = SongUrlController;
