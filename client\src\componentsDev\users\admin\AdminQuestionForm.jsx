import React, { useEffect, useMemo, useState } from 'react';

const API_BASE = process.env.REACT_APP_API_BASE || 'http://localhost:3001/api';

const AdminQuestionForm = () => {
  const [songs, setSongs] = useState([]);
  const [difficulties, setDifficulties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [form, setForm] = useState({
    songs_master_id: '',
    difficulty_id: '',
    question_text: '',
    time_limit_seconds: 30,
    answers: [
      { answer_text: '', is_correct: true, answer_order: 1 },
      { answer_text: '', is_correct: false, answer_order: 2 },
    ],
  });

  useEffect(() => {
    const fetchMeta = async () => {
      try {
        const [songsRes, diffRes] = await Promise.all([
          fetch(`${API_BASE}/songs`).then(r => r.json()),
          fetch(`${API_BASE}/meta/difficulties`).then(r => r.json()),
        ]);
        setSongs(songsRes || []);
        setDifficulties(diffRes || []);
      } catch (e) {
        setError('Failed to load songs or difficulties');
      } finally {
        setLoading(false);
      }
    };
    fetchMeta();
  }, []);

  const handleAnswerChange = (index, key, value) => {
    setForm(prev => {
      const next = { ...prev };
      next.answers = prev.answers.map((a, i) => i === index ? { ...a, [key]: value } : a);
      return next;
    });
  };

  const handleSetCorrect = (index) => {
    setForm(prev => {
      const next = { ...prev };
      next.answers = prev.answers.map((a, i) => ({ ...a, is_correct: i === index }));
      return next;
    });
  };

  const addAnswer = () => {
    setForm(prev => ({
      ...prev,
      answers: [...prev.answers, { answer_text: '', is_correct: false, answer_order: prev.answers.length + 1 }],
    }));
  };

  const removeAnswer = (index) => {
    setForm(prev => {
      const next = { ...prev };
      next.answers = prev.answers.filter((_, i) => i !== index).map((a, i) => ({ ...a, answer_order: i + 1 }));
      if (!next.answers.some(a => a.is_correct) && next.answers.length > 0) {
        next.answers[0].is_correct = true;
      }
      return next;
    });
  };

  const canSubmit = useMemo(() => {
    if (!form.songs_master_id || !form.difficulty_id) return false;
    if (!form.question_text.trim()) return false;
    if (form.answers.length < 2) return false;
    if (form.answers.filter(a => a.is_correct).length !== 1) return false;
    if (form.answers.some(a => !a.answer_text.trim())) return false;
    return true;
  }, [form]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    try {
      const res = await fetch(`${API_BASE}/admin/questions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          songs_master_id: Number(form.songs_master_id),
          question_text: form.question_text.trim(),
          difficulty_id: Number(form.difficulty_id),
          time_limit_seconds: Number(form.time_limit_seconds) || 30,
          answers: form.answers.map(a => ({
            answer_text: a.answer_text.trim(),
            is_correct: !!a.is_correct,
            answer_order: a.answer_order,
          })),
        }),
      });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.error || 'Failed to create question');
      }
      const data = await res.json();
      setSuccess('Question created');
      setForm(prev => ({
        ...prev,
        question_text: '',
        answers: [
          { answer_text: '', is_correct: true, answer_order: 1 },
          { answer_text: '', is_correct: false, answer_order: 2 },
        ],
      }));
      console.log('Created', data);
    } catch (e) {
      setError(e.message);
    }
  };

  if (loading) return <div>Loading…</div>;

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h2 className="text-xl font-semibold mb-4">Create Quiz Question</h2>
      {error && <div className="bg-red-100 text-red-800 p-2 mb-3">{error}</div>}
      {success && <div className="bg-green-100 text-green-800 p-2 mb-3">{success}</div>}
      <form onSubmit={handleSubmit} className="space-y-3">
        <div>
          <label className="block text-sm mb-1">Song</label>
          <select
            className="w-full border p-2"
            value={form.songs_master_id}
            onChange={e => setForm({ ...form, songs_master_id: e.target.value })}
          >
            <option value="">Select a song</option>
            {songs.map(s => (
              <option key={s.songs_master_id} value={s.songs_master_id}>
                {s.name} {s.artist ? `- ${s.artist}` : ''}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm mb-1">Difficulty</label>
          <select
            className="w-full border p-2"
            value={form.difficulty_id}
            onChange={e => setForm({ ...form, difficulty_id: e.target.value })}
          >
            <option value="">Select difficulty</option>
            {difficulties.map(d => (
              <option key={d.difficulty_id} value={d.difficulty_id}>{d.level_name}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm mb-1">Question</label>
          <textarea
            className="w-full border p-2"
            rows={3}
            value={form.question_text}
            onChange={e => setForm({ ...form, question_text: e.target.value })}
          />
        </div>

        <div>
          <label className="block text-sm mb-1">Time limit (seconds)</label>
          <input
            type="number"
            className="w-full border p-2"
            min={5}
            max={600}
            value={form.time_limit_seconds}
            onChange={e => setForm({ ...form, time_limit_seconds: e.target.value })}
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm">Answers</label>
            <button type="button" className="px-2 py-1 bg-gray-100" onClick={addAnswer}>Add</button>
          </div>
          <div className="space-y-2">
            {form.answers.map((ans, i) => (
              <div key={i} className="flex items-center gap-2">
                <input
                  type="radio"
                  name="correct"
                  checked={!!ans.is_correct}
                  onChange={() => handleSetCorrect(i)}
                  title="Correct"
                />
                <input
                  className="flex-1 border p-2"
                  placeholder={`Answer ${i + 1}`}
                  value={ans.answer_text}
                  onChange={e => handleAnswerChange(i, 'answer_text', e.target.value)}
                />
                <button type="button" className="px-2 py-1 bg-red-100" onClick={() => removeAnswer(i)} disabled={form.answers.length <= 2}>
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>

        <div className="pt-2">
          <button type="submit" disabled={!canSubmit} className={`px-4 py-2 text-white ${canSubmit ? 'bg-blue-600' : 'bg-gray-400'}`}>
            Create Question
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminQuestionForm;


