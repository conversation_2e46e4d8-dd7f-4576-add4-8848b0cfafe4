// hooks/useSongMutations.js
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { getDeviceId } from '../../utils/cookieUtils';

export const useSongRequest = () => {
  const queryClient = useQueryClient();
  const deviceId = getDeviceId();

  return useMutation({
    mutationFn: async (songData) => {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...songData,
            device_id: deviceId,
          }),
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to submit song request');
      }
      
      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries after successful mutation
      queryClient.invalidateQueries(['activeRequests']);
      queryClient.invalidateQueries(['cart']);
    },
  });
};

export const useDeleteSongRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (requestId) => {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/${requestId}`,
        {
          method: 'DELETE',
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to delete song request');
      }
      
      return requestId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['activeRequests']);
      queryClient.invalidateQueries(['cart']);
    },
  });
};