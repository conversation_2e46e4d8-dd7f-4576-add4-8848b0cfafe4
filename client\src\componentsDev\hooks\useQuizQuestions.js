// hooks/useQuizQuestions.js
import { useApi, useApiMutation } from './useApi';
import { useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

const API_BASE = process.env.REACT_APP_API_URL;

// Hook for fetching all quiz questions
export const useQuizQuestions = (options = {}) => {
  return useApi(
    'quiz-questions',
    `${API_BASE}/api/admin/questions`,
    {
      select: (data) => {
        return data.map((question) => ({
          ...question,
          id: question.question_id,
        }));
      },
      ...options,
    }
  );
};

// Hook for fetching a single quiz question
export const useQuizQuestion = (id, options = {}) => {
  return useApi(
    ['quiz-question', id],
    `${API_BASE}/api/admin/questions/${id}`,
    {
      enabled: !!id,
      ...options,
    }
  );
};

// Hook for creating a new quiz question
export const useCreateQuizQuestion = (options = {}) => {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (questionData) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions`,
        questionData
      );
      return response.data;
    },
    {
      onSuccess: (data) => {
        // Invalidate and refetch quiz questions list
        queryClient.invalidateQueries(['quiz-questions']);
        
        // Add the new question to the cache
        queryClient.setQueryData(['quiz-question', data.question_id], data);
      },
      ...options,
    }
  );
};

// Hook for updating a quiz question
export const useUpdateQuizQuestion = (options = {}) => {
  const queryClient = useQueryClient();

  return useApiMutation(
    async ({ id, ...questionData }) => {
      const response = await axios.put(
        `${API_BASE}/api/admin/questions/${id}`,
        questionData
      );
      return response.data;
    },
    {
      onSuccess: (data, variables) => {
        // Invalidate and refetch quiz questions list
        queryClient.invalidateQueries(['quiz-questions']);
        
        // Update the specific question in cache
        queryClient.setQueryData(['quiz-question', variables.id], data);
      },
      ...options,
    }
  );
};

// Hook for deleting a quiz question
export const useDeleteQuizQuestion = (options = {}) => {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (id) => {
      const response = await axios.delete(
        `${API_BASE}/api/admin/questions/${id}`
      );
      return response.data;
    },
    {
      onSuccess: (data, id) => {
        // Invalidate and refetch quiz questions list
        queryClient.invalidateQueries(['quiz-questions']);
        
        // Remove the question from cache
        queryClient.removeQueries(['quiz-question', id]);
      },
      ...options,
    }
  );
};

// Hook for fetching quiz questions by song
export const useQuizQuestionsBySong = (songId, options = {}) => {
  return useApi(
    ['quiz-questions-by-song', songId],
    `${API_BASE}/api/admin/questions/song/${songId}`,
    {
      enabled: !!songId,
      ...options,
    }
  );
};

// Hook for fetching quiz questions by difficulty
export const useQuizQuestionsByDifficulty = (difficultyId, options = {}) => {
  return useApi(
    ['quiz-questions-by-difficulty', difficultyId],
    `${API_BASE}/api/admin/questions/difficulty/${difficultyId}`,
    {
      enabled: !!difficultyId,
      ...options,
    }
  );
};

// Hook for bulk operations on quiz questions
export const useBulkQuizQuestionOperations = (options = {}) => {
  const queryClient = useQueryClient();

  const bulkDelete = useApiMutation(
    async (questionIds) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/bulk-delete`,
        { questionIds }
      );
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['quiz-questions']);
      },
      ...options,
    }
  );

  const bulkUpdate = useApiMutation(
    async (updates) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/bulk-update`,
        { updates }
      );
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['quiz-questions']);
      },
      ...options,
    }
  );

  return {
    bulkDelete,
    bulkUpdate,
  };
};

// Hook for quiz question statistics
export const useQuizQuestionStats = (options = {}) => {
  return useApi(
    'quiz-question-stats',
    `${API_BASE}/api/admin/questions/stats`,
    {
      select: (data) => ({
        totalQuestions: data.total_questions || 0,
        questionsByDifficulty: data.by_difficulty || [],
        questionsByType: data.by_type || [],
        questionsBySong: data.by_song || [],
        averageTimeLimit: data.average_time_limit || 30,
        mostUsedSongs: data.most_used_songs || [],
      }),
      ...options,
    }
  );
};

// Hook for validating quiz question data
export const useValidateQuizQuestion = () => {
  return useApiMutation(
    async (questionData) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/validate`,
        questionData
      );
      return response.data;
    }
  );
};

// Hook for duplicating a quiz question
export const useDuplicateQuizQuestion = (options = {}) => {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (questionId) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/${questionId}/duplicate`
      );
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['quiz-questions']);
      },
      ...options,
    }
  );
};

// Hook for importing quiz questions from file
export const useImportQuizQuestions = (options = {}) => {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (formData) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/import`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['quiz-questions']);
      },
      ...options,
    }
  );
};

// Hook for exporting quiz questions
export const useExportQuizQuestions = () => {
  return useApiMutation(
    async (filters = {}) => {
      const response = await axios.post(
        `${API_BASE}/api/admin/questions/export`,
        filters,
        {
          responseType: 'blob',
        }
      );
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'quiz-questions.csv');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return response.data;
    }
  );
};
