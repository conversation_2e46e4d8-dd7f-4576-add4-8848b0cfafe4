import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Button,
  IconButton,
  Modal,
  TextField,
  MenuItem,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { songService } from "./songService";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  maxHeight: "90vh",
  overflowY: "auto",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
};

const FileSection = ({
  songFiles,
  setSongFiles,
  tempFiles,
  setTempFiles,
  editingIndex,
  selectedSongId,
}) => {
  const [fileTypes, setFileTypes] = useState([]);
  const [selectedFileType, setSelectedFileType] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [openFileModal, setOpenFileModal] = useState(false);

  useEffect(() => {
    const fetchFileTypes = async () => {
      try {
        const fileTypesData = await songService.fetchFileTypes();
        setFileTypes(fileTypesData);
      } catch (error) {
        console.error("Failed to fetch file types:", error);
      }
    };
    fetchFileTypes();
  }, []);

  const handleOpenFileModal = () => {
    setOpenFileModal(true);
    setSelectedFileType("");
    setSelectedFile(null);
  };

  const handleCloseFileModal = () => {
    setOpenFileModal(false);
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleAddFile = async () => {
    if (!selectedFileType || !selectedFile) return;

    const fileType = fileTypes.find(
      (type) => type.file_type_name === selectedFileType
    );
    if (!fileType) return;

    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("file_type_lookup_id", fileType.file_type_lookup_id);

    try {
      if (selectedSongId) {
        // Existing song - add to API immediately
        await songService.addSongFile(selectedSongId, formData);
        const updatedFiles = await songService.fetchSongFiles(selectedSongId);
        setSongFiles(updatedFiles);
      } else {
        // New song - store in temp state
        setTempFiles((prev) => [
          ...prev,
          {
            file_name: selectedFile.name,
            file_type_lookup_id: fileType.file_type_lookup_id,
          },
        ]);
      }

      handleCloseFileModal();
      setSelectedFileType("");
      setSelectedFile(null);
    } catch (error) {
      console.error("Failed to add file:", error);
    }
  };

  const handleDeleteFile = async (fileId, isTemp = false) => {
    try {
      if (isTemp) {
        setTempFiles((prev) => prev.filter((_, index) => index !== fileId));
      } else {
        await songService.deleteSongFile(fileId);
        if (selectedSongId) {
          const updatedFiles = await songService.fetchSongFiles(selectedSongId);
          setSongFiles(updatedFiles);
        }
      }
    } catch (error) {
      console.error("Failed to delete file:", error);
    }
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="subtitle1">Song Files</Typography>
      <Button
        variant="outlined"
        onClick={handleOpenFileModal}
        sx={{ mb: 1 }}
      >
        + Add File
      </Button>
      {(editingIndex !== null ? songFiles : tempFiles).map(
        (fileItem, index) => (
          <Box
            key={fileItem.song_files_id || index}
            sx={{ display: "flex", alignItems: "center", mb: 1 }}
          >
            <Typography>
              {fileItem.file_type_name} :
              <a
                href={`${process.env.REACT_APP_API_URL}/${fileItem.file}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{ textDecoration: "none", color: "blue" }}
              >
                {fileItem.file.split("\\").pop()}
              </a>
            </Typography>
            <IconButton
              onClick={() =>
                handleDeleteFile(
                  editingIndex !== null
                    ? fileItem.song_files_id
                    : index,
                  editingIndex === null
                )
              }
              size="small"
              sx={{ ml: 1 }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        )
      )}
      {(editingIndex !== null
        ? songFiles.length === 0
        : tempFiles.length === 0) && (
        <Typography variant="body2" sx={{ color: "gray" }}>
          No files attached to this song.
        </Typography>
      )}

      <Modal open={openFileModal} onClose={handleCloseFileModal}>
        <Box sx={modalStyle}>
          <Typography variant="h6">Add Song File</Typography>
          <TextField
            select
            label="File Type"
            value={selectedFileType}
            onChange={(e) => setSelectedFileType(e.target.value)}
            fullWidth
            margin="normal"
          >
            {fileTypes.map((type) => (
              <MenuItem
                key={type.file_type_lookup_id}
                value={type.file_type_name}
              >
                {type.file_type_name}
              </MenuItem>
            ))}
          </TextField>
          <Box sx={{ mt: 2, mb: 2 }}>
            <Button variant="outlined" component="label">
              Select File
              <input type="file" hidden onChange={handleFileUpload} />
            </Button>
            {selectedFile && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Selected: {selectedFile.name}
              </Typography>
            )}
          </Box>
          <Button
            variant="contained"
            onClick={handleAddFile}
            disabled={!selectedFileType || !selectedFile}
          >
            Add File
          </Button>
        </Box>
      </Modal>
    </Box>
  );
};

export default FileSection;