const express = require("express");
const router = express.Router();
const settingsController = require("../controllers/settingsController");
const authMiddleware = require("../middleware/authMiddleware");

// Get all settings (admin only)
router.get("/", authMiddleware, settingsController.getAll);

// Get specific setting by key (admin only)
router.get("/:key", authMiddleware, settingsController.getByKey);

// Create or update setting (admin only)
router.post("/", authMiddleware, settingsController.upsert);

// Delete setting (admin only)
router.delete("/:key", authMiddleware, settingsController.delete);

// Get request options mode (public endpoint for user side)
router.get("/public/request-options-mode", settingsController.getRequestOptionsMode);

// Update request options mode (admin only)
router.put("/request-options-mode", authMiddleware, settingsController.updateRequestOptionsMode);

module.exports = router;
