export function timeSince(dateString) {
  if (!dateString) return "Unknown time"; // Prevent crashing on undefined

  const now = Date.now();
  const utcString = dateString.replace(' ', 'T') + 'Z';
  const past = new Date(utcString).getTime();

  const seconds = Math.floor((now - past) / 1000);
  const intervals = {
    year:   365 * 24 * 60 * 60,
    month:  30 * 24 * 60 * 60,
    day:    24 * 60 * 60,
    hour:   60 * 60,
    minute: 60,
    second: 1,
  };

  for (const [unit, secInUnit] of Object.entries(intervals)) {
    const count = Math.floor(seconds / secInUnit);
    if (count >= 1) return `${count} ${unit}${count > 1 ? 's' : ''} ago`;
  }
  return 'just now';
}