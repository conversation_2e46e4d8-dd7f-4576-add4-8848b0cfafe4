import axios from "axios";

const fetchSongs = async () => {
  const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/songs`);
  return response.data;
};

const createSong = async (payload) => {
  const res = await axios.post(
    `${process.env.REACT_APP_API_URL}/api/songs`,
    payload
  );
  return res.data.id;
};

const updateSong = async (songId, payload) => {
  await axios.put(
    `${process.env.REACT_APP_API_URL}/api/songs/${songId}`,
    payload
  );
};

const deleteSong = async (songId) => {
  await axios.delete(
    `${process.env.REACT_APP_API_URL}/api/songs/${songId}`
  );
};

const removeSongMappings = async (songId) => {
  await Promise.all([
    axios.delete(
      `${process.env.REACT_APP_API_URL}/api/playlist-songs/song/${songId}`
    ),
    axios.delete(
      `${process.env.REACT_APP_API_URL}/api/song-genres/song/${songId}`
    ),
  ]);
};

const addSongUrls = async (songId, tempUrls) => {
  if (tempUrls.length > 0) {
    await Promise.all(
      tempUrls.map((url) =>
        axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
          ...url,
          songs_master_id: songId,
        })
      )
    );
  }
};

const addSongFiles = async (songId, tempFiles) => {
  if (tempFiles.length > 0) {
    await Promise.all(
      tempFiles.map((file) =>
        axios.post(`${process.env.REACT_APP_API_URL}/api/song-files`, {
          ...file,
          songs_master_id: songId,
        })
      )
    );
  }
};

const addSongPlaylists = async (songId, selectedPlaylists) => {
  if (selectedPlaylists.length > 0) {
    await Promise.all(
      selectedPlaylists.map((playlistId) =>
        axios.post(`${process.env.REACT_APP_API_URL}/api/playlist-songs`, {
          playlist_master_id: playlistId,
          songs_master_id: songId,
        })
      )
    );
  }
};

const addSongGenres = async (songId, selectedGenres) => {
  if (selectedGenres.length > 0) {
    await Promise.all(
      selectedGenres.map((genre) =>
        axios.post(`${process.env.REACT_APP_API_URL}/api/song-genres`, {
          songs_master_id: songId,
          genre_type_lookup_id: genre.genre_type_lookup_id,
        })
      )
    );
  }
};

const fetchSongPlaylists = async (songId) => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/playlist-songs/song/${songId}/playlists`
  );
  return response.data.map((p) => p.playlist_master_id);
};

const fetchSongGenres = async (songId) => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/song-genres/song/${songId}`
  );
  return response.data.map((item) => ({
    genre_type_lookup_id: item.genre_type_lookup_id,
    genre_type_name: item.genre_type_name,
  }));
};

const fetchSongFiles = async (songId) => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/song-files/song/${songId}`
  );
  return response.data;
};

const fetchSongUrls = async (songId) => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songId}`
  );
  return response.data;
};

const deleteSongUrl = async (songUrlId) => {
  await axios.delete(
    `${process.env.REACT_APP_API_URL}/api/song-urls/${songUrlId}`
  );
};

const deleteSongFile = async (fileId) => {
  await axios.delete(
    `${process.env.REACT_APP_API_URL}/api/song-files/${fileId}`
  );
};

const addSongUrl = async (songId, urlPayload) => {
  await axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
    ...urlPayload,
    songs_master_id: songId,
  });
};

const addSongFile = async (songId, formData) => {
  await axios.post(
    `${process.env.REACT_APP_API_URL}/api/song-files/${songId}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
};

const fetchPlaylists = async () => {
  const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/playlist`);
  return response.data;
};

const fetchGenres = async () => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/lookups/genre`
  );
  return response.data;
};

const fetchUrlTypes = async () => {
  const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/url`);
  return response.data.filter((t) => t.status === 1);
};

const fetchFileTypes = async () => {
  const response = await axios.get(
    `${process.env.REACT_APP_API_URL}/api/lookups/file`
  );
  return response.data.filter((type) => type.status === 1);
};

const removePlaylistFromSong = async (playlistId, songId) => {
  await axios.delete(
    `${process.env.REACT_APP_API_URL}/api/playlist-songs/${playlistId}/${songId}`
  );
};

export const songService = {
  fetchSongs,
  createSong,
  updateSong,
  deleteSong,
  removeSongMappings,
  addSongUrls,
  addSongFiles,
  addSongPlaylists,
  addSongGenres,
  fetchSongPlaylists,
  fetchSongGenres,
  fetchSongFiles,
  fetchSongUrls,
  deleteSongUrl,
  deleteSongFile,
  addSongUrl,
  addSongFile,
  fetchPlaylists,
  fetchGenres,
  fetchUrlTypes,
  fetchFileTypes,
  removePlaylistFromSong,
};