const Playlist = require("../models/playlistModel");

exports.create = async (req, res) => {
  try {
    const { title, description, playlist_type_lookup_id, spotify_url } =
      req.body;
    const image = req.file ? `/uploads/playlists/${req.file.filename}` : null;

    const result = await Playlist.create({
      title,
      description,
      playlist_type_lookup_id,
      spotify_url,
      image,
    });
    res.status(201).json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getAll = async (req, res) => {
  try {
    const data = await Playlist.getAll();
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.getById = async (req, res) => {
  try {
    const data = await Playlist.getById(req.params.id);
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.update = async (req, res) => {
  try {
    const { title, description, playlist_type_lookup_id, spotify_url, image: existingImage } =
      req.body;
      
    let image;
    if (req.file) {
      // New file uploaded
      image = `/uploads/playlists/${req.file.filename}`;
    } else if (existingImage) {
      // Keep existing image path
      image = existingImage;
    } else {
      // No image
      image = null;
    }

    const result = await Playlist.update(req.params.id, {
      title,
      description,
      playlist_type_lookup_id,
      spotify_url,
      image,
    });

    // Return the updated playlist data
    const updatedPlaylist = await Playlist.getById(req.params.id);
    res.json(updatedPlaylist);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.remove = async (req, res) => {
  try {
    const result = await Playlist.delete(req.params.id);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
