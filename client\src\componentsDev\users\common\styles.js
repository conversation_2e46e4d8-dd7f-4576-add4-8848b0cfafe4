import { AppBar, styled } from "@mui/material";

export const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: "transparent",
  backgroundImage: `linear-gradient(to left, #291f0c, #322314, #3c271d, #462c26, #4f302f, #583638, #613c40, #6a4248)`,
}));

export const BottomTabBar = styled("div")({
  position: "fixed",
  bottom: "15px",
  left: "5px",
  right: "5px",
  backgroundColor: "#1E1E1E",
  display: "flex",
  justifyContent: "space-around",
  alignItems: "center",
  padding: "8px",
  borderRadius: "50px",
  boxShadow: "0px -2px 10px rgba(0, 0, 0, 0.5)",
  zIndex: 2,
});

export const Tab = styled("div")({
  position: "relative",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  padding: "8px",
  zIndex: 1,
  marginTop: "10px",
});

export const TabIconWrapper = styled("div")(({ isActive, activeTab }) => ({
  position: "absolute",
  top: isActive ? "-40px" : "-7px",
  left: "50%",
  transform: "translateX(-50%)",
  width: isActive ? "50px" : "auto",
  height: isActive ? "50px" : "auto",
  backgroundColor: isActive ? "#1E1E1E" : "transparent",
  borderRadius: isActive ? "50%" : "none",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  boxShadow: isActive ? "0px 4px 10px rgba(0, 0, 0, 0.3)" : "none",
}));

export const TabLabel = styled("span")(({ isActive }) => ({
  color: isActive ? "#966300" : "#939393",
  marginTop: "12px",
  fontFamily: "Poppins",
  fontSize: "14px",
  fontWeight: " 400",
  lineHeight: "18px",
  textAlign: "center",
}));

export const CurvedBackground = styled("div")(({ activeTab }) => ({
  position: "absolute",
  top: "-20px",
  left: "50%",
  transform: "translateX(-52%)",
  width: "80px",
  height: "40px",
  backgroundColor: "rgba(0, 0, 0, 0.7)",
  borderRadius: "0 0 50px 50px",
}));
