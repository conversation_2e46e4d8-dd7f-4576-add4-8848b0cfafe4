import React from 'react'
import { Box, Typography } from '@mui/material'

const HeroMaster = ({ mode, songsLength }) => {
    return (
        <Box
            sx={{
                px: 4,
                mb: { md: 8, sx: 0 },
                paddingTop: { xs: "130px", md: "130px" },
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                alignItems: "center",
                width: "100%",
            }}
        >
            <Box
                sx={{
                    width: { md: "50%" },
                    borderRadius: "5px",
                    padding: "10px 20px 20px 20px",
                }}
            >
                {mode === "jukebox" ? (
                    <img
                        src="./jukebox_logo.png"
                        alt="Iron Mountain Logo"
                        style={{
                            width: "320px",
                            height: "auto",
                            zIndex: 2,
                            borderRadius: "5px",
                            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            marginInline: "auto",
                        }}
                    />
                ) : mode === "openmic" ? (
                    <img
                        src="./openmic_logo.png"
                        alt="Iron Mountain Logo"
                        style={{
                            width: "320px",
                            height: "auto",
                            zIndex: 2,
                            borderRadius: "5px",
                            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            marginInline: "auto",
                        }}
                    />
                ) : (
                    <img
                        src="./assets/HomePageBackground.png"
                        alt="Iron Mountain Logo"
                        style={{
                            width: "320px",
                            height: "auto",
                            zIndex: 2,
                            borderRadius: "5px",
                            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            marginInline: "auto",
                        }}
                    />
                )}
            </Box>
            <Box
                sx={{
                    position: "relative",
                    zIndex: 0,
                    widht: "100%",
                }}
            >
                <Typography
                    variant="h3"
                    sx={{
                        mb: 2,
                        color: "white",
                        fontSize: { xs: "2rem", md: "3rem" },
                        fontWeight: "700",
                        lineHeight: { xs: "38px", md: "57.6px" },
                        textShadow: "2px 2px 4px rgba(0, 0, 0, 0.7)",
                        fontFamily: "Barlow Semi Condensed",
                        width: "fit-content",
                    }}
                >
                    {/* {mode === "jukebox"
                ? "Welcome To The Live Music Juke Box"
                : mode === "openmic"
                  ? "Welcome To Open Mic Hour"
                  : "Welcome to Iron Mountain Music"} */}
                    {mode === "jukebox" || mode === "openmic"
                        ? ""
                        : "Welcome to Iron Mountain Music"}
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        mb: 4,
                        color: "#D6D6D6",
                        fontSize: { xs: "1rem", md: "1.25rem" },
                        fontWeight: "400",
                        opacity: 0.9,
                        fontFamily: "Nunito",
                        lineHeight: { xs: "23.28px", md: "27.38px" },
                    }}
                >
                    {mode === "jukebox"
                        ? `Make the band play your way. Request the songs you love, hear them played live, and own the soundtrack to your night.`
                        : mode === "openmic"
                            ? "Pick It. Sing It. Own It. Choose the songs you love with the Live Music Jukebox—then take the mic yourself during Open Mic Hour."
                            : `Browse our collection of ${songsLength > 0 ? songsLength : "..."
                            } songs from various artists and genres.`}
                </Typography>
            </Box>
        </Box>
    )
}

export default HeroMaster