import {
  <PERSON><PERSON>,
  TextField,
  Box,
  Container,
  Typography,
  IconButton,
  Modal,
  MenuItem,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Paper,
  Divider,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import Chip from "@mui/material/Chip";
import Autocomplete from "@mui/material/Autocomplete";
import { useState, useEffect } from "react";
import imageCompression from "browser-image-compression";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import CloseIcon from "@mui/icons-material/Close";
import axios from "axios";
import Header from "./Header";
import { enqueueSnackbar } from "notistack";
import YouTubeIcon from "@mui/icons-material/YouTube";
import YouTubeSearchAndSaveDialog from "./ImportedSongs/YouTubeSearchAndSaveDialog";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  maxHeight: "90vh",
  overflowY: "auto",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
};

const SongScreen = () => {
  const [songs, setSongs] = useState([]);
  const [allPlaylists, setAllPlaylists] = useState([]);
  const [selectedPlaylists, setSelectedPlaylists] = useState([]);
  const [searchPlaylistTerm, setSearchPlaylistTerm] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [songData, setSongData] = useState({
    name: "",
    artist: "",
    album: "",
    image: "",
    release_year: "",
    decade: "",
    duration: "",
  });
  const [selectedSongId, setSelectedSongId] = useState(null);
  const [urlTypes, setUrlTypes] = useState([]);
  const [selectedUrlType, setSelectedUrlType] = useState("");
  const [newUrl, setNewUrl] = useState("");
  const [openUrlModal, setOpenUrlModal] = useState(false);
  const [songUrls, setSongUrls] = useState([]);
  const [tempUrls, setTempUrls] = useState([]);
  const [openYouTubeDialog, setOpenYouTubeDialog] = useState(false);

  // Genre states
  const [genres, setGenres] = useState([]); // All available genres
  const [selectedGenres, setSelectedGenres] = useState([]); // Selected genres for current song
  const [openGenreModal, setOpenGenreModal] = useState(false);
  const [tempSelectedGenres, setTempSelectedGenres] = useState([]);

  // files states
  const [fileTypes, setFileTypes] = useState([]);
  const [selectedFileType, setSelectedFileType] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [openFileModal, setOpenFileModal] = useState(false);
  const [songFiles, setSongFiles] = useState([]);
  const [tempFiles, setTempFiles] = useState([]);

  // duplicate Check
  const [isDuplicate, setIsDuplicate] = useState(false);

  // YouTube Search Terms
  const [youtubeSearchTerms, setYouTubeSearchTerms] = useState({});

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      const [songsRes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/songs`),
      ]);

      setSongs(songsRes.data);
    } catch (err) {
      console.error("Failed to fetch data", err);
    }
  };

  useEffect(() => {
    const fetchGenres = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/lookups/genre`
        );
        setGenres(response.data);
      } catch (error) {
        console.error("Failed to fetch genres:", error);
      }
    };
    fetchGenres();
  }, []);

  const handleOpenGenreModal = () => {
    setTempSelectedGenres(selectedGenres);
    setOpenGenreModal(true);
  };

  const handleCloseGenreModal = () => {
    setOpenGenreModal(false);
  };

  const saveSelectedGenres = async () => {
    setSelectedGenres(tempSelectedGenres);
    setOpenGenreModal(false);
  };

  useEffect(() => {
    axios
      .get(`${process.env.REACT_APP_API_URL}/api/playlist`)
      .then((res) => setAllPlaylists(res.data))
      .catch((err) => console.error("Failed to load playlists:", err));
  }, []);

  useEffect(() => {
    if (selectedSongId) {
      axios
        .get(
          `${process.env.REACT_APP_API_URL}/api/playlist-songs/song/${selectedSongId}/playlists`
        )
        .then((res) =>
          setSelectedPlaylists(res.data.map((p) => p.playlist_master_id))
        )
        .catch((err) => console.error("Failed to fetch song playlists:", err));
    }
  }, [selectedSongId]);

  useEffect(() => {
    if (selectedSongId) {
      fetchSongFiles(selectedSongId);
    }
  }, [selectedSongId]);

  useEffect(() => {
    const fetchFileTypes = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/lookups/file`
        );
        const filteredFileTypes = response.data.filter(
          (type) => type.status === 1
        );
        setFileTypes(filteredFileTypes);
      } catch (error) {
        console.error("Failed to fetch file types:", error);
      }
    };

    fetchFileTypes();
  }, []);

  const handleOpenFileModal = () => {
    setOpenFileModal(true);
    setSelectedFileType("");
    setSelectedFile(null);
  };

  const handleCloseFileModal = () => {
    setOpenFileModal(false);
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleAddFile = async () => {
    if (!selectedFileType || !selectedFile) return;

    const fileType = fileTypes.find(
      (type) => type.file_type_name === selectedFileType
    );
    if (!fileType) return;

    const formData = new FormData();
    formData.append("file", selectedFile); // Append the file
    formData.append("file_type_lookup_id", fileType.file_type_lookup_id); // Append the file type

    try {
      if (selectedSongId) {
        // Existing song - add to API immediately
        await axios.post(
          `${process.env.REACT_APP_API_URL}/api/song-files/${selectedSongId}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        fetchSongFiles(selectedSongId);
      } else {
        // New song - store in temp state
        setTempFiles((prev) => [
          ...prev,
          {
            file_name: selectedFile.name,
            file_type_lookup_id: fileType.file_type_lookup_id,
          },
        ]);
      }

      handleCloseFileModal();
      setSelectedFileType("");
      setSelectedFile(null);
    } catch (error) {
      console.error("Failed to add file:", error);
    }
  };

  const fetchSongFiles = async (songId) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/song-files/song/${songId}`
      );
      setSongFiles(response.data);
    } catch (error) {
      console.error("Failed to fetch song files:", error);
    }
  };

  const handleDeleteFile = async (fileId, isTemp = false) => {
    try {
      if (isTemp) {
        setTempFiles((prev) => prev.filter((_, index) => index !== fileId));
      } else {
        await axios.delete(
          `${process.env.REACT_APP_API_URL}/api/song-files/${fileId}`
        );
        if (selectedSongId) {
          fetchSongFiles(selectedSongId);
        }
      }
    } catch (error) {
      console.error("Failed to delete file:", error);
    }
  };

  const handleDeleteSongUrl = async (songUrlID, isTemp = false) => {
    try {
      if (isTemp) {
        setTempUrls((prev) => prev.filter((_, index) => index !== songUrlID));
      } else {
        await axios.delete(
          `${process.env.REACT_APP_API_URL}/api/song-urls/${songUrlID}`
        );
        if (selectedSongId) {
          fetchSongUrls(selectedSongId);
        }
      }
    } catch (error) {
      console.error("Failed to delete song url:", error);
    }
  };

  useEffect(() => {
    if (selectedSongId) {
      fetchSongUrls(selectedSongId);
    }
  }, [selectedSongId]);

  useEffect(() => {
    axios
      .get(`${process.env.REACT_APP_API_URL}/api/lookups/url`)
      .then((res) => {
        const types = res.data.filter((t) => t.status === 1);
        setUrlTypes(types);
        // Set default checked state: Karaoke checked, others unchecked
        const initialTerms = {};
        types.forEach((type) => {
          initialTerms[type.url_type_name] =
            type.url_type_name.toLowerCase() === "karaoke";
        });
        setYouTubeSearchTerms(initialTerms);
      })
      .catch((err) => console.error("Failed to fetch URL types:", err));
  }, []);

  const handleOpenUrlModal = () => {
    setOpenUrlModal(true);
    setSelectedUrlType("");
    setNewUrl("");
  };

  const handleCloseUrlModal = () => {
    setOpenUrlModal(false);
  };

  const handleAddUrl = async () => {
    if (!selectedUrlType || !newUrl) return;

    const urlType = urlTypes.find(
      (type) => type.url_type_name === selectedUrlType
    );
    if (!urlType) return;

    const urlPayload = {
      song_url: newUrl,
      url_type_lookup_id: urlType.url_type_lookup_id,
      url_type_name: urlType.url_type_name,
    };

    if (selectedSongId) {
      // Existing song - add to API immediately
      try {
        await axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
          ...urlPayload,
          songs_master_id: selectedSongId,
        });
        fetchSongUrls(selectedSongId); // Refresh the URLs list
      } catch (error) {
        console.error("Failed to add URL:", error);
      }
    } else {
      // New song - store in temp state
      setTempUrls((prev) => [...prev, urlPayload]);
    }

    handleCloseUrlModal();
    setSelectedUrlType("");
    setNewUrl("");
  };

  const fetchSongUrls = async (songId) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songId}`
      );
      setSongUrls(response.data);
    } catch (error) {
      console.error("Failed to fetch song URLs:", error);
    }
  };

  const filteredPlaylists = allPlaylists.filter((pl) =>
    pl.title.toLowerCase().includes(searchPlaylistTerm.toLowerCase())
  );

  const handleAddPlaylist = (playlist) => {
    if (!selectedPlaylists.includes(playlist.playlist_master_id)) {
      setSelectedPlaylists((prev) => [...prev, playlist.playlist_master_id]);
    }
  };

  const handleRemovePlaylist = (playlist) => {
    try {
      axios.delete(
        `${process.env.REACT_APP_API_URL}/api/playlist-songs/${playlist.playlist_master_id}/${selectedSongId}`
      );
      setSelectedPlaylists((prev) =>
        prev.filter((id) => id !== playlist.playlist_master_id)
      );
    } catch (error) {
      console.error("Failed to remove playlist:", error);
    }
  };

  const handleOpen = () => {
    setOpen(true);
    setSelectedPlaylists([]);
    setSelectedSongId(null);
    setSongData({
      name: "",
      artist: "",
      album: "",
      image: "",
      release_year: "",
      decade: "",
      duration: "",
    });
    setEditingIndex(null);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingIndex(null);
    setSelectedSongId(null);
    setSelectedPlaylists([]);
    setSelectedGenres([]);
    setTempSelectedGenres([]);
    setSongUrls([]);
    setTempUrls([]);
    setSongFiles([]);
    setTempFiles([]);
    setSongData({
      name: "",
      artist: "",
      album: "",
      image: "",
      release_year: "",
      decade: "",
      duration: "",
    }); // You can define a reusable initialSongData object
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedSongData = {
      ...songData,
      [name]: value,
    };
    setSongData(updatedSongData);

    // Real-time duplicate check only when typing song name or artist
    if (name === "name") {
      const duplicate = songs.find(
        (song) =>
          song.name.trim().toLowerCase() ===
            (name === "name"
              ? value.trim().toLowerCase()
              : updatedSongData.name.trim().toLowerCase()) &&
          (editingIndex === null ||
            song.songs_master_id !== songs[editingIndex].songs_master_id)
      );

      setIsDuplicate(!!duplicate);
    }
  };

  const convertToBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (err) => reject(err);
    });

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    try {
      // Compress the image
      const compressedFile = await imageCompression(file, {
        maxSizeMB: 0.3, // Max size in MB
        maxWidthOrHeight: 600, // Resize the image
        useWebWorker: true,
      });

      // Convert to base64 after compression
      const base64 = await convertToBase64(compressedFile);
      setSongData((prev) => ({ ...prev, image: base64 }));
    } catch (error) {
      console.error("Image compression failed:", error);
    }
  };

  const handleSave = async () => {
    try {
      const payload = {
        name: songData.name,
        artist: songData.artist,
        album: songData.album,
        decade: songData.decade,
        duration: songData.duration,
        image: songData.image,
        release_year: songData.release_year,
      };

      const existingDuplicate = songs.find(
        (song) =>
          song.name.trim().toLowerCase() ===
            songData.name.trim().toLowerCase() &&
          // song.artist.trim().toLowerCase() ===
          //   songData.artist.trim().toLowerCase() &&
          (editingIndex === null ||
            song.songs_master_id !== songs[editingIndex].songs_master_id)
      );

      if (existingDuplicate) {
        enqueueSnackbar("This song already exists. Please try another name.", {
          variant: "error",
        });
        return;
      }

      let songId;

      if (editingIndex !== null) {
        songId = songs[editingIndex].songs_master_id;
        await axios.put(
          `${process.env.REACT_APP_API_URL}/api/songs/${songId}`,
          payload
        );

        // 🔥 Remove all previous playlist mappings for this song
        await axios.delete(
          `${process.env.REACT_APP_API_URL}/api/playlist-songs/song/${songId}`
        );
        await axios.delete(
          `${process.env.REACT_APP_API_URL}/api/song-genres/song/${songId}`
        );
      } else {
        const res = await axios.post(
          `${process.env.REACT_APP_API_URL}/api/songs`,
          payload
        );
        songId = res.data.id;
      }

      // Only add new URLs from tempUrls
      if (tempUrls.length > 0) {
        await Promise.all(
          tempUrls.map((url) =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
              ...url,
              songs_master_id: songId,
            })
          )
        );
      }

      // Only add new files from tempFiles
      if (tempFiles.length > 0) {
        await Promise.all(
          tempFiles.map((file) =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/song-files`, {
              ...file,
              songs_master_id: songId,
            })
          )
        );
      }

      // ✅ Add updated playlist mappings
      if (selectedPlaylists.length > 0) {
        await Promise.all(
          selectedPlaylists.map((playlistId) =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/playlist-songs`, {
              playlist_master_id: playlistId,
              songs_master_id: songId,
            })
          )
        );
      }

      if (selectedGenres.length > 0) {
        await Promise.all(
          selectedGenres.map((genre) =>
            axios.post(`${process.env.REACT_APP_API_URL}/api/song-genres`, {
              songs_master_id: songId,
              genre_type_lookup_id: genre.genre_type_lookup_id,
            })
          )
        );
      }

      // ✅ Refetch updated playlists for this song (UI state sync)
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/playlist-songs/song/${songId}/playlists`
      );
      setSelectedPlaylists(res.data.map((p) => p.playlist_master_id));

      await fetchAllData();
      handleClose();
    } catch (err) {
      console.error("Error saving song:", err);
    }
  };

  const handleEdit = async (songId) => {
    const index = songs.findIndex((song) => song.songs_master_id === songId);
    if (index === -1) return;
    setEditingIndex(index);
    const songToEdit = songs[index];
    setSelectedSongId(songToEdit.songs_master_id);
    setSongData({
      ...songToEdit,
    });
    setSelectedPlaylists([]);

    try {
      // Fetch genres for this song
      const [genresRes, filesRes, urlsRes] = await Promise.all([
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/song-genres/song/${songToEdit.songs_master_id}`
        ),
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/song-files/song/${songToEdit.songs_master_id}`
        ),
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/song-urls/urls?songs_master_id=${songToEdit.songs_master_id}`
        ),
      ]);

      // Map to the format expected by your component
      const songGenres = genresRes.data.map((item) => ({
        genre_type_lookup_id: item.genre_type_lookup_id,
        genre_type_name: item.genre_type_name,
      }));

      setSelectedGenres(songGenres);
      setTempSelectedGenres(songGenres);
      setSongFiles(filesRes.data);
      setSongUrls(urlsRes.data);
      setTempUrls([]);
      setTempFiles([]);
    } catch (err) {
      console.error("Failed to load song genres:", err);
    }
    setOpen(true);
  };

  const handleDelete = async (songId) => {
    try {
      await axios.delete(
        `${process.env.REACT_APP_API_URL}/api/songs/${songId}`
      );
      await fetchAllData();
    } catch (err) {
      console.error("Delete song failed", err);
    }
  };

  const filteredSongs = songs.filter(
    (song) =>
      song.name && song.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <Header />
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 2,
            pb: 2,
            borderBottom: "1px solid",
          }}
        >
          <Typography variant="h4">Songs</Typography>
          <Button variant="contained" color="success" onClick={handleOpen}>
            Add Song
          </Button>
        </Box>

        <TextField
          fullWidth
          label="Search Songs"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ my: 2 }}
        />

        {filteredSongs.map((song) => (
          <Box
            key={song.songs_master_id}
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <img
                src={song.image || "https://placehold.co/50x50"}
                alt="cover"
                style={{ width: 50, height: 50, borderRadius: 8 }}
              />
              <Box>
                <Typography variant="h6">{song.name}</Typography>
                <Typography variant="subtitle2">{song.artist}</Typography>
              </Box>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <IconButton onClick={() => handleEdit(song.songs_master_id)}>
                <EditIcon />
              </IconButton>
              <IconButton onClick={() => handleDelete(song.songs_master_id)}>
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>
        ))}

        {/* Add/Edit Song Modal */}
        <Modal open={open} onClose={handleClose}>
          <Box sx={modalStyle}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6">
                {editingIndex !== null ? "Edit Song" : "Add Song"}
              </Typography>
              <IconButton onClick={handleClose}>
                <CloseIcon />
              </IconButton>
            </Box>

            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                label="Song Name"
                name="name"
                value={songData.name}
                onChange={handleChange}
                fullWidth
                error={isDuplicate}
                helperText={isDuplicate ? "This song already exists." : ""}
              />
              <TextField
                label="Album"
                name="album"
                value={songData.album}
                onChange={handleChange}
                fullWidth
              />
              <TextField
                label="Artist"
                name="artist"
                value={songData.artist}
                onChange={handleChange}
                fullWidth
              />
              <TextField
                label="release_year"
                name="release_year"
                value={songData.release_year}
                onChange={handleChange}
              />
              <TextField
                label="Decade"
                name="decade"
                value={songData.decade}
                onChange={handleChange}
              />
              <TextField
                label="Duration"
                name="duration"
                value={songData.duration}
                onChange={handleChange}
                fullWidth
                placeholder="mm:ss"
              />
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Genres
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  placeholder={
                    selectedGenres.length === 0 ? "Select genres" : ""
                  }
                  InputProps={{
                    startAdornment: selectedGenres.length > 0 && (
                      <Box
                        sx={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: 0.5,
                          maxWidth: "calc(100% - 48px)",
                        }}
                      >
                        {selectedGenres.map((genre) => (
                          <Chip
                            key={genre.genre_type_lookup_id}
                            label={genre.genre_type_name}
                            size="small"
                            onDelete={() => {
                              setSelectedGenres((prev) =>
                                prev.filter(
                                  (g) =>
                                    g.genre_type_lookup_id !==
                                    genre.genre_type_lookup_id
                                )
                              );
                              setTempSelectedGenres((prev) =>
                                prev.filter(
                                  (g) =>
                                    g.genre_type_lookup_id !==
                                    genre.genre_type_lookup_id
                                )
                              );
                            }}
                          />
                        ))}
                      </Box>
                    ),
                    endAdornment: (
                      <IconButton
                        onClick={handleOpenGenreModal}
                        edge="end"
                        sx={{
                          position: "absolute",
                          right: "20px",
                        }}
                      >
                        <AddIcon />
                      </IconButton>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      flexWrap: "nowrap",
                      minHeight: "40px",
                      alignItems: "center",
                      paddingRight: "40px",
                    },
                  }}
                />
              </Box>
              {/* Genre Selection Modal */}
              <Modal open={openGenreModal} onClose={handleCloseGenreModal}>
                <Box sx={modalStyle}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mb: 2,
                    }}
                  >
                    <Typography variant="h6">Select Genres</Typography>
                    <IconButton onClick={handleCloseGenreModal}>
                      <CloseIcon />
                    </IconButton>
                  </Box>

                  <Autocomplete
                    multiple
                    options={genres}
                    getOptionLabel={(option) => option.genre_type_name}
                    value={tempSelectedGenres}
                    onChange={(event, newValue) => {
                      setTempSelectedGenres(newValue);
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label="Search genres" fullWidth />
                    )}
                    sx={{ mb: 2 }}
                  />

                  <Button
                    variant="contained"
                    onClick={saveSelectedGenres}
                    fullWidth
                  >
                    Save Genres
                  </Button>
                </Box>
              </Modal>
              <Typography variant="subtitle1">Upload Song Image</Typography>
              <Button variant="outlined" component="label">
                Upload Image
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleImageUpload}
                />
              </Button>
              {songData.image && (
                <img
                  src={songData.image}
                  alt="Preview"
                  style={{ width: "100%", borderRadius: 8 }}
                />
              )}
              <>
                <TextField
                  label="Search Playlists"
                  value={searchPlaylistTerm}
                  onChange={(e) => setSearchPlaylistTerm(e.target.value)}
                  fullWidth
                  margin="normal"
                />

                <Typography variant="caption" sx={{ display: "block", mb: 1 }}>
                  {selectedPlaylists.length} playlist
                  {selectedPlaylists.length !== 1 ? "s" : ""} selected
                </Typography>

                <Box
                  sx={{
                    maxHeight: 250,
                    overflowY: "auto",
                    border: "1px solid #ccc",
                    borderRadius: 2,
                    p: 1,
                  }}
                >
                  {filteredPlaylists.map((playlist) => {
                    return (
                      <Box
                        key={playlist.playlist_master_id}
                        display="flex"
                        justifyContent="space-between"
                        alignItems="center"
                        p={1}
                        borderBottom="1px solid #eee"
                      >
                        <Typography>{playlist.title}</Typography>
                        <Button
                          size="small"
                          variant="contained"
                          color={
                            selectedPlaylists.includes(
                              playlist.playlist_master_id
                            )
                              ? "error"
                              : "primary"
                          }
                          onClick={() =>
                            selectedPlaylists.includes(
                              playlist.playlist_master_id
                            )
                              ? handleRemovePlaylist(playlist)
                              : handleAddPlaylist(playlist)
                          }
                        >
                          {selectedPlaylists.includes(
                            playlist.playlist_master_id
                          )
                            ? "-"
                            : "+"}
                        </Button>
                      </Box>
                    );
                  })}
                </Box>

                {filteredPlaylists.length === 0 && (
                  <Typography variant="body2" sx={{ mt: 1, color: "gray" }}>
                    No playlists found.
                  </Typography>
                )}
              </>
              {/* Song URLs */}
              <Box>
                <Typography variant="subtitle1">Song URLs</Typography>
                <Button variant="outlined" onClick={handleOpenUrlModal}>
                  + Add URL
                </Button>
                {(editingIndex !== null ? songUrls : tempUrls).map(
                  (urlItem, index) => (
                    <Box
                      key={urlItem.song_url_id || index}
                      sx={{ display: "flex", alignItems: "center", mb: 1 }}
                    >
                      <Typography>
                        {urlItem.url_type_name}:{" "}
                        <a
                          href={urlItem.song_url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {urlItem.song_url}
                        </a>
                      </Typography>
                      <IconButton
                        onClick={() =>
                          handleDeleteSongUrl(
                            editingIndex !== null
                              ? urlItem.song_urls_id
                              : index,
                            editingIndex === null
                          )
                        }
                        size="small"
                        sx={{ ml: 1 }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )
                )}

                {(editingIndex !== null
                  ? songUrls.length === 0
                  : tempUrls.length === 0) && (
                  <Typography variant="body2" sx={{ color: "gray" }}>
                    No URLs added for this song.
                  </Typography>
                )}
              </Box>

              <Modal open={openUrlModal} onClose={handleCloseUrlModal}>
                <Box sx={modalStyle}>
                  <Typography variant="h6">Add Song URL</Typography>
                  <TextField
                    select
                    label="URL Type"
                    value={selectedUrlType}
                    onChange={(e) => setSelectedUrlType(e.target.value)}
                    fullWidth
                    margin="normal"
                  >
                    {urlTypes.map((type) => (
                      <MenuItem
                        key={type.url_type_lookup_id}
                        value={type.url_type_name}
                      >
                        {type.url_type_name}
                      </MenuItem>
                    ))}
                  </TextField>
                  <TextField
                    label="URL"
                    value={newUrl}
                    onChange={(e) => setNewUrl(e.target.value)}
                    fullWidth
                    margin="normal"
                  />
                  <Button variant="contained" onClick={handleAddUrl}>
                    Add
                  </Button>
                </Box>
              </Modal>
              <Divider sx={{ my: 2 }} />
              <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                  YouTube Search
                </Typography>
                <FormGroup row sx={{ mb: 1 }}>
                  {urlTypes.map((type) => (
                    <FormControlLabel
                      key={type.url_type_lookup_id}
                      control={
                        <Checkbox
                          checked={!!youtubeSearchTerms[type.url_type_name]}
                          onChange={(e) =>
                            setYouTubeSearchTerms((prev) => ({
                              ...prev,
                              [type.url_type_name]: e.target.checked,
                            }))
                          }
                        />
                      }
                      label={type.url_type_name}
                    />
                  ))}
                </FormGroup>
                <Button
                  variant="outlined"
                  startIcon={<YouTubeIcon />}
                  onClick={() => setOpenYouTubeDialog(true)}
                  sx={{ mb: 1 }}
                >
                  Search YouTube
                </Button>
                <YouTubeSearchAndSaveDialog
                  open={openYouTubeDialog}
                  onClose={() => setOpenYouTubeDialog(false)}
                  song={songData}
                  songs_master_id={selectedSongId}
                  urlTypes={urlTypes}
                  searchTerms={Object.keys(youtubeSearchTerms).filter(
                    (k) => youtubeSearchTerms[k]
                  )}
                  onUrlSaved={() => fetchSongUrls(selectedSongId)}
                  songUrls={songUrls}
                />
              </Paper>
              <Divider sx={{ my: 2 }} />
              {/* Song Files */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1">Song Files</Typography>
                <Button
                  variant="outlined"
                  onClick={handleOpenFileModal}
                  sx={{ mb: 1 }}
                >
                  + Add File
                </Button>
                {(editingIndex !== null ? songFiles : tempFiles).map(
                  (fileItem, index) => (
                    <Box
                      key={fileItem.song_files_id || index}
                      sx={{ display: "flex", alignItems: "center", mb: 1 }}
                    >
                      <Typography>
                        {fileItem.file_type_name} :
                        <a
                          href={`${process.env.REACT_APP_API_URL}/${fileItem.file}`} // Replace <your-port> with your actual port
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ textDecoration: "none", color: "blue" }} // Optional styling
                        >
                          {fileItem.file.split("\\").pop()}{" "}
                          {/* Extract the file name */}
                        </a>
                      </Typography>
                      <IconButton
                        onClick={() =>
                          handleDeleteFile(
                            editingIndex !== null
                              ? fileItem.song_files_id
                              : index,
                            editingIndex === null
                          )
                        }
                        size="small"
                        sx={{ ml: 1 }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )
                )}
                {(editingIndex !== null
                  ? songFiles.length === 0
                  : tempFiles.length === 0) && (
                  <Typography variant="body2" sx={{ color: "gray" }}>
                    No files attached to this song.
                  </Typography>
                )}
              </Box>
              {/* File Modal */}
              <Modal open={openFileModal} onClose={handleCloseFileModal}>
                <Box sx={modalStyle}>
                  <Typography variant="h6">Add Song File</Typography>
                  <TextField
                    select
                    label="File Type"
                    value={selectedFileType}
                    onChange={(e) => setSelectedFileType(e.target.value)}
                    fullWidth
                    margin="normal"
                  >
                    {fileTypes.map((type) => (
                      <MenuItem
                        key={type.file_type_lookup_id}
                        value={type.file_type_name}
                      >
                        {type.file_type_name}
                      </MenuItem>
                    ))}
                  </TextField>
                  <Box sx={{ mt: 2, mb: 2 }}>
                    <Button variant="outlined" component="label">
                      Select File
                      <input type="file" hidden onChange={handleFileUpload} />
                    </Button>
                    {selectedFile && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Selected: {selectedFile.name}
                      </Typography>
                    )}
                  </Box>
                  <Button
                    variant="contained"
                    onClick={handleAddFile}
                    disabled={!selectedFileType || !selectedFile}
                  >
                    Add File
                  </Button>
                </Box>
              </Modal>
              <Button variant="contained" color="success" onClick={handleSave}>
                {editingIndex !== null ? "Update Song" : "Add Song"}
              </Button>
            </Box>
          </Box>
        </Modal>
      </Container>
    </>
  );
};

export default SongScreen;
