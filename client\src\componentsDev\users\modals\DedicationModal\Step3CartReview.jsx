// components/dedication/Step3CartReview.jsx
import {
  Box,
  Typography,
  Button,
  List,
} from "@mui/material";
import { useSongCart } from "../../../../context/SongCartContext";
import CartItem from "./CartItem";
import { getAction } from "../../../../lib/utils";

const Step3CartReview = ({ onAddMoreSongs, onContinue, onBack, onRemoveSong }) => {
  const { cart } = useSongCart();

  return (
    <>
      {cart.length === 0 ? (
        <Typography
          sx={{
            fontWeight: "bold",
            fontSize: "24px",
            textAlign: "center",
            color: "#757575",
            my: 2,
          }}
        >
          Please add a song / Queue a song
        </Typography>
      ) : (
        <List sx={{ width: "100%", mb: 2, maxHeight: "300px", overflowX: "hidden" }}>
          {cart.map((item) => (
            <CartItem
              key={item.songs_master_id}
              item={item}
              onRemove={() => onRemoveSong(item.songs_master_id)}
              showAction={true}
              showDedication={true}
            />
          ))}
        </List>
      )}

      <Box sx={{ my: 1 }}>
        {cart.length < 3 ? (
          <>
            <Typography
              sx={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#757575",
                textAlign: "center",
              }}
            >
              You can queue up to 3 songs,
              <Box
                component="span"
                onClick={onAddMoreSongs}
                sx={{
                  fontWeight: "600",
                  fontSize: "18px",
                  color: "#966300",
                  textAlign: "center",
                  textDecoration: "underline",
                  cursor: "pointer",
                  marginLeft: "5px",
                }}
              >
                Queue Another Song
              </Box>
            </Typography>
          </>
        ) : (
          <Typography
            sx={{
              fontWeight: "600",
              fontSize: "18px",
              color: "#e80b19",
              textAlign: "center",
            }}
          >
            Please remove a song if you want to add another, as you can queue up
            to only three songs.
          </Typography>
        )}
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="outlined"
          onClick={onBack}
          sx={{
            mt: 2,
            width: "100%",
            color: "#976609",
            borderColor: "#976609",
            "&:hover": {
              borderColor: "#b7830e",
            },
          }}
        >
          Back
        </Button>

        <Button
          variant="contained"
          onClick={onContinue}
          disabled={cart.length === 0}
          sx={{
            mt: 2,
            width: "100%",
            backgroundColor: "#976609",
            color: "#ffff",
            "&:hover": {
              backgroundColor: "#b7830e",
            },
            "&.Mui-disabled": {
              backgroundColor: "#aaaaaa",
              color: "#fff",
            },
          }}
        >
          Next
        </Button>
      </Box>
    </>
  );
};

export default Step3CartReview;