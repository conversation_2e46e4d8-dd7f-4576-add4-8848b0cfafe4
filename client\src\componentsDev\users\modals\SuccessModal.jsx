import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  Typography,
  IconButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useSongCart } from '../../../context/SongCartContext';

const SuccessModal = ({ open, onClose, requesterName }) => {
  const { cart } = useSongCart();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      slotProps={{
        backdrop: {
          sx: {
            backdropFilter: "blur(8px)",
            backgroundColor: "rgba(0, 0, 0, 0.4)",
          },
        },
      }}
      sx={{
        px: 2,
        '& .MuiDialog-paper': {
          borderRadius: '12px',
          overflow: 'hidden',
          maxWidth: '500px',
          width: '100%'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#000000",
          color: "#FFFFFF",
          fontFamily: "Nunito",
          fontSize: "32px",
          fontWeight: 700,
          lineHeight: "40px",
          letterSpacing: "0.2px",
          padding: "16px 24px",
          margin: 0
        }}
      >
        Success!
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: "#FFFFFF",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          backgroundColor: "#000000",
          color: "#FFFFFF",
          padding: "24px",
          display: "flex",
          flexDirection: "column",
          textAlign: "center",
        }}
      >
        <CheckCircleIcon sx={{ fontSize: 50, color: "#16a34a", alignSelf: 'center', mb: 2 }} />

        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            fontSize: "24px",
            lineHeight: "28px",
            letterSpacing: "0.2px",
            mb: 1,
          }}
        >
          Request Sent
        </Typography>

        <Typography
          sx={{
            fontWeight: 600,
            fontSize: "16px",
            lineHeight: "24px",
            letterSpacing: "0.2px",
            mb: 3,
          }}
        >
          Thank you{" "}
          <span style={{ color: "#976609" }}>{requesterName ? requesterName : ""}</span>, your
          song request has been sent successfully!
          <br />
          Enjoy your music!
        </Typography>

        {/* <Typography
          sx={{
            fontWeight: 600,
            fontSize: "16px",
            lineHeight: "24px",
            letterSpacing: "0.2px",
            alignSelf: 'flex-start',
            mb: 1
          }}
        >
          Songs queued:
        </Typography>
        {console.log(cart)}

        <List dense sx={{
          maxHeight: '200px',
          overflow: 'auto',
          border: '1px solid #333',
          borderRadius: 1,
          py: 0,
          mb: 3
        }}>
          {cart.map((song, index) => (
            <ListItem key={index} sx={{ px: 1, py: 0.5, backgroundColor: '#121212' }}>
              <ListItemAvatar sx={{ minWidth: '40px', marginRight: '12px' }}>
                <Avatar alt={song.name} src={song.image} sx={{ width: 50, height: 50 }} />
              </ListItemAvatar>
              <ListItemText
                primary={song.name}
                secondary={`by ${song.artist}`}
                primaryTypographyProps={{
                  fontSize: '0.9rem',
                  fontWeight: 'medium',
                  noWrap: true,
                  color: 'white'
                }}
                secondaryTypographyProps={{
                  fontSize: '0.8rem',
                  noWrap: true,
                  color: 'rgba(255,255,255,0.7)'
                }}
              />
            </ListItem>
          ))}
        </List> */}

        <Button
          onClick={onClose}
          variant="contained"
          sx={{
            backgroundColor: "#976609",
            color: "#FFFFFF",
            fontWeight: 600,
            fontSize: "16px",
            lineHeight: "24px",
            letterSpacing: "0.2px",
            borderRadius: "8px",
            py: 1.5,
            textTransform: "none",
            "&:hover": {
              backgroundColor: "#b5840f",
            },
          }}
        >
          Close
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessModal;