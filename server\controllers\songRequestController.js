const { isObjectIdOrHexString } = require("mongoose");
const SongRequestModel = require("../models/songRequestModel"); // Import your SongRequest model
const SongModel = require("./../models/songModel");

// Create a new song request
const createSongRequest = async (req, res) => {
  const { requester_name, device_id, songs } = req.body;

  if (!device_id || !songs || !Array.isArray(songs) || songs.length === 0) {
    return res.status(400).json({ message: "Invalid request data" });
  }

  try {
    const io = req.app.get("io"); // Get socket instance

    const results = await Promise.all(
      songs.map(async (song) => {
        if (!song.songs_master_id || !song.action_type) {
          throw new Error("Missing required song fields");
        }

        const result = await SongRequestModel.create({
          songs_master_id: song.songs_master_id,
          requester_name: requester_name || "Anonymous",
          dedication_msg: song.dedication_msg || "No dedication message",
          status: "Pending",
          action_type: song.action_type,
          device_id: device_id,
        });

         let songDetails = {};
        try {
          songDetails = await SongModel.getById(song.songs_master_id);
        } catch (err) {
          console.error("❌ Failed to fetch song details:", err.message);
        }

        const newRequest = {
          song_request_id: result.id,
          songs_master_id: song.songs_master_id,
          requester_name: requester_name || "Anonymous",
          dedication_msg: song.dedication_msg || "No dedication message",
          status: "Pending",
          displayStatus: "Song-Q",
          action_type: song.action_type,
          created_at: new Date().toISOString(),
          song_name: songDetails?.name || "Unknown Song",
          artist: songDetails?.artist || "Unknown Artist",
          release_year: songDetails?.release_year || null,
        };
        io.emit("new-song-request", newRequest);

        return newRequest;
      })
    );

    res.status(201).json(results);
  } catch (error) {
    console.error("Failed to create song request:", error);
    res.status(500).json({
      message: "Failed to create song request",
      error: error.message,
    });
  }
};

// Retrieve all song requests
const getAllSongRequests = async (req, res) => {
  try {
    const songRequests = await SongRequestModel.getAll();
    res.status(200).json(songRequests);
  } catch (error) {
    console.error("Failed to fetch song requests:", error);
    res.status(500).json({ message: "Failed to fetch song requests" });
  }
};

// Update the status of a specific song request
const updateSongRequestStatus = async (req, res) => {
  const { requestId } = req.params;
  const { status } = req.body;

  if (!status) {
    return res.status(400).json({ message: "Status update required" });
  }

  try {
    const updatedRequest = await SongRequestModel.updateStatus(
      requestId,
      status
    );
    if (!updatedRequest) {
      return res.status(404).json({ message: "Song request not found" });
    }
    res.json(updatedRequest);
  } catch (error) {
    console.error("Failed to update song request:", error);
    res.status(500).json({ message: "Failed to update song request" });
  }
};

const deleteSongRequest = async (req, res) => {
  const { id } = req.params;

  try {
    const deletedRequest = await SongRequestModel.delete(id);
    if (deletedRequest.deleted === 0) {
      return res.status(404).json({ message: "Song request not found" });
    }
    res.json({ message: "Song request deleted successfully" });
  } catch (error) {
    console.error("Failed to delete song request:", error);
    res.status(500).json({ message: "Failed to delete song request" });
  }
};

// Check request limit for device_id
const checkRequestLimit = async (req, res) => {
  const { device_id } = req.query;

  if (!device_id) {
    return res.status(400).json({ message: "device_id is required" });
  }

  try {
    const limitReached = await SongRequestModel.checkRequestLimit(device_id);
    res.json({ limitReached });
  } catch (error) {
    console.error("Failed to check request limit:", error);
    res.status(500).json({ message: "Error checking request limit" });
  }
};

// Get active song requests for a specific device
const getActiveSongRequests = async (req, res) => {
  const { device_id } = req.query;

  if (!device_id) {
    return res.status(400).json({ message: "device_id is required" });
  }

  try {
    const activeRequests = await SongRequestModel.getActiveSongRequests(
      device_id
    );
    res.json(activeRequests);
  } catch (error) {
    console.error("Failed to fetch active song requests:", error);
    res.status(500).json({ message: "Error fetching active song requests" });
  }
};

const getUnfulfilledSongRequests = async (req, res) => {
  try {
    const unfulfilledRequests = await SongRequestModel.getUnfulfilledRequests();
    res.status(200).json(unfulfilledRequests);
  } catch (error) {
    console.error("Failed to fetch unfulfilled song requests:", error);
    res
      .status(500)
      .json({ message: "Failed to fetch unfulfilled song requests" });
  }
};

module.exports = {
  createSongRequest,
  getAllSongRequests,
  updateSongRequestStatus,
  deleteSongRequest,
  checkRequestLimit,
  getActiveSongRequests,
  getUnfulfilledSongRequests,
};
