// components/dedication/Step1ActionSelection.jsx
import {
  <PERSON><PERSON><PERSON>,
  Button,
  FormControlLabel,
  Radio,
  RadioGroup,
  Divider,
} from "@mui/material";
import SongHeader from "./SongHeader";
import { useState, useEffect } from "react";

const Step1ActionSelection = ({ song, onActionSelect, requestMode }) => {
  const [selectedOption, setSelectedOption] = useState("");

  useEffect(() => {
    if (requestMode === "jukebox") {
      setSelectedOption("singForMe");
    } else if (requestMode === "openmic") {
      setSelectedOption("singWithYou");
    } else {
      // For "default" mode, set the first available option
      setSelectedOption("singForMe");
    }
  }, [requestMode]);

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
  };

  const handleContinue = () => {
    onActionSelect(selectedOption);
  };

  // Get available options based on requestMode
  const getAvailableOptions = () => {
    const options = [];

    if (requestMode === "jukebox" || requestMode === "default") {
      options.push({ value: "singForMe", label: "Sing this song for me" });
    }

    if (requestMode === "openmic" || requestMode === "default") {
      options.push(
        { value: "singWithYou", label: "I would like to sing this song with you" },
        { value: "singAlone", label: "I would like to sing this song solo" }
      );
    }

    return options;
  };

  const availableOptions = getAvailableOptions();

  return (
    <>
      <SongHeader song={song} />
      <Divider sx={{ color: "#202020", height: "1px" }} />

      <Typography
        sx={{
          fontSize: "16px",
          fontWeight: "600",
          lineHeight: "16px",
          letterSpacing: "0.20000000298023224px",
          textAlign: "left",
          color: "#F9F9F9",
          my: 2,
        }}
      >
        What would you like?
      </Typography>

      <RadioGroup
        value={selectedOption}
        onChange={handleOptionChange}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        {availableOptions.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={
              <Radio
                sx={{
                  color: "#976609",
                  "&, &.Mui-checked": {
                    color: "#976609",
                  },
                }}
              />
            }
            label={
              <Typography
                variant="body1"
                sx={{
                  color: "#FFFFFF",
                  textTransform: "none",
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "20px",
                }}
              >
                {option.label}
              </Typography>
            }
          />
        ))}
      </RadioGroup>

      <Button
        onClick={handleContinue}
        variant="contained"
        disabled={!selectedOption}
        sx={{
          width: "100%",
          height: "40px",
          backgroundColor: "#966300",
          color: "#ffff",
          marginTop: "24px",
          textTransform: "uppercase",
          "&:disabled": {
            backgroundColor: "#555555",
            color: "#999999",
          },
        }}
      >
        Continue
      </Button>
    </>
  );
};

export default Step1ActionSelection;