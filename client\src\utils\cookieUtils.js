// utils/cookieUtils.js
export const setCookie = (name, value, days = 7) => {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${name}=${value};${expires};path=/`;
};

export const getCookie = (name) => {
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === " ") c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
};

export const generateUniqueId = () => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

// export const getDeviceId = () => {
//   let deviceId = getCookie('deviceId');
//   if (!deviceId) {
//     deviceId = generateUniqueId();
//     setCookie('deviceId', deviceId);
//   }
//   return deviceId;
// };

// utils/cookieUtils.js
export const setDeviceId = () => {
  const deviceId = crypto.randomUUID(); // or any other unique ID generation method
  const expiration = new Date();
  expiration.setHours(expiration.getHours() + 1); // 1 hour expiration
  document.cookie = `device_id=${deviceId}; expires=${expiration.toUTCString()}; path=/`;
  return deviceId;
};

export const getDeviceId = () => {
  const cookies = document.cookie.split(";").map((cookie) => cookie.trim());
  const deviceCookie = cookies.find((cookie) =>
    cookie.startsWith("device_id=")
  );
  if (deviceCookie) {
    return deviceCookie.split("=")[1];
  }
  return setDeviceId(); // Create new ID if not found
};