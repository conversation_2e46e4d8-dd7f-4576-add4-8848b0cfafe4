import { useApi } from './useApi';

export const usePlaylists = (options = {}) => {
  return useApi(
    'playlists',
    `${process.env.REACT_APP_API_URL}/api/playlist`,
    {
      select: (data) => data.filter((p) => p.songs_count > 0),
      ...options,
    }
  );
};

export const usePlaylistDetails = (id, options = {}) => {
  return useApi(
    ['playlist', id],
    `${process.env.REACT_APP_API_URL}/api/playlist/${id}`,
    options
  );
};

export const usePlaylistSongs = (playlistId, options = {}) => {
  return useApi(
    ['playlistSongs', playlistId],
    `${process.env.REACT_APP_API_URL}/api/playlist-songs/playlist/${playlistId}/songs`,
    options
  );
};

export const useActiveRequests = (deviceId, options = {}) => {
  return useApi(
    ['activeRequests', deviceId],
    `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`,
    {
      refetchInterval: 30 * 1000, // Refetch every 30 seconds
      ...options,
    }
  );
};