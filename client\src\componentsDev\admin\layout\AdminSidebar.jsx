import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const AdminSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { path: '/admin/dashboard', label: 'Dashboard', icon: '📊' },
    { path: '/admin/requests', label: 'Song Requests', icon: '🎵' },
    { path: '/admin/songs', label: 'Songs', icon: '🎶' },
    { path: '/admin/playlists', label: 'Playlists', icon: '📝' },
    { path: '/admin/artists', label: 'Artists', icon: '🎤' },
    { path: '/admin/quiz', label: 'Quiz', icon: '❓' },
    { path: '/admin/showdown', label: 'Showdown', icon: '⚔️' },
    { path: '/admin/imported-songs', label: 'Imported Songs', icon: '📥' },
  ];

  const isActive = (path) => location.pathname === path;

  const handleMenuClick = (path) => {
    navigate(path);
  };

  return (
    <aside style={{
      width: '250px',
      backgroundColor: '#f5f5f5',
      borderRight: '1px solid #e0e0e0',
      height: 'calc(100vh - 80px)',
      overflowY: 'auto'
    }}>
      <nav style={{ padding: '1rem 0' }}>
        {menuItems.map((item) => (
          <div
            key={item.path}
            onClick={() => handleMenuClick(item.path)}
            style={{
              padding: '1rem 1.5rem',
              cursor: 'pointer',
              backgroundColor: isActive(item.path) ? '#e3f2fd' : 'transparent',
              borderLeft: isActive(item.path) ? '4px solid #1976d2' : '4px solid transparent',
              color: isActive(item.path) ? '#1976d2' : '#333',
              fontWeight: isActive(item.path) ? '600' : '400',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}
          >
            <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
            {item.label}
          </div>
        ))}
      </nav>
    </aside>
  );
};

export default AdminSidebar;
