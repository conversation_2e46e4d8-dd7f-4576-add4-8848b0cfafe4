import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Grid
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import axios from 'axios';
import { 
  QuizDashboard, 
  QuizQuestionForm, 
  QuizQuestionManagement,
  QuizSettings,
  useQuizQuestions,
  useCreateQuizQuestion
} from './index';

// Styled components
const TestContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#121212',
  minHeight: '100vh',
  padding: '24px',
}));

const TestCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  marginBottom: '16px',
}));

const TestButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  backgroundColor: '#4CAF50',
  color: '#fff',
  '&:hover': {
    backgroundColor: '#45a049',
  },
}));

const QuizTestPage = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [testResults, setTestResults] = useState({});
  const [activeTest, setActiveTest] = useState(null);

  // API hooks for testing
  const { data: questions, isLoading: questionsLoading, error: questionsError } = useQuizQuestions();
  const createMutation = useCreateQuizQuestion();

  const runTest = async (testName, testFunction) => {
    setActiveTest(testName);
    try {
      const result = await testFunction();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, message: result.message, data: result.data }
      }));
      enqueueSnackbar(`✅ ${testName} passed`, { variant: 'success' });
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, message: error.message, error }
      }));
      enqueueSnackbar(`❌ ${testName} failed: ${error.message}`, { variant: 'error' });
    } finally {
      setActiveTest(null);
    }
  };

  const tests = {
    'API Connection Test': async () => {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/admin/questions`);
      return {
        message: `Successfully connected to API. Found ${response.data.length} questions.`,
        data: response.data
      };
    },

    'Lookup Data Test': async () => {
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`)
      ]);

      return {
        message: `Lookup data loaded: ${difficulties.data.length} difficulties, ${questionTypes.data.length} question types, ${answerTypes.data.length} answer types`,
        data: { difficulties: difficulties.data, questionTypes: questionTypes.data, answerTypes: answerTypes.data }
      };
    },

    'Status Filtering Test': async () => {
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`)
      ]);

      // Filter to only active items (status = 1)
      const activeDifficulties = difficulties.data.filter(item => item.status === 1);
      const activeQuestionTypes = questionTypes.data.filter(item => item.status === 1);
      const activeAnswerTypes = answerTypes.data.filter(item => item.status === 1);

      const totalItems = difficulties.data.length + questionTypes.data.length + answerTypes.data.length;
      const activeItems = activeDifficulties.length + activeQuestionTypes.length + activeAnswerTypes.length;

      if (activeItems === 0) {
        throw new Error('No active lookup items found. Please activate some items in the lookup management.');
      }

      return {
        message: `Status filtering working: ${activeItems}/${totalItems} items are active`,
        data: {
          activeDifficulties: activeDifficulties.length,
          activeQuestionTypes: activeQuestionTypes.length,
          activeAnswerTypes: activeAnswerTypes.length,
          totalActive: activeItems,
          totalItems: totalItems
        }
      };
    },

    'Songs API Test': async () => {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/songs`);
      return {
        message: `Songs API working. Found ${response.data.length} songs.`,
        data: response.data
      };
    },

    'Answer Type Independence Test': async () => {
      // Get active lookup data
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`)
      ]);

      const activeDifficulties = difficulties.data.filter(item => item.status === 1);
      const activeQuestionTypes = questionTypes.data.filter(item => item.status === 1);
      const activeAnswerTypes = answerTypes.data.filter(item => item.status === 1);

      // Test that answer types are independent of question types
      const expectedAnswerTypes = ['multiple_choice', 'single_choice', 'true_false'];
      const availableAnswerTypes = activeAnswerTypes.map(at => at.type_name);
      const hasRequiredTypes = expectedAnswerTypes.every(type => availableAnswerTypes.includes(type));

      return {
        message: `Answer type independence working: ${activeAnswerTypes.length} answer types available, required types ${hasRequiredTypes ? 'present' : 'missing'}`,
        data: {
          availableAnswerTypes,
          expectedAnswerTypes,
          hasRequiredTypes,
          activeQuestionTypes: activeQuestionTypes.length,
          activeAnswerTypes: activeAnswerTypes.length
        }
      };
    },

    'Question Creation Test': async () => {
      // Get first active difficulty and question type for test
      const [difficulties, questionTypes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/difficulty`),
        axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/question-type`)
      ]);

      const activeDifficulties = difficulties.data.filter(item => item.status === 1);
      const activeQuestionTypes = questionTypes.data.filter(item => item.status === 1);

      if (activeDifficulties.length === 0 || activeQuestionTypes.length === 0) {
        throw new Error('No active difficulties or question types available for testing');
      }

      // Get active answer types for testing
      const answerTypes = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/answer-type`);
      const activeAnswerTypes = answerTypes.data.filter(item => item.status === 1);

      if (activeAnswerTypes.length === 0) {
        throw new Error('No active answer types available for testing');
      }

      const testQuestion = {
        songs_master_id: 1,
        question_text: "Test question created by automated test with independent answer type selection",
        difficulty_id: activeDifficulties[0].difficulty_id,
        time_limit_seconds: 30,
        answer_type: activeAnswerTypes[0].type_name, // Use answer type instead of question type
        answers: [
          { answer_text: "Test Answer 1", is_correct: true, answer_order: 1 },
          { answer_text: "Test Answer 2", is_correct: false, answer_order: 2 }
        ]
      };

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/admin/questions`,
        testQuestion
      );

      return {
        message: `Question created successfully with ID: ${response.data.question_id} using active lookup data`,
        data: response.data
      };
    },

    'Question Stats Test': async () => {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/admin/questions/stats`);
      return {
        message: `Stats retrieved: ${response.data.total_questions} total questions`,
        data: response.data
      };
    },

    'Settings API Test': async () => {
      // Test getting settings
      const getResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/admin/questions/settings`);
      
      // Test saving settings
      const testSettings = {
        defaultTimeLimit: 45,
        defaultPoints: 15,
        testMode: true
      };
      
      await axios.post(`${process.env.REACT_APP_API_URL}/api/admin/questions/settings`, testSettings);
      
      return {
        message: 'Settings API working correctly',
        data: { get: getResponse.data, saved: testSettings }
      };
    },

    'React Query Hooks Test': async () => {
      if (questionsLoading) {
        throw new Error('Questions still loading');
      }
      
      if (questionsError) {
        throw new Error(`Questions error: ${questionsError.message}`);
      }

      return {
        message: `React Query hooks working. Loaded ${questions?.length || 0} questions.`,
        data: questions
      };
    }
  };

  const runAllTests = async () => {
    for (const [testName, testFunction] of Object.entries(tests)) {
      await runTest(testName, testFunction);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getTestStatus = (testName) => {
    const result = testResults[testName];
    if (!result) return 'pending';
    return result.success ? 'success' : 'error';
  };

  const getTestIcon = (testName) => {
    const status = getTestStatus(testName);
    if (status === 'success') return <CheckIcon sx={{ color: '#4CAF50' }} />;
    if (status === 'error') return <ErrorIcon sx={{ color: '#F44336' }} />;
    return <InfoIcon sx={{ color: '#2196F3' }} />;
  };

  return (
    <TestContainer>
      <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 3 }}>
        Quiz Components Test Suite
      </Typography>

      <Grid container spacing={3}>
        {/* Test Controls */}
        <Grid item xs={12} md={4}>
          <TestCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                Test Controls
              </Typography>
              
              <Stack spacing={2}>
                <TestButton
                  fullWidth
                  onClick={runAllTests}
                  disabled={activeTest !== null}
                  startIcon={<PlayIcon />}
                >
                  {activeTest ? `Running: ${activeTest}` : 'Run All Tests'}
                </TestButton>
                
                <Divider sx={{ borderColor: '#333' }} />
                
                {Object.keys(tests).map((testName) => (
                  <Button
                    key={testName}
                    variant="outlined"
                    fullWidth
                    onClick={() => runTest(testName, tests[testName])}
                    disabled={activeTest !== null}
                    startIcon={getTestIcon(testName)}
                    sx={{
                      borderColor: '#333',
                      color: '#ccc',
                      '&:hover': {
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                      },
                    }}
                  >
                    {testName}
                  </Button>
                ))}
              </Stack>
            </CardContent>
          </TestCard>
        </Grid>

        {/* Test Results */}
        <Grid item xs={12} md={8}>
          <TestCard>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#fff', mb: 2 }}>
                Test Results
              </Typography>
              
              {Object.keys(testResults).length === 0 ? (
                <Alert severity="info" sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)' }}>
                  No tests run yet. Click "Run All Tests" to start.
                </Alert>
              ) : (
                <Stack spacing={2}>
                  {Object.entries(testResults).map(([testName, result]) => (
                    <Box
                      key={testName}
                      sx={{
                        backgroundColor: '#121212',
                        border: `1px solid ${result.success ? '#4CAF50' : '#F44336'}`,
                        borderRadius: '8px',
                        p: 2,
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        {getTestIcon(testName)}
                        <Typography variant="body1" sx={{ color: '#fff', fontWeight: 600 }}>
                          {testName}
                        </Typography>
                        <Chip
                          label={result.success ? 'PASS' : 'FAIL'}
                          size="small"
                          sx={{
                            backgroundColor: result.success ? '#4CAF50' : '#F44336',
                            color: '#fff',
                          }}
                        />
                      </Box>
                      
                      <Typography variant="body2" sx={{ color: '#ccc', mb: 1 }}>
                        {result.message}
                      </Typography>
                      
                      {result.data && (
                        <Box
                          sx={{
                            backgroundColor: '#0A0A0A',
                            border: '1px solid #333',
                            borderRadius: '4px',
                            p: 1,
                            fontFamily: 'monospace',
                            fontSize: '12px',
                            color: '#ccc',
                            maxHeight: '100px',
                            overflow: 'auto',
                          }}
                        >
                          <pre>{JSON.stringify(result.data, null, 2)}</pre>
                        </Box>
                      )}
                    </Box>
                  ))}
                </Stack>
              )}
            </CardContent>
          </TestCard>
        </Grid>
      </Grid>

      {/* Component Previews */}
      <Box mt={4}>
        <Typography variant="h5" sx={{ color: '#fff', mb: 2, fontWeight: 600 }}>
          Component Integration Status
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <TestCard>
              <CardContent sx={{ textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" sx={{ color: '#fff' }}>
                  QuizDashboard
                </Typography>
                <Typography variant="body2" sx={{ color: '#ccc' }}>
                  ✅ Complete
                </Typography>
              </CardContent>
            </TestCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TestCard>
              <CardContent sx={{ textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" sx={{ color: '#fff' }}>
                  QuizQuestionForm
                </Typography>
                <Typography variant="body2" sx={{ color: '#ccc' }}>
                  ✅ Complete
                </Typography>
              </CardContent>
            </TestCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TestCard>
              <CardContent sx={{ textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" sx={{ color: '#fff' }}>
                  QuizManagement
                </Typography>
                <Typography variant="body2" sx={{ color: '#ccc' }}>
                  ✅ Complete
                </Typography>
              </CardContent>
            </TestCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <TestCard>
              <CardContent sx={{ textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" sx={{ color: '#fff' }}>
                  QuizSettings
                </Typography>
                <Typography variant="body2" sx={{ color: '#ccc' }}>
                  ✅ Complete
                </Typography>
              </CardContent>
            </TestCard>
          </Grid>
        </Grid>
      </Box>
    </TestContainer>
  );
};

export default QuizTestPage;
