import React, { useState } from "react";
import {
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  Button,
  Divider,
  Box,
  Pagination,
  Typography,
} from "@mui/material";

function SongList({ songs, onSongSelect }) {
  const itemsPerPage = 10;
  const [page, setPage] = useState(1);
  const count = Math.ceil(songs.length / itemsPerPage);

  const handleChange = (event, value) => {
    setPage(value);
  };

  const startIndex = (page - 1) * itemsPerPage;
  const selectedSongs = songs.slice(startIndex, startIndex + itemsPerPage);

  return (
    <React.Fragment>
      <List>
        {selectedSongs.map((song, index) => (
          <React.Fragment key={song.songs_master_id}>
            <ListItem
              sx={{
                bgcolor: "background.paper",
                my: 1,
                borderRadius: "4px",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "10px",
              }}
            >
              <ListItemAvatar sx={{ marginRight: 2 }}>
                <Avatar
                  variant="square"
                  src={song.image}
                  alt={song.name}
                  sx={{ width: 100, height: 100 }}
                />
              </ListItemAvatar>
              <Box sx={{ flex: 1, overflow: "hidden", mr: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: "bold", color: "text.primary" }}>
                  {song.name}
                </Typography>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  by {song.artist}
                </Typography>
                
              </Box>
              <Button
                variant="outlined"
                sx={{
                  borderColor: "grey.700",
                  color: "text.primary",
                  "&:hover": {
                    backgroundColor: "grey.800",
                    borderColor: "grey.700",
                  },
                  ml: 2,
                }}
                onClick={() => onSongSelect(song)}
              >
                Make Request
              </Button>
            </ListItem>
            {index !== selectedSongs.length - 1 && (
              <Divider
                variant="inset"
                component="li"
                sx={{ marginLeft: "72px" }}
              />
            )}
          </React.Fragment>
        ))}
      </List>
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
        <Pagination count={count} page={page} onChange={handleChange} />
      </Box>
    </React.Fragment>
  );
}

export default SongList;