const ImportSpotifyModel = require("../models/importSpotifyModel");
const SongModel = require("../models/songModel");

exports.addSpotifyTracks = async (req, res) => {
  const { import_song_id, tracks } = req.body;

  if (!import_song_id || !Array.isArray(tracks)) {
    return res.status(400).json({
      error: "import_song_id and tracks[] are required",
    });
  }

  if (tracks.length === 0) {
    return res.status(400).json({
      error: "At least one track is required",
    });
  }

  try {
    // Validate tracks data
    for (const track of tracks) {
      if (!track.id || !track.name || !track.artist) {
        return res.status(400).json({
          error: "Each track must have id, name, and artist fields",
        });
      }
    }

    const inserted = await ImportSpotifyModel.addTracks(import_song_id, tracks);
    res.status(200).json({
      message: "Spotify tracks added successfully",
      inserted: inserted.length,
      tracks: inserted,
    });
  } catch (err) {
    console.error("❌ Add Spotify tracks error:", err);
    res.status(500).json({ error: "Failed to add Spotify tracks" });
  }
};

exports.addSpotifyArtists = async (req, res) => {
  const { artists } = req.body;

  if (!Array.isArray(artists)) {
    return res.status(400).json({ message: "Artists array is required." });
  }

  try {
    for (const artist of artists) {
      await SongModel.saveArtist({
        id: artist.id,
        name: artist.name,
        image: artist.image,
      });
    }

    res.status(200).json({ message: "✅ Artists saved successfully." });
  } catch (error) {
    console.error("❌ Error saving artists:", error);
    res.status(500).json({ message: "Failed to save artists." });
  }
};

exports.getSpotifyTracks = async (req, res) => {
  const { import_song_id } = req.params;

  if (!import_song_id) {
    return res.status(400).json({ error: "import_song_id is required" });
  }

  try {
    const tracks = await ImportSpotifyModel.getTracksByImportSongId(
      import_song_id
    );
    res.status(200).json(tracks);
  } catch (err) {
    console.error("❌ Get Spotify tracks error:", err);
    res.status(500).json({ error: "Failed to fetch Spotify tracks" });
  }
};

exports.getSpotifyArtists = async (req, res) => {
  const { import_song_id } = req.params;

  if (!import_song_id) {
    return res.status(400).json({ error: "import_song_id is required" });
  }

  try {
    const artists = await ImportSpotifyModel.getArtistsByImportSongId(
      import_song_id
    );
    res.status(200).json(artists);
  } catch (err) {
    console.error("❌ Get Spotify artists error:", err);
    res.status(500).json({ error: "Failed to fetch Spotify artists" });
  }
};

exports.deleteSpotifyTracks = async (req, res) => {
  const { import_song_id } = req.params;

  if (!import_song_id) {
    return res.status(400).json({ error: "import_song_id is required" });
  }

  try {
    const result = await ImportSpotifyModel.deleteTracksByImportSongId(
      import_song_id
    );
    res.status(200).json({
      message: "Spotify tracks deleted successfully",
      deleted: result.changes,
    });
  } catch (err) {
    console.error("❌ Delete Spotify tracks error:", err);
    res.status(500).json({ error: "Failed to delete Spotify tracks" });
  }
};

exports.deleteSpotifyArtists = async (req, res) => {
  const { import_song_id } = req.params;

  if (!import_song_id) {
    return res.status(400).json({ error: "import_song_id is required" });
  }

  try {
    const result = await ImportSpotifyModel.deleteArtistsByImportSongId(
      import_song_id
    );
    res.status(200).json({
      message: "Spotify artists deleted successfully",
      deleted: result.changes,
    });
  } catch (err) {
    console.error("❌ Delete Spotify artists error:", err);
    res.status(500).json({ error: "Failed to delete Spotify artists" });
  }
};

exports.deleteSpotifyTrack = async (req, res) => {
  const { track_id } = req.params;

  if (!track_id) {
    return res.status(400).json({ error: "track_id is required" });
  }

  try {
    const result = await ImportSpotifyModel.deleteTrack(track_id);

    if (result.changes === 0) {
      return res.status(404).json({ error: "Track not found" });
    }

    res.status(200).json({
      message: "Spotify track deleted successfully",
      deleted: result.changes,
    });
  } catch (err) {
    console.error("❌ Delete Spotify track error:", err);
    res.status(500).json({ error: "Failed to delete Spotify track" });
  }
};

exports.deleteSpotifyArtist = async (req, res) => {
  const { artist_id } = req.params;

  if (!artist_id) {
    return res.status(400).json({ error: "artist_id is required" });
  }

  try {
    const result = await ImportSpotifyModel.deleteArtist(artist_id);

    if (result.changes === 0) {
      return res.status(404).json({ error: "Artist not found" });
    }

    res.status(200).json({
      message: "Spotify artist deleted successfully",
      deleted: result.changes,
    });
  } catch (err) {
    console.error("❌ Delete Spotify artist error:", err);
    res.status(500).json({ error: "Failed to delete Spotify artist" });
  }
};

exports.getAllSpotifyTracks = async (req, res) => {
  try {
    const db = require("../models/importSpotifyModel");
    const tracks = await db.getAllTracks();
    res.status(200).json(tracks);
  } catch (err) {
    console.error("Get all tracks error:", err);
    res.status(500).json({ error: "Failed to fetch all tracks" });
  }
};

exports.updateSpotifyTrack = async (req, res) => {
  const { id } = req.params;
  const { name, artist, album, release_year, duration } = req.body;

  try {
    const result = await ImportSpotifyModel.updateTrack({
      id,
      name,
      artist,
      album,
      release_year,
      duration,
    });

    if (result.changes === 0) {
      return res.status(404).json({ error: "Track not found" });
    }

    res.status(200).json({ message: "Track updated successfully" });
  } catch (err) {
    console.error("Update track error:", err);
    res.status(500).json({ error: "Failed to update track" });
  }
};
