import React, { useState, useEffect } from "react";
import {
    Grid,
    Container,
    Box,
    CircularProgress,
    Button,
    Typography,
} from "@mui/material";
// import RequestHandler from "./RequestHandler";
import { useSongCart } from "../../../../context/SongCartContext";
import SongCard from "../../common/SongCard";
import { useActiveRequests } from "../../../hooks/useActiveRequests";
import DedicationMaster from "../../modals/DedicationModal/DedicationMaster";
import SuccessModal from "../../modals/SuccessModal";
import ThankYouDialog from "../../modals/ThankDialog";
import { useSnackbar } from "notistack";
import { getDeviceId } from "../../../../utils/cookieUtils";
import { useQueryClient } from "@tanstack/react-query";

const INITIAL_SONGS_COUNT = 20;
const SONGS_PER_PAGE = 10;

const SongsListSection = ({ songs = [], isLoading }) => {
    const [visibleSongs, setVisibleSongs] = useState([]);
    const [visibleCount, setVisibleCount] = useState(INITIAL_SONGS_COUNT);
    const [loadingMore, setLoadingMore] = useState(false);
    const { cart, clearCart } = useSongCart();
    const [selectedSong, setSelectedSong] = useState(null);
    const [showDedicationModal, setShowDedicationModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showThankYouDialog, setShowThankYouDialog] = useState(false);
    const [requesterName, setRequesterName] = useState("");
    const { enqueueSnackbar } = useSnackbar();
    const queryClient = useQueryClient();

    const {
        data: activeRequests = {},
        refetch: refetchActiveRequests
    } = useActiveRequests();

    // Initialize visible songs when songs data changes
    useEffect(() => {
        if (songs && songs.length > 0) {
            setVisibleSongs(songs.slice(0, INITIAL_SONGS_COUNT));
            setVisibleCount(INITIAL_SONGS_COUNT);
        }
    }, [songs]);

    const handleRequestSuccess = () => {
        // Invalidate and refetch active requests
        queryClient.invalidateQueries({ queryKey: ['activeRequests'] });
    };


    const handleRequestOpen = (song) => {
        setSelectedSong(song);
        setShowDedicationModal(true);
    };

    const handleSubmitRequest = async (dedicationData) => {
        try {
            const deviceId = getDeviceId();
            const { userName, songs } = dedicationData;

            // Validate the data structure
            if (!songs || !Array.isArray(songs) || songs.length === 0) {
                console.error("Invalid songs data received:", dedicationData);
                enqueueSnackbar("Invalid request data. Please try again.", {
                    variant: "error",
                });
                return;
            }

            const finalUserName = userName || "Anonymous";
            setRequesterName(finalUserName);

            // Check for existing active requests for all songs
            for (const song of songs) {
                const canRequestResponse = await fetch(
                    `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${song.songs_master_id}`
                );

                const canRequestData = await canRequestResponse.json();

                if (canRequestData.hasActiveRequest) {
                    enqueueSnackbar(`You already have an active request for song ID ${song.songs_master_id}`, {
                        variant: "warning",
                    });
                    return;
                }
            }

            // Submit the request for all songs
            const requestBody = {
                requester_name: finalUserName,
                device_id: deviceId,
                songs: songs.map(song => ({
                    songs_master_id: song.songs_master_id,
                    dedication_msg: song.dedication || "",
                    action_type: song.actionType,
                })),
            };

            const response = await fetch(
                `${process.env.REACT_APP_API_URL}/api/song-requests`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(requestBody),
                }
            );

            if (!response.ok) {
                throw new Error("Failed to submit request");
            }

            await response.json();
            setShowDedicationModal(false);
            setShowSuccessModal(true);
            handleRequestSuccess();

        } catch (error) {
            console.error("Failed to make request:", error);
            enqueueSnackbar("Failed to submit request. Please try again.", {
                variant: "error",
            });
        }
    };

    const handleSuccessModalClose = () => {
        setShowSuccessModal(false);
        setShowThankYouDialog(true);
    };

    const handleThankYouClose = () => {
        setShowThankYouDialog(false);
        clearCart();
    };


    const handleLoadMore = () => {
        setLoadingMore(true);
        setTimeout(() => {
            const newCount = visibleCount + SONGS_PER_PAGE;
            setVisibleSongs(songs.slice(0, newCount));
            setVisibleCount(newCount);
            setLoadingMore(false);
        }, 500);
    };


    const hasMore = visibleSongs.length < songs.length;

    // Show loading spinner for initial load
    if (isLoading) {
        return (
            <Container
                maxWidth="xl"
                sx={{
                    mt: 0,
                    bgcolor: "background.default",
                    py: 2,
                    px: 3,
                }}
            >
                <Box
                    sx={{
                        height: "60vh",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <CircularProgress size={50} thickness={5} />
                </Box>
            </Container>
        );
    }

    // Show message if no songs available
    if (!songs.length) {
        return (
            <Container
                maxWidth="xl"
                sx={{
                    mt: 0,
                    bgcolor: "background.default",
                    py: 2,
                    px: 3,
                }}
            >
                <Box
                    sx={{
                        height: "40vh",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Typography variant="h6" color="text.secondary">
                        No songs available
                    </Typography>
                </Box>
            </Container>
        );
    }

    return (
        <>
            <Container
                maxWidth="xl"
                sx={{
                    mt: 0,
                    bgcolor: "background.default",
                    py: 2,
                    px: 3,
                }}
            >
                <Grid container spacing={2}>
                    {visibleSongs.map((song, index) => (
                        <Grid
                            item
                            xs={12}
                            key={`${song.songs_master_id || song.id || `temp-id-${index}`}`}
                            sx={{
                                width: "100%",
                                paddingLeft: "0px !important",
                                paddingTop: "0px !important",
                            }}
                        >
                            <SongCard
                                song={song}
                                onRequestOpen={handleRequestOpen}
                                activeRequests={activeRequests}
                                cart={cart}
                            />
                        </Grid>
                    ))}
                </Grid>

                {/* Load More Button */}
                {hasMore && (
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            mt: 3,
                            marginBottom: "1rem",
                        }}
                    >
                        <Button
                            variant="contained"
                            onClick={handleLoadMore}
                            sx={{
                                background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                                color: "#fff",
                                fontWeight: "bold",
                                fontSize: "1rem",
                                px: 4,
                                py: 1,
                                borderRadius: "2rem",
                                boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
                                transition: "all 0.3s ease-in-out",
                                "&:hover": {
                                    background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                                    transform: "scale(1.05)",
                                },
                            }}
                            disabled={loadingMore}
                        >
                            {loadingMore ? (
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                    }}
                                >
                                    <CircularProgress size={20} thickness={5} color="inherit" />
                                    <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                                        Loading...
                                    </Typography>
                                </Box>
                            ) : (
                                "Show More Songs"
                            )}
                        </Button>
                    </Box>
                )}
            </Container>

            {/* Use your existing modal components directly */}
            <DedicationMaster
                open={showDedicationModal}
                onClose={() => setShowDedicationModal(false)}
                song={selectedSong}
                onSubmit={handleSubmitRequest}
            />

            <SuccessModal
                open={showSuccessModal}
                onClose={handleSuccessModalClose}
                requesterName={requesterName}
            />

            <ThankYouDialog
                open={showThankYouDialog}
                onClose={handleThankYouClose}
            />

        </>
    );
};

export default SongsListSection;