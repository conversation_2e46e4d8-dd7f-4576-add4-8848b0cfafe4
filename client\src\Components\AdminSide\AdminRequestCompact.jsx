import React, { useState } from "react";
import { Box, Tab, Tabs } from "@mui/material";
import ShowdownTab from "./ShowdownTab";
import SongqRequests from "./SongqRequests";
import Header from "./Header"; 

const AdminRequestCompact = () => {
    const [currentTab, setCurrentTab] = useState(0);

    const handleTabChange = (event, newValue) => {
        setCurrentTab(newValue);
    };

    return (
        <Box sx={{ width: "100%" }}>

            <Header />
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs value={currentTab} onChange={handleTabChange}>
                    <Tab label="SongReq" />
                    <Tab label="Showdown" />
                </Tabs>
            </Box>

            <Box sx={{ p: 2 }}>
                {/* {currentTab === 0 && <SongReqTabs />} */}
                {currentTab === 0 && <SongqRequests />}
                {currentTab === 1 && <ShowdownTab />}
            </Box>
        </Box>
    );
};

export default AdminRequestCompact;