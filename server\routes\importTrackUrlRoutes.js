const express = require("express");
const router = express.Router();
const importTrackUrlController = require("../controllers/importTrackUrlController");

// Add a new track URL (e.g., YouTube)
router.post("/", importTrackUrlController.addTrackUrl);

// Get all URLs for a specific import track
router.get("/:id", importTrackUrlController.getTrackUrls);

// Delete a specific URL
router.delete("/:id", importTrackUrlController.deleteTrackUrl);

module.exports = router;