// SongCard.js
import React, { useEffect, useState } from "react";
import { Box, Typography, Button } from "@mui/material";

const SongCard = ({ song, activeRequests, onRequestOpen, cart }) => {
  const [remainingTime, setRemainingTime] = useState(0);

  const activeRequest = activeRequests[song.songs_master_id];
  const songStatus = activeRequest?.status;
  const songExpiresAt = activeRequest?.expires_at;

  const isInCart = cart?.some(
    (item) => item.songs_master_id === song.songs_master_id
  );

  const isRequestDisabled =
    activeRequest &&
    songStatus !== "Fulfilled" &&
    new Date(songExpiresAt) > new Date();

  const isDisabled = isInCart || isRequestDisabled;

  useEffect(() => {
    if (isRequestDisabled && songExpiresAt) {
      const interval = setInterval(() => {
        const timeLeft = Math.max(0, new Date(songExpiresAt) - new Date());
        setRemainingTime(timeLeft);

        if (timeLeft <= 0) {
          clearInterval(interval);
        }
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setRemainingTime(0);
    }
  }, [songExpiresAt, isRequestDisabled]);

  const formattedTime = remainingTime
    ? new Date(remainingTime).toISOString().substr(11, 8)
    : "00:00:00";

  const getButtonLabel = () => {
    if (isInCart) return "Request";
    if (isRequestDisabled) return `Requested (${formattedTime})`;
    return "Request";
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: { xs: "flex-start", sm: "center" },
        justifyContent: "space-between",
        gap: 2,
        mb: 2,
        p: 2,
        borderBottom: "1px solid #3c3c3c",
        backgroundColor: "#121212",
        borderRadius: "8px",
        opacity: isDisabled ? 0.4 : 1,
        pointerEvents: isDisabled ? "none" : "auto",
      }}
    >
      {/* Song Image */}
      <Box
        component="img"
        src={song.image}
        alt={song.name}
        sx={{
          width: 48,
          height: 48,
          borderRadius: "4px",
          objectFit: "cover",
        }}
      />

      {/* Song Info */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "flex-start",
          flex: 1,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontSize: "1rem",
            fontWeight: "bold",
            color: "#fff",
            textAlign: "left",
          }}
        >
          {song.name}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontSize: "0.85rem",
            color: "#aaaaaa",
            textAlign: "left",
            wordBreak: "break-word",
          }}
        >
          {song.artist}
        </Typography>
      </Box>

      {/* Request Button */}
      <Button
        disabled={isDisabled}
        onClick={() => onRequestOpen(song)}
        sx={{
          backgroundColor: "#966300",
          color: "#fff",
          "&:hover": { backgroundColor: "#b17a00" },
          fontWeight: "bold",
          fontSize: "0.8rem",
          px: 2,
          py: 1,
          borderRadius: "8px",
          alignSelf: { xs: "flex-start", sm: "center" },
          mt: { xs: 1, sm: 0 },
        }}
      >
        {getButtonLabel()}
      </Button>
    </Box>
  );
};

export default SongCard;