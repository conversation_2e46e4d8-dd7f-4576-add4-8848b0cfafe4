import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import axios from 'axios';

const PullPlaylistModal = ({ open, onClose, onPlaylistAdded }) => {
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [playlistPreview, setPlaylistPreview] = useState(null);

  const extractSpotifyPlaylistId = (url) => {
    const match = url.match(/playlist\/([a-zA-Z0-9]+)(\?|$)/);
    return match ? match[1] : null;
  };

  const handlePreview = async () => {
    if (!playlistUrl.trim()) {
      setError('Please enter a playlist URL');
      return;
    }

    const playlistId = extractSpotifyPlaylistId(playlistUrl);
    if (!playlistId) {
      setError('Invalid Spotify playlist URL');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${playlistId}`
      );
      
      setPlaylistPreview(response.data);
    } catch (error) {
      console.error('Error fetching playlist preview:', error);
      setError('Failed to fetch playlist details. Make sure the URL is valid and the playlist is public.');
    } finally {
      setLoading(false);
    }
  };

  const handlePullPlaylist = async () => {
    const playlistId = extractSpotifyPlaylistId(playlistUrl);
    
    try {
      setLoading(true);
      setError('');
      setSuccess('');
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${playlistId}`
      );
      
      const newPlaylist = response.data;
      
      // Check for duplicates and show message
      if (newPlaylist.duplicates && newPlaylist.duplicates.length > 0) {
        const duplicateNames = newPlaylist.duplicates
          .map(song => `${song.name} by ${song.artist}`)
          .join(', ');
        setSuccess(
          `Playlist added successfully! Note: ${newPlaylist.duplicates.length} duplicate songs were skipped: ${duplicateNames}`
        );
      } else {
        setSuccess('Playlist added successfully!');
      }
      
      // Notify parent component
      if (onPlaylistAdded) {
        onPlaylistAdded(newPlaylist);
      }
      
      // Reset form after delay
      setTimeout(() => {
        handleClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error pulling playlist:', error);
      setError('Failed to pull playlist. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setPlaylistUrl('');
    setPlaylistPreview(null);
    setError('');
    setSuccess('');
    setLoading(false);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
    >
      <DialogTitle>Pull Playlist from Spotify</DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Spotify Playlist URL"
            placeholder="https://open.spotify.com/playlist/..."
            value={playlistUrl}
            onChange={(e) => setPlaylistUrl(e.target.value)}
            fullWidth
            disabled={loading}
          />
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button 
              variant="outlined" 
              onClick={handlePreview}
              disabled={loading || !playlistUrl.trim()}
            >
              Preview Playlist
            </Button>
          </Box>

          {error && (
            <Alert severity="error" onClose={() => setError('')}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
              <CircularProgress />
            </Box>
          )}

          {playlistPreview && !loading && (
            <Box sx={{ border: '1px solid #ddd', borderRadius: 1, p: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                {playlistPreview.images && playlistPreview.images[0] && (
                  <img 
                    src={playlistPreview.images[0].url} 
                    alt={playlistPreview.name}
                    style={{ width: 80, height: 80, borderRadius: 4 }}
                  />
                )}
                <Box>
                  <Typography variant="h6">{playlistPreview.name}</Typography>
                  <Typography variant="body2" color="textSecondary">
                    {playlistPreview.owner?.display_name} • {playlistPreview.tracks?.total} tracks
                  </Typography>
                  {playlistPreview.description && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {playlistPreview.description}
                    </Typography>
                  )}
                </Box>
              </Box>
              
              <Divider sx={{ my: 1 }} />
              
              <Typography variant="body2" color="textSecondary">
                This playlist will be imported with all tracks into your music database.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handlePullPlaylist}
          variant="contained"
          disabled={loading || !playlistPreview}
          color="success"
        >
          {loading ? 'Pulling...' : 'Pull Playlist'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PullPlaylistModal;
