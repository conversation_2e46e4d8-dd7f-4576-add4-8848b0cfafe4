import React, { useState, useEffect, useCallback } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  TextField,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import axios from "axios";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import DoneIcon from "@mui/icons-material/Done";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import RefreshIcon from "@mui/icons-material/Refresh";
import { io } from "socket.io-client";

const COOLDOWN_MS = 60 * 60 * 1000; // 1 hour
const WS_URL = process.env.REACT_APP_WS_URL || "ws://localhost:8080";

const formatTime = (ms) => {
  const minutes = Math.floor(ms / 60000);
  const seconds = Math.floor((ms % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
};

const ShowdownTab = () => {
  const [registrations, setRegistrations] = useState([]);
  const [search, setSearch] = useState("");
  const [timeNow, setTimeNow] = useState(Date.now());
  const [loadingIds, setLoadingIds] = useState([]);
  const [timeRange, setTimeRange] = useState({ start: "00:00", end: "23:59" });
  const [filterStatus, setFilterStatus] = useState("all");
  const [ws, setWs] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  // WebSocket connection
  useEffect(() => {
    const socket = io(process.env.REACT_APP_API_URL, {
      transports: ["websocket"],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    socket.on("connect", () => {
      console.log('Socket.IO connected');
      setIsConnected(true);
    });

    socket.on("disconnect", () => {
      console.log('Socket.IO disconnected');
      setIsConnected(false);
    });

    socket.on("REGISTRATION_UPDATE", (data) => {
      setRegistrations(prev => {
        const exists = prev.some(reg => reg.id === data.id);
        return exists ? prev.map(reg =>
          reg.id === data.id ? data : reg
        ) : [data, ...prev];
      });
    });

    socket.on("connect_error", (err) => {
      console.error('Socket.IO connection error:', err);
    });

    setWs(socket);

    return () => {
      socket.disconnect();
    };
  }, []);

  // Update time every second for live countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeNow(Date.now());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/showdown/registrations`
      );
      setRegistrations(res.data);
    } catch (error) {
      console.error("Failed to fetch registrations:", error);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleMarkDone = async (userId) => {
    try {
      setLoadingIds(prev => [...prev, userId]);

      await axios.post(
        `${process.env.REACT_APP_API_URL}/api/showdown/mark-done`,
        { userId }
      );

      setRegistrations(prev => prev.map(user =>
        user.id === userId ? { ...user, is_completed: true } : user
      ));

    } catch (error) {
      console.error("Failed to mark as done:", error);
    } finally {
      setLoadingIds(prev => prev.filter(id => id !== userId));
    }
  };

  // Group users by date and hour with live cooldown calculation
  const groupedByDateAndHour = registrations.reduce((acc, user) => {
    const date = new Date(user.created_at).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });

    const hour = new Date(user.created_at).getHours();
    const hourLabel = `${hour}:00 - ${hour + 1}:00`;

    const timePassed = timeNow - user.last_registration_time;
    const isCooldown = timePassed < COOLDOWN_MS;
    const timeRemaining = isCooldown ? formatTime(COOLDOWN_MS - timePassed) : "Available";

    const newUser = {
      ...user,
      isCooldown,
      timeRemaining,
      cooldownProgress: isCooldown ? (timePassed / COOLDOWN_MS) * 100 : 100
    };

    if (!acc[date]) acc[date] = {};
    if (!acc[date][hourLabel]) acc[date][hourLabel] = [];
    acc[date][hourLabel].push(newUser);
    return acc;
  }, {});

  // Filter by search, time range and status
  const filteredGrouped = {};
  Object.entries(groupedByDateAndHour).forEach(([date, hours]) => {
    const filteredHours = {};

    Object.entries(hours).forEach(([hour, users]) => {
      // Filter by time range
      const [hourStart] = hour.split(':');
      const hourNum = parseInt(hourStart);
      const [filterStartH, filterStartM] = timeRange.start.split(':').map(Number);
      const [filterEndH, filterEndM] = timeRange.end.split(':').map(Number);

      const hourInRange = (
        (hourNum > filterStartH || (hourNum === filterStartH && 0 >= filterStartM)) &&
        (hourNum < filterEndH || (hourNum === filterEndH && 0 <= filterEndM))
      );

      if (hourInRange) {
        const filteredUsers = users.filter(user => {
          // Filter by search
          const matchesSearch =
            user.name.toLowerCase().includes(search.toLowerCase()) ||
            user.email.toLowerCase().includes(search.toLowerCase());

          // Filter by status
          const matchesStatus =
            filterStatus === 'all' ||
            (filterStatus === 'completed' && user.is_completed) ||
            (filterStatus === 'pending' && !user.is_completed);

          return matchesSearch && matchesStatus;
        });

        if (filteredUsers.length > 0) {
          filteredHours[hour] = filteredUsers;
        }
      }
    });

    if (Object.keys(filteredHours).length > 0) {
      filteredGrouped[date] = filteredHours;
    }
  });

  const handleTimeRangeChange = (e, type) => {
    setTimeRange(prev => ({
      ...prev,
      [type]: e.target.value
    }));
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
        <TextField
          label="Search by name or email"
          variant="outlined"
          sx={{ flexGrow: 1, minWidth: 250 }}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <FilterAltIcon color="action" />
          <TextField
            label="From"
            type="time"
            value={timeRange.start}
            onChange={(e) => handleTimeRangeChange(e, 'start')}
            InputLabelProps={{ shrink: true }}
            sx={{ width: 120 }}
          />
          <TextField
            label="To"
            type="time"
            value={timeRange.end}
            onChange={(e) => handleTimeRangeChange(e, 'end')}
            InputLabelProps={{ shrink: true }}
            sx={{ width: 120 }}
          />

          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filterStatus}
              label="Status"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
            </Select>
          </FormControl>

          {/* <Tooltip title="Refresh data">
            <IconButton onClick={fetchData}>
              <RefreshIcon />
            </IconButton>
          </Tooltip> */}

          <Chip
            label={`Live: ${isConnected ? "ON" : "OFF"}`}
            color={isConnected ? "success" : "error"}
            size="small"
            variant="outlined"
            icon={isConnected ?
              <CheckCircleOutlineIcon fontSize="small" /> :
              <AccessTimeIcon fontSize="small" />
            }
          />
        </Box>
      </Box>

      {Object.keys(filteredGrouped).length === 0 && (
        <Typography sx={{ color: "#aaa", mt: 2 }}>No registrations found</Typography>
      )}

      {Object.entries(filteredGrouped).map(([date, hours]) => (
        <Accordion key={date} sx={{ bgcolor: "#1a1a1a", color: "#fff", mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}>
            <Typography fontWeight="bold">
              {date} ({Object.values(hours).reduce((sum, arr) => sum + arr.length, 0)} users)
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {Object.entries(hours).map(([hour, users]) => (
              <Accordion key={hour} sx={{ bgcolor: "#252525", mb: 1 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}>
                  <Typography>
                    {hour} ({users.length} {users.length === 1 ? "user" : "users"})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {users.map((user) => (
                    <Box
                      key={user.id}
                      sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        justifyContent: 'space-between',
                        alignItems: { xs: 'flex-start', sm: 'center' },
                        py: 1,
                        px: 1,
                        borderBottom: "1px solid #333",
                        backgroundColor: user.is_completed ? "rgba(255, 0, 0, 0.1)" : "transparent",
                        transition: "background-color 0.3s ease",
                        gap: 1
                      }}
                    >
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography
                          fontWeight="bold"
                          sx={{
                            textDecoration: user.is_completed ? "line-through" : "none",
                            color: user.is_completed ? "#888" : "inherit"
                          }}
                        >
                          {user.name}
                        </Typography>
                        <Typography variant="body2" color={user.is_completed ? "#666" : "#aaa"}>
                          {user.email}
                        </Typography>
                        <Typography variant="body2" color={user.is_completed ? "#666" : "#aaa"}>
                          IP: {user.ip_address}
                        </Typography>
                      </Box>

                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        flexDirection: { xs: 'column', sm: 'row' },
                        alignItems: { xs: 'flex-start', sm: 'center' }
                      }}>
                        {user.isCooldown ? (
                          <>
                            <CircularProgress
                              variant="determinate"
                              value={user.cooldownProgress}
                              size={24}
                              thickness={4}
                              sx={{ color: "orange" }}
                            />
                            <Chip
                              icon={<AccessTimeIcon />}
                              label={`Cooldown: ${user.timeRemaining}`}
                              color="warning"
                              variant="outlined"
                              size="small"
                            />
                          </>
                        ) : (
                          <Chip
                            icon={<CheckCircleOutlineIcon />}
                            label="Available"
                            color="success"
                            variant="outlined"
                            size="small"
                          />
                        )}

                        {!user.is_completed ? (
                          <Tooltip title="Mark as done">
                            <IconButton
                              onClick={() => handleMarkDone(user.id)}
                              disabled={loadingIds.includes(user.id)}
                              color="error"
                              size="small"
                            >
                              {loadingIds.includes(user.id) ? (
                                <CircularProgress size={24} />
                              ) : (
                                <DoneIcon />
                              )}
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="Completed">
                            <DoneAllIcon color="success" />
                          </Tooltip>
                        )}
                      </Box>
                    </Box>
                  ))}
                </AccordionDetails>
              </Accordion>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};

export default ShowdownTab;