import { useSnackbar } from 'notistack';
import React, { createContext, useContext, useState } from 'react';

// Create the context
const SongCartContext = createContext();


// Export the use of context for easy importing
export const useSongCart = () => useContext(SongCartContext);

// Provider component
export const SongCartProvider = ({ children }) => {
  const [cart, setCart] = useState([]);
  const { enqueueSnackbar } = useSnackbar();

  const addToCart = (song) => {
    if (cart.length >= 3) {
      // throw new Error("Can't add more than 3 songs for one request. Remove some songs first.");
      enqueueSnackbar("Can't add more than 3 songs for one request. Remove any songs first anf then add it.", {
        variant: "error",
        autoHideDuration: 5000,
      });
      return;
    }
    setCart(current => {
      const updatedCart = [...current, song];
      return updatedCart;
    });
  };

  const removeFromCart = (songId) => {
    setCart(current => current.filter(item => item.songs_master_id !== songId));
  };

  const clearCart = () => {
    setCart([]);
  };

  const updateCartItemDedication = (songs_master_id, dedication) => {
    setCart(current =>
      current.map(item =>
        item.songs_master_id === songs_master_id
          ? { ...item, dedication }
          : item
      )
    )
  };


  return (
    <SongCartContext.Provider value={{ cart, addToCart, removeFromCart, clearCart, updateCartItemDedication }}>
      {children}
    </SongCartContext.Provider>
  );
};