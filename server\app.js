// Load environment variables from the .env file.
// require("dotenv").config();
require("dotenv").config({ path: ".env.local" });
const sqlite3 = require("sqlite3").verbose();
require('./database/migrate');

const { autoFulfillExpiredRequests } = require("./models/songRequestModel");

// Import the necessary models.
const PlaylistModel = require("./models/playlistModel");
const SongModel = require("./models/songModel");
const PlaylistSongsModel = require("./models/playlistSongsModel");

// Import the necessary route handlers.
const lookupRoutes = require("./routes/lookupRoutes");
const metaRoutes = require("./routes/metaRoutes.js");
const adminQuestionsRoutes = require("./routes/questionsRoutes.js");
const playlistRoutes = require("./routes/playlistRoutes");
const songRoutes = require("./routes/songRoutes");
const songUrlRoutes = require("./routes/songUrlRoutes");
const songFileRoutes = require("./routes/songFileRoutes");
const playlistSongsRoutes = require("./routes/playlistSongsRoutes");
const songGenresRoutes = require("./routes/songGenresRoutes");
const songRequestRoutes = require("./routes/songRequestRoutes");
const importSongRoutes = require("./routes/importSongRoutes");
const importSpotifyRoutes = require("./routes/importSpotifyRoutes");
const importTrackUrlRoutes = require("./routes/importTrackUrlRoutes");
const migrateTempToMainRoutes = require("./routes/migrateTempToMainRoutes");
const settingsRoutes = require("./routes/settingsRoutes");
const showdownRoutes = require("./routes/showdownRoutes.js");
const uploadRoutes = require("./routes/uploadRoutes");

// Utility function to format duration in milliseconds to mm:ss format.
const formatDuration = require("./utils/formatDuration");

// Import the necessary modules.
const express = require("express");
const http = require("http");
const socketIO = require("socket.io");
const cookieParser = require("cookie-parser");
const SpotifyWebApi = require("spotify-web-api-node");
const cors = require("cors");
const helmet = require("helmet");
const path = require("path");
const axios = require("axios");
const session = require('express-session');
const { initializeDatabase } = require('./database');
const schemaRoutes = require('./routes/schema');

// Initialize an Express application.
const app = express();
const port = 3001;

const server = http.createServer(app);
const io = socketIO(server, {
  cors: {
    origin: process.env.FRONTEND_URL,
    methods: ["GET", "POST"],
    credentials: true,
  },
});


async function startServer() {
  try {
    // Initialize database with auto-sync
    await initializeDatabase({
      enableAutoSync: true,
      enableWatcher: true,
      debounceMs: 2000
    });

    // Add schema management API routes
    app.use('/api/schema', schemaRoutes);


    app.set("io", io);

    // Socket.IO Events
    io.on("connection", (socket) => {
      console.log("Client connected:", socket.id);

      socket.on("disconnect", () => {
        console.log("Client disconnected:", socket.id);
      });
    });

    // Apply middlewares
    app.use(
      cors({
        origin: process.env.FRONTEND_URL,
        credentials: true,
      })
    );
    app.use(helmet({
      crossOriginResourcePolicy: { policy: "cross-origin" }
    }));
    app.use(cookieParser());
    app.use(express.json());

    // Set up session management.
    app.use("/api/auth", require("./routes/authRoutes"));

    app.use("/api/lookups", lookupRoutes);
    app.use("/api/meta", metaRoutes);
    app.use("/api/admin/questions", adminQuestionsRoutes);
    app.use("/api/playlist", playlistRoutes);
    app.use("/api/songs", songRoutes);
    app.use("/api/song-urls", songUrlRoutes);
    app.use("/api/song-files", songFileRoutes);
    app.use("/api/playlist-songs", playlistSongsRoutes);
    app.use("/api/song-genres", songGenresRoutes);
    app.use("/api", songRequestRoutes);
    app.use("/uploads", (req, res, next) => {
      res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL);
      res.header('Access-Control-Allow-Methods', 'GET');
      res.header('Cross-Origin-Resource-Policy', 'cross-origin');
      next();
    }, express.static(path.join(__dirname, "uploads")));
    app.use("/api/import", importSongRoutes);
    app.use("/api/import/spotify", importSpotifyRoutes);
    app.use("/api/import/track-urls", importTrackUrlRoutes);
    app.use("/api/migrate", migrateTempToMainRoutes);
    app.use("/api/settings", settingsRoutes);
    app.use("/api/showdown", showdownRoutes);
    app.use("/api/upload", uploadRoutes);
 
    app.use(session({
      secret: process.env.JWT_SECRET,
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: false, // Set to true in production with HTTPS
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true
      }
    }));
 
    // Extract Spotify Playlist ID from URL
    function extractSpotifyId(spotifyUrl) {
      try {
        const parts = spotifyUrl.split("/playlist/");
        if (parts.length > 1) {
          return parts[1].split("?")[0];
        }
        return null;
      } catch (err) {
        return null;
      }
    }
 
    // Initialize the Spotify API with credentials from environment variables.
    const spotifyApi = new SpotifyWebApi({
      clientId: process.env.SPOTIFY_CLIENT_ID,
      clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
      redirectUri: process.env.REDIRECT_URI,
    });

    // YouTube API key from environment variables
    const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;

    // Spotify playlist details from stored Spotify URL
    app.get("/api/spotify/playlist-details", async (req, res) => {
      const { playlistId } = req.query;

      try {
        const playlist = await PlaylistModel.getById(playlistId);
        if (!playlist || !playlist.spotify_url) {
          return res.status(400).json({ message: "Spotify URL not found" });
        }

        const spotifyId = extractSpotifyId(playlist.spotify_url);
        if (!spotifyId) {
          return res.status(400).json({ message: "Invalid Spotify URL format" });
        }

        const spotifyApi = new SpotifyWebApi({
          clientId: process.env.SPOTIFY_CLIENT_ID,
          clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
        });

        const tokenData = await spotifyApi.clientCredentialsGrant();
        spotifyApi.setAccessToken(tokenData.body.access_token);

        const playlistDetails = await spotifyApi.getPlaylist(spotifyId);
        res.json(playlistDetails.body);
      } catch (error) {
        console.error("Error fetching Spotify playlist details:", error.message);
        res
          .status(500)
          .json({ message: "Failed to fetch Spotify playlist details" });
      }
    });

    // Route handler for the login endpoint.
    app.get("/login", (req, res) => {
      const scopes = [
        "user-read-private",
        "user-read-email",
        "playlist-read-private",
        "user-modify-playback-state",
        "playlist-read-collaborative",
      ];
      res.redirect(spotifyApi.createAuthorizeURL(scopes));
    });

    app.get("/callback", async (req, res) => {
      const { code } = req.query;
      try {
        const data = await spotifyApi.authorizationCodeGrant(code);
        const { access_token, refresh_token, expires_in } = data.body;

        // Set the access and refresh tokens on the Spotify API instance
        spotifyApi.setAccessToken(access_token);
        spotifyApi.setRefreshToken(refresh_token);

        // Fetch user details from Spotify
        const userData = await spotifyApi.getMe();

        // Store user data in session
        req.session.user = {
          id: userData.body.id,
          display_name: userData.body.display_name,
          email: userData.body.email,
          images: userData.body.images,
          access_token: access_token,
          refresh_token: refresh_token,
          expires_at: Date.now() + (expires_in * 1000)
        };

        // console.log('User logged in:', userData.body.display_name);

        // Redirect to your React app
        res.redirect(`${process.env.FRONTEND_URL}/admin/playlists`);

        // Set up token refresh
        setInterval(async () => {
          try {
            if (req.session.user && Date.now() > req.session.user.expires_at - 300000) { // 5 mins before expiry
              const refreshData = await spotifyApi.refreshAccessToken();
              spotifyApi.setAccessToken(refreshData.body["access_token"]);

              // Update session with new token
              req.session.user.access_token = refreshData.body["access_token"];
              req.session.user.expires_at = Date.now() + (refreshData.body["expires_in"] * 1000);
            }
          } catch (refreshError) {
            console.error("Error refreshing access token:", refreshError);
          }
        }, (expires_in / 2) * 1000);
      } catch (error) {
        console.error("Callback Error:", error);
        if (!res.headersSent) {
          res.redirect(`${process.env.FRONTEND_URL}/login?error=auth_failed`);
        }
      }
    });


    app.get('/api/spotify/user-details', (req, res) => {
      console.log('Session:', req.session);
      console.log('User in session:', req.session.user);

      if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
      }

      res.json(req.session.user);
    });

    // Updated logout route
    app.post('/api/logout', (req, res) => {
      req.session.destroy((err) => {
        if (err) {
          console.error('Session destruction error:', err);
          return res.status(500).json({ error: 'Could not log out' });
        }
        res.clearCookie('connect.sid'); // Clear the session cookie
        res.json({ message: 'Logged out successfully' });
      });
    });

    // Update the playlist-details endpoint
    app.get("/playlist-details", async (req, res) => {
      const { playlistId } = req.query;
      if (!playlistId) {
        return res.status(400).json({ message: "Playlist ID is required" });
      }

      try {
        // Fetch playlist details from Spotify
        const spotifyPlaylist = await spotifyApi.getPlaylist(playlistId);
        const spotifyTracks = await getAllSongs(playlistId);

        // Create playlist in our database
        const newPlaylist = await PlaylistModel.create({
          title: spotifyPlaylist.body.name,
          description: spotifyPlaylist.body.description || "",
          image: spotifyPlaylist.body.images[0]?.url || null,
          playlist_type_lookup_id: 3, // Adjust based on your lookup table
          spotify_url: spotifyPlaylist.body.external_urls.spotify,
        });

        const duplicateSongs = [];
        const addedSongs = [];

        // Process and save each track
        for (const track of spotifyTracks) {
          try {
            // Create new song in our database
            const { song, isDuplicate } = await SongModel.create({
              name: track.name,
              artist: track.artist,
              album: track.album,
              release_year: track.release_year,
              decade: Math.floor(track.release_year / 10) * 10,
              duration: formatDuration(track.duration_ms),
              image: track.imageUrl,
              spotify_id: track.spotify_id,
            });

            // Save all artists to DB
            for (const artist of track.artists) {
              await SongModel.saveArtist({
                id: artist.id,
                name: artist.name,
                image: artist.image,
              });

              await SongModel.linkSongToArtist({
                songId: song.songs_master_id,
                artistId: artist.id,
              });
            }

            // Link song to playlist
            await PlaylistSongsModel.create({
              playlist_master_id: newPlaylist.playlist_master_id, // This is the ID of the playlist
              songs_master_id: song.songs_master_id, // This is the ID of the newly created song
            });

            if (isDuplicate) {
              duplicateSongs.push(song);
            } else {
              addedSongs.push(song);
            }
          } catch (error) {
            console.error("Error saving track:", track.name, error);
            // Continue with next track even if one fails
          }
        }

        // Get the full playlist with songs to return
        const savedPlaylist = await PlaylistModel.getById(
          newPlaylist.playlist_master_id
        );
        // const songs = await PlaylistSongsModel.getSongsByPlaylistId(newPlaylist.id);

        res.json({
          id: savedPlaylist.playlist_master_id,
          title: savedPlaylist.title,
          image: savedPlaylist.image,
          tracks: addedSongs,
          duplicates: duplicateSongs,
        });
      } catch (error) {
        console.error("Error fetching playlist details:", error);
        res.status(error.statusCode || 500).json({ message: error.message });
      }
    });

    // Utility function to fetch all songs from a playlist with pagination
    async function getAllSongs(playlistId) {
      let allTracks = [];
      let offset = 0;
      let fetchMore = true;

      while (fetchMore) {
        try {
          const response = await spotifyApi.getPlaylistTracks(playlistId, {
            offset: offset,
            limit: 100,
          });
          allTracks = allTracks.concat(response.body.items);
          offset += 100;
          fetchMore = response.body.next !== null;
        } catch (error) {
          console.error("Error fetching tracks:", error);
          throw error; // Rethrow to handle it in the outer try-catch block
        }
      }

      const artistCache = new Map();

      return await Promise.all(
        allTracks
          .filter((item) => item.track)
          .map(async (trackItem) => {
            const track = trackItem.track;
            const artistsInfo = await Promise.all(
              track.artists.map(async (artist) => {
                if (artistCache.has(artist.id)) return artistCache.get(artist.id);

                try {
                  const artistData = await spotifyApi.getArtist(artist.id);
                  const artistInfo = {
                    id: artist.id,
                    name: artist.name,
                    image: artistData.body.images[0]?.url || null,
                  };
                  artistCache.set(artist.id, artistInfo);
                  return artistInfo;
                } catch (err) {
                  console.warn("Artist fetch failed for:", artist.name);
                  return {
                    id: artist.id,
                    name: artist.name,
                    image: null,
                  };
                }
              })
            );

            return {
              spotify_id: track.id,
              name: track.name,
              artist: artistsInfo.map((a) => a.name).join(", "),
              artists: artistsInfo, // full artist objects
              album: track.album?.name || null,
              release_year: track.album?.release_date
                ? new Date(track.album.release_date).getFullYear()
                : null,
              duration_ms: track.duration_ms,
              imageUrl: track.album?.images?.[0]?.url || null,
            };
          })
      );
    }

    app.get("/api/spotify/search-track", async (req, res) => {
      const { songName, artistName } = req.query;

      if (!songName || !artistName) {
        return res
          .status(400)
          .json({ error: "songName and artistName are required" });
      }

      try {
        const tokenData = await spotifyApi.clientCredentialsGrant();
        spotifyApi.setAccessToken(tokenData.body.access_token);

        const query = `track:${songName} artist:${artistName}`;
        const result = await spotifyApi.searchTracks(query, { limit: 5 });

        const tracks = result.body.tracks.items.map((track) => ({
          id: track.id,
          name: track.name,
          artist: track.artists[0]?.name,
          album: track.album?.name,
          release_year: track.album?.release_date?.slice(0, 4),
          decade: track.album?.release_date
            ? `${track.album?.release_date.slice(0, 3)}0`
            : null,
          duration: formatDuration(track.duration_ms),
          image: track.album?.images?.[0]?.url,
        }));

        res.json({ tracks });
      } catch (err) {
        console.error("Spotify track search error:", err);
        res.status(500).json({ error: "Failed to search tracks" });
      }
    });

    app.get("/api/spotify/search-artist", async (req, res) => {
      const { artistName } = req.query;

      if (!artistName) {
        return res.status(400).json({ error: "artistName is required" });
      }

      try {
        const tokenData = await spotifyApi.clientCredentialsGrant();
        spotifyApi.setAccessToken(tokenData.body.access_token);

        const result = await spotifyApi.searchArtists(artistName, { limit: 5 });

        const artists = result.body.artists.items.map((artist) => ({
          artist_id: artist.id,
          artistName: artist.name,
          image: artist.images?.[0]?.url || null,
        }));

        res.json({ artists });
      } catch (err) {
        console.error("Spotify artist search error:", err);
        res.status(500).json({ error: "Failed to search artists" });
      }
    });

    app.get("/api/youtube/search", async (req, res) => {
      const { song, artist, searchTerms } = req.query;

      if (!song || !artist) {
        return res.status(400).json({ error: "Song and artist are required." });
      }

      // Parse search terms from comma-separated string
      const terms = searchTerms ? searchTerms.split(",") : ["karaoke"]; // Default to karaoke if no terms provided

      let allVideos = [];
      const seenVideoIds = new Set();

      try {
        // Search for each term and combine results
        for (const term of terms) {
          const trimmedTerm = term.trim();

          // Create more specific search queries
          const searchQueries = [
            `"${song}" "${artist}" "${trimmedTerm}"`, // Exact phrase match
            `${song} ${artist} ${trimmedTerm}`, // Standard search
            `${song} ${trimmedTerm} ${artist}`, // Alternative order
          ];

          for (const query of searchQueries) {
            const searchUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${encodeURIComponent(
              query
            )}&maxResults=15&relevanceLanguage=en&key=${YOUTUBE_API_KEY}`;

            const searchRes = await axios.get(searchUrl);
            const items = searchRes.data.items;

            // Filter out duplicates
            const newItems = items.filter(
              (item) => !seenVideoIds.has(item.id.videoId)
            );
            newItems.forEach((item) => seenVideoIds.add(item.id.videoId));

            allVideos = allVideos.concat(newItems);
          }
        }

        // Get video IDs for statistics
        const videoIds = allVideos.map((item) => item.id.videoId).join(",");

        if (videoIds) {
          const statsUrl = `https://www.googleapis.com/youtube/v3/videos?part=statistics,snippet&id=${videoIds}&key=${YOUTUBE_API_KEY}`;
          const statsRes = await axios.get(statsUrl);

          const enrichedVideos = statsRes.data.items.map((item) => ({
            title: item.snippet.title,
            videoId: item.id,
            url: `https://www.youtube.com/watch?v=${item.id}`,
            views: item.statistics.viewCount || "0",
            likes: item.statistics.likeCount || "0",
            publishedAt: item.snippet.publishedAt,
          }));

          // Filter and sort results by relevance
          const filteredVideos = enrichedVideos.filter((video) => {
            const title = video.title.toLowerCase();
            const songLower = song.toLowerCase();
            const artistLower = artist.toLowerCase();

            // Must contain both song and artist name
            const hasSong = title.includes(songLower);
            const hasArtist = title.includes(artistLower);

            // Check if it contains any of the search terms
            const hasSearchTerm = terms.some((term) =>
              title.includes(term.trim().toLowerCase())
            );

            return hasSong && hasArtist && hasSearchTerm;
          });

          // Sort by relevance: search terms first, then by views
          filteredVideos.sort((a, b) => {
            const titleA = a.title.toLowerCase();
            const titleB = b.title.toLowerCase();

            // Count how many search terms are in the title
            const termCountA = terms.filter((term) =>
              titleA.includes(term.trim().toLowerCase())
            ).length;
            const termCountB = terms.filter((term) =>
              titleB.includes(term.trim().toLowerCase())
            ).length;

            if (termCountA !== termCountB) {
              return termCountB - termCountA; // More terms first
            }

            // If same term count, sort by views
            return parseInt(b.views) - parseInt(a.views);
          });

          res.json(filteredVideos.slice(0, 20)); // Return top 20 most relevant
        } else {
          res.json([]);
        }
      } catch (error) {
        console.error("YouTube API error:", error.response?.data || error.message);
        res.status(500).json({ error: "Failed to fetch videos from YouTube." });
      }
    });

    // Automatically fulfill expired requests every minute.
    setInterval(() => {
      autoFulfillExpiredRequests();
    }, 60 * 1000);

    // Start the Express server.
    server.listen(port, () => {
      console.log(`Listening at http://localhost:${port}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();