import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Box,
  Container,
  Typo<PERSON>,
  TextField,
} from "@mui/material";
import SongList from "./SongList";
import SongModal from "./SongModal";
import { songService } from "./songService";
import { enqueueSnackbar } from "notistack";

const SongsMaster = () => {
  const [songs, setSongs] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [selectedSongId, setSelectedSongId] = useState(null);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      const songsData = await songService.fetchSongs();
      setSongs(songsData);
    } catch (err) {
      console.error("Failed to fetch data", err);
    }
  };

  const handleOpen = () => {
    setOpen(true);
    setSelectedSongId(null);
    setEditingIndex(null);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingIndex(null);
    setSelectedSongId(null);
  };

  const handleEdit = async (songId) => {
    const index = songs.findIndex((song) => song.songs_master_id === songId);
    if (index === -1) return;
    setEditingIndex(index);
    setSelectedSongId(songId);
    setOpen(true);
  };

  const handleDelete = async (songId) => {
    try {
      await songService.deleteSong(songId);
      await fetchAllData();
    } catch (err) {
      console.error("Delete song failed", err);
    }
  };

  const handleSave = async (songData, selectedPlaylists, selectedGenres, tempUrls, tempFiles) => {
    try {
      const existingDuplicate = songs.find(
        (song) =>
          song.name.trim().toLowerCase() === songData.name.trim().toLowerCase() &&
          song.spotify_id === songData.spotify_id &&
          (editingIndex === null ||
            song.songs_master_id !== songs[editingIndex].songs_master_id)
      );

      if (existingDuplicate) {
        enqueueSnackbar("A song with this name and Spotify ID already exists.", {
          variant: "warning",
          autoHideDuration: 3000,
        });
        return false;
      }

      const payload = {
        name: songData.name,
        artist: songData.artist,
        album: songData.album,
        decade: songData.decade,
        duration: songData.duration,
        image: songData.image,
        release_year: songData.release_year,
        spotify_id: songData.spotify_id, // Add spotify_id to payload
      };

      let songId;

      if (editingIndex !== null) {
        songId = songs[editingIndex].songs_master_id;
        await songService.updateSong(songId, payload);
        await songService.removeSongMappings(songId);
      } else {
        songId = await songService.createSong(payload);
      }

      // Add URLs, files, playlists, and genres
      await Promise.all([
        songService.addSongUrls(songId, tempUrls),
        songService.addSongFiles(songId, tempFiles),
        songService.addSongPlaylists(songId, selectedPlaylists),
        songService.addSongGenres(songId, selectedGenres),
      ]);

      await fetchAllData();
      handleClose();
      return true;
    } catch (err) {
      console.error("Error saving song:", err);
      enqueueSnackbar("Failed to save song. Please try again.", {
        variant: "error",
        autoHideDuration: 3000,
      });
      return false;
    }
  };

  const filteredSongs = songs.filter(
    (song) =>
      song.name && song.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 2,
            pb: 2,
            borderBottom: "1px solid",
          }}
        >
          <Typography variant="h4">Songs</Typography>
          <Button variant="contained" color="success" onClick={handleOpen}>
            Add Song
          </Button>
        </Box>

        <TextField
          fullWidth
          label="Search Songs"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ my: 2 }}
        />

        <SongList
          songs={filteredSongs}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />

        <SongModal
          open={open}
          onClose={handleClose}
          onSave={handleSave}
          editingIndex={editingIndex}
          selectedSongId={selectedSongId}
          songs={songs}
        />
      </Container>
    </>
  );
};

export default SongsMaster;