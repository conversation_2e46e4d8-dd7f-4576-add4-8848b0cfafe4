import React, { useState, useEffect, useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Box,
  CircularProgress,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSongCart } from "../../../context/SongCartContext";
import { useSongs } from "../../hooks/useSongs";
import { useActiveRequests } from "../../hooks/useActiveRequests";
import SongCard from "../common/SongCard";
import DedicationMaster from './DedicationModal/DedicationMaster';
import SuccessModal from "./SuccessModal";
import ThankDialog from "./ThankDialog";
import { useSnackbar } from "notistack";
import { getDeviceId } from "../../../utils/cookieUtils";

const SearchModal = ({ open, onClose }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSong, setSelectedSong] = useState(null);
  const [showDedicationModal, setShowDedicationModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showThankYouDialog, setShowThankYouDialog] = useState(false);
  const [requesterName, setRequesterName] = useState("");
  const { cart, clearCart } = useSongCart();
  const { enqueueSnackbar } = useSnackbar();

  // Use the custom hooks with enabled option to control when to fetch
  const {
    data: songs = [],
    isLoading: songsLoading,
    error: songsError,
    isFetching: songsFetching
  } = useSongs({
    enabled: open,
  });

  const {
    data: activeRequests = {},
    isLoading: requestsLoading,
    error: requestsError,
    refetch: refetchActiveRequests
  } = useActiveRequests({
    enabled: open,
  });

  // Filter songs based on search query
  const filteredSongs = useMemo(() => {
    if (!searchQuery) return songs;
    return songs.filter((song) =>
      song.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [songs, searchQuery]);

  // Reset search when modal closes
  useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  const handleRequestSuccess = () => {
    refetchActiveRequests();
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleRequestOpen = (song) => {
    // if (cart.length >= 3) {
    //   enqueueSnackbar("Cart is full. Maximum 3 songs allowed.", {
    //     variant: "warning",
    //   });
    //   return;
    // }

    setSelectedSong(song);
    setShowDedicationModal(true);
  };

  const handleSubmitRequest = async (dedicationData) => {
    try {
      console.log('dedicationData:', dedicationData);

      const deviceId = getDeviceId();
      const { userName, songs } = dedicationData;

      const finalUserName = userName || "Anonymous";
      setRequesterName(finalUserName);

      // Check for existing active request for this song
      const canRequestResponse = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/can-request?device_id=${deviceId}&song_id=${selectedSong.songs_master_id}`
      );

      const canRequestData = await canRequestResponse.json();

      if (canRequestData.hasActiveRequest) {
        enqueueSnackbar("You already have an active request for this song", {
          variant: "warning",
        });
        return;
      }

      // Add to cart with dedication
      // const songToAdd = {
      //   ...selectedSong,
      //   actionType: actionType,
      //   dedication: dedication,
      // };
      // addToCart(songToAdd);

      // Submit the request
      const requestBody = {
        requester_name: finalUserName,
        device_id: deviceId,
        songs: songs.map(song => ({
          songs_master_id: song.songs_master_id,
          dedication_msg: song.dedication || "",
          action_type: song.actionType,
        })),
      };


      console.log('Request body:', requestBody);

      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit request");
      }

      await response.json();
      setShowDedicationModal(false);
      onClose();
      setShowSuccessModal(true);
      handleRequestSuccess();

    } catch (error) {
      console.error("Failed to make request:", error);
      enqueueSnackbar("Failed to submit request. Please try again.", {
        variant: "error",
      });
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    setShowThankYouDialog(true);
  };

  const handleThankYouClose = () => {
    setShowThankYouDialog(false);
    clearCart();
  };

  // Combined loading state
  const isLoading = songsLoading || requestsLoading;
  const hasError = songsError || requestsError;

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            width: {
              xs: "90vw",
              sm: "80vw",
              md: "500px",
            },
            height: "100vh",
            overflowY: "auto",
            margin: {
              xs: "16px",
              sm: "24px",
            },
            borderRadius: "12px",
            border: "1px solid",
            backgroundColor: "#000000",
          },
        }}
        slotProps={{
          backdrop: {
            sx: {
              backdropFilter: "blur(8px)",
              backgroundColor: "rgba(0, 0, 0, 0.4)",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            backgroundColor: "#000000",
            color: "#FFFFFF",
            padding: "16px 24px",
          }}
        >
          Search Songs
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{ color: "#FFFFFF" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent
          dividers
          sx={{
            backgroundColor: "#000000",
            color: "#FFFFFF",
            padding: "16px 24px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <TextField
            fullWidth
            placeholder="Search song name..."
            value={searchQuery}
            onChange={handleSearchChange}
            size="small"
            InputProps={{
              sx: {
                backgroundColor: "#1a1a1a",
                color: "#fff",
                borderRadius: "8px",
                "&:focus": {
                  backgroundColor: "#2a2a2a",
                },
              },
            }}
            sx={{ mb: 2 }}
          />

          {isLoading ? (
            <Box sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "200px",
              my: 3
            }}>
              <CircularProgress />
            </Box>
          ) : hasError ? (
            <Box sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "200px",
              my: 3
            }}>
              <Typography color="error">
                Error loading data. Please try again.
              </Typography>
            </Box>
          ) : (
            <Box sx={{ maxHeight: "70vh", overflowY: "auto" }}>
              {filteredSongs.length === 0 ? (
                <Typography sx={{ textAlign: "center", py: 3 }}>
                  {searchQuery ? 'No songs found matching your search' : 'No songs available'}
                </Typography>
              ) : (
                filteredSongs.map((song) => (
                  <Box key={song.id} sx={{ mb: 2 }}>
                    <SongCard
                      song={song}
                      onRequestOpen={handleRequestOpen}
                      activeRequests={activeRequests}
                      cart={cart}
                    />
                  </Box>
                ))
              )}
            </Box>
          )}

          {songsFetching && !songsLoading && (
            <Box sx={{
              display: "flex",
              justifyContent: "center",
              mt: 1
            }}>
              <CircularProgress size={20} />
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* Use your existing modal components directly */}
      <DedicationMaster
        open={showDedicationModal}
        onClose={() => setShowDedicationModal(false)}
        song={selectedSong}
        onSubmit={handleSubmitRequest}
      />

      <SuccessModal
        open={showSuccessModal}
        onClose={handleSuccessModalClose}
        requesterName={requesterName}
      />

      <ThankDialog
        open={showThankYouDialog}
        onClose={handleThankYouClose}
      />
    </>
  );
};

export default SearchModal;