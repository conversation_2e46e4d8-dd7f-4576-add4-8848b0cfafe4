import React from 'react';
import { Button } from '@mui/material';

const Login = () => {
  const handleLogin = () => {
    window.location.href = `${process.env.REACT_APP_API_URL}/login`; // Ensure this points to the correct backend server
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', marginTop: '20vh' }}>
      <Button variant="contained" color="primary" onClick={handleLogin}>
        Login with Spotify
      </Button>
    </div>
  );
};

export default Login;