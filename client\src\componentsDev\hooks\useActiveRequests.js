// hooks/useActiveRequests.js
import { useQuery } from '@tanstack/react-query';
import { getDeviceId } from '../../utils/cookieUtils';

const fetchActiveRequests = async () => {
  const deviceId = getDeviceId();
  if (!deviceId) {
    throw new Error('Device ID not found');
  }

  const response = await fetch(
    `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch active requests');
  }
  
  return response.json();
};

export const useActiveRequests = (options = {}) => {
  return useQuery({
    queryKey: ['activeRequests'],
    queryFn: fetchActiveRequests,
    ...options,
  });
};