const db = require("../database/db.js");
const getClientIp = require("../utils/getClientIp.js");
const { isWithinCooldown, minutesToMs } = require("../utils/timeUtils.js");
// const sendThankYouEmail = require("../utils/mailUtils.js");

const COOLDOWN_MINUTES = 60; // 1 hour

async function registerAudience(req, res) {
    try {
        const { name, email } = req.body;

        if (!name || !email) {
            return res.status(400).json({ error: "Name and email are required" });
        }

        const ip = getClientIp(req);

        // 1️⃣ Check last registration for this email
        const existingUser = await new Promise((resolve, reject) => {
            db.get(
                `SELECT * FROM showdown_user 
                 WHERE email = ? 
                 ORDER BY last_registration_time DESC 
                 LIMIT 1`,
                [email],
                (err, row) => (err ? reject(err) : resolve(row))
            );
        });

        // 2️⃣ If within cooldown, block registration
        if (
            existingUser &&
            isWithinCooldown(existingUser.last_registration_time, minutesToMs(COOLDOWN_MINUTES))
        ) {
            const now = Date.now();
            const remainingMs =
                minutesToMs(COOLDOWN_MINUTES) - (now - existingUser.last_registration_time);

            const minutes = Math.floor(remainingMs / 60000);
            const seconds = Math.floor((remainingMs % 60000) / 1000);

            const formatted = `${minutes.toString().padStart(2, "0")}:${seconds
                .toString()
                .padStart(2, "0")}`;

            return res.status(429).json({
                error: `You can register again after ${formatted}`,
                cooldown: formatted,
            });
        }

        // 3️⃣ Insert new registration
        const now = Date.now();
        let insertedId;
        await new Promise((resolve, reject) => {
            db.run(
                `INSERT INTO showdown_user 
                 (name, email, ip_address, last_registration_time) 
                 VALUES (?, ?, ?, ?)`,
                [name, email, ip, now],
                function (err) {
                    if (err) reject(err);
                    else {
                        insertedId = this.lastID; // get last inserted ID
                        resolve();
                    }
                }
            );
        });

        // 4️⃣ Get inserted user
        const newUser = await new Promise((resolve, reject) => {
            db.get(
                `SELECT * FROM showdown_user WHERE id = ?`,
                [insertedId],
                (err, row) => (err ? reject(err) : resolve(row))
            );
        });

        // 5️⃣ Emit real-time update to all connected clients
        const io = req.app.get("io");
        io.emit("REGISTRATION_UPDATE", newUser);

        // 6️⃣ Respond success
        res.status(201).json({ message: "Registered successfully", user: newUser });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Something went wrong" });
    }
}

// Function to get all registrations
async function getRegistrations(req, res) {
    try {
        const data = await new Promise((resolve, reject) => {
            db.all(
                `SELECT * FROM showdown_user ORDER BY created_at DESC`,
                [],
                (err, rows) => (err ? reject(err) : resolve(rows))
            );
        });
        res.json(data);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Something went wrong" });
    }
}

// Function to mark registration as done
async function markRegistrationDone(req, res) {
    try {
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }

        // Update the database
        await new Promise((resolve, reject) => {
            db.run(
                `UPDATE showdown_user 
                 SET is_completed = TRUE 
                 WHERE id = ?`,
                [userId],
                (err) => (err ? reject(err) : resolve())
            );
        });

        // Get the updated user data
        const updatedUser = await new Promise((resolve, reject) => {
            db.get(
                `SELECT * FROM showdown_user WHERE id = ?`,
                [userId],
                (err, row) => (err ? reject(err) : resolve(row))
            );
        });


        res.json({
            message: "Marked as done successfully",
            user: updatedUser // Optional: return updated user data
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Something went wrong" });
    }
}

module.exports = { getRegistrations, registerAudience, markRegistrationDone };
