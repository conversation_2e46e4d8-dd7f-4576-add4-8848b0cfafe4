// Quiz Components Export Index
// This file provides a centralized export for all quiz-related components

// Main Components
export { default as QuizDashboard } from './QuizDashboard';
export { default as QuizQuestionForm } from './QuizQuestionForm';
export { default as QuizQuestionManagement } from './QuizQuestionManagement';
export { default as QuizSettings } from './QuizSettings';
export { default as QuizIntegrationExample } from './QuizIntegrationExample';
export { default as QuizTestPage } from './QuizTestPage';
export { default as StatusFilteringTest } from './StatusFilteringTest';
export { default as QuestionViewDialog } from './QuestionViewDialog';
export { default as QuestionManagementTest } from './QuestionManagementTest';
export { default as AnswerSegregationTest } from './AnswerSegregationTest';

// Legacy Components (for backward compatibility)
export { default as QuizMaster } from './QuizMaster';

// Re-export hooks for convenience
export {
  useQuizQuestions,
  useQuizQuestion,
  useCreateQuizQuestion,
  useUpdateQuizQuestion,
  useDeleteQuizQuestion,
  useQuizQuestionsBySong,
  useQuizQuestionsByDifficulty,
  useBulkQuizQuestionOperations,
  useQuizQuestionStats,
  useValidateQuizQuestion,
  useDuplicateQuizQuestion,
  useImportQuizQuestions,
  useExportQuizQuestions
} from '../../../hooks/useQuizQuestions';

// Component configurations and constants
export const QUIZ_ANSWER_TYPES = [
  { value: 'multiple_choice', label: 'Multiple Choice', minAnswers: 2, maxAnswers: 6, allowMultipleCorrect: true },
  { value: 'single_choice', label: 'Single Choice', minAnswers: 2, maxAnswers: 6, allowMultipleCorrect: false },
  { value: 'true_false', label: 'True/False', minAnswers: 2, maxAnswers: 2, allowMultipleCorrect: false }
];

export const QUIZ_QUESTION_TYPES = [
  { value: 'multiple_choice', label: 'Multiple Choice', minAnswers: 2, maxAnswers: 6 },
  { value: 'single_choice', label: 'Single Choice', minAnswers: 2, maxAnswers: 6 },
  { value: 'true_false', label: 'True/False', minAnswers: 2, maxAnswers: 2 }
];

export const QUIZ_DIFFICULTY_COLORS = {
  'Easy': '#4CAF50',
  'Medium': '#FF9800',
  'Hard': '#F44336',
  'Very Hard': '#9C27B0',
  'Expert': '#E91E63'
};

// Utility functions
export const formatTimeLimit = (seconds) => {
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
};

export const getDifficultyColor = (level) => {
  return QUIZ_DIFFICULTY_COLORS[level] || '#2196F3';
};

export const validateQuizQuestion = (questionData) => {
  const errors = {};

  if (!questionData.songs_master_id) {
    errors.songs_master_id = 'Please select a song';
  }

  if (!questionData.question_text?.trim()) {
    errors.question_text = 'Question text is required';
  }

  if (!questionData.difficulty_id) {
    errors.difficulty_id = 'Please select a difficulty level';
  }

  if (questionData.time_limit_seconds < 5 || questionData.time_limit_seconds > 300) {
    errors.time_limit_seconds = 'Time limit must be between 5 and 300 seconds';
  }

  if (questionData.points_value < 1 || questionData.points_value > 100) {
    errors.points_value = 'Points value must be between 1 and 100';
  }

  // Validate answers
  const hasCorrectAnswer = questionData.answers?.some(answer => answer.is_correct);
  if (!hasCorrectAnswer && questionData.question_type !== 'fill_blank') {
    errors.answers = 'At least one answer must be marked as correct';
  }

  const hasEmptyAnswers = questionData.answers?.some(answer => !answer.answer_text?.trim());
  if (hasEmptyAnswers) {
    errors.answers = 'All answer fields must be filled';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Default form data
export const getDefaultQuizQuestionForm = () => ({
  songs_master_id: '',
  question_text: '',
  question_type: 'multiple_choice',
  difficulty_id: '',
  time_limit_seconds: 30,
  points_value: 10,
  answers: [
    { answer_text: '', is_correct: false, answer_order: 1 },
    { answer_text: '', is_correct: false, answer_order: 2 }
  ]
});

// Component usage examples and documentation
export const COMPONENT_USAGE_EXAMPLES = {
  QuizDashboard: `
    // Full-featured quiz management dashboard
    import { QuizDashboard } from './components/quiz';
    
    function AdminPanel() {
      return <QuizDashboard />;
    }
  `,
  
  QuizQuestionForm: `
    // Standalone quiz question creation form
    import { QuizQuestionForm } from './components/quiz';
    
    function CreateQuestion() {
      const handleSuccess = (newQuestion) => {
        console.log('Question created:', newQuestion);
      };
      
      return (
        <QuizQuestionForm 
          onSuccess={handleSuccess}
          editingQuestion={null} // or pass existing question for editing
        />
      );
    }
  `,
  
  QuizQuestionManagement: `
    // Quiz question list and management
    import { QuizQuestionManagement } from './components/quiz';
    
    function ManageQuestions() {
      return <QuizQuestionManagement />;
    }
  `,
  
  Hooks: `
    // Using quiz question hooks
    import { useQuizQuestions, useCreateQuizQuestion } from './components/quiz';
    
    function MyComponent() {
      const { data: questions, isLoading } = useQuizQuestions();
      const createMutation = useCreateQuizQuestion({
        onSuccess: (data) => console.log('Created:', data)
      });
      
      const handleCreate = (questionData) => {
        createMutation.mutate(questionData);
      };
      
      return (
        <div>
          {isLoading ? 'Loading...' : \`\${questions.length} questions\`}
        </div>
      );
    }
  `
};

// API endpoint documentation
export const API_ENDPOINTS = {
  base: '/api/admin/questions',
  endpoints: {
    list: 'GET /',
    create: 'POST /',
    get: 'GET /:id',
    update: 'PUT /:id',
    delete: 'DELETE /:id',
    stats: 'GET /stats',
    duplicate: 'POST /:id/duplicate',
    bulkDelete: 'POST /bulk-delete',
    export: 'POST /export',
    import: 'POST /import'
  }
};

// Theme configuration for quiz components
export const QUIZ_THEME_CONFIG = {
  colors: {
    background: '#121212',
    cardBackground: '#1A1A1A',
    primary: '#4CAF50',
    secondary: '#2196F3',
    error: '#F44336',
    warning: '#FF9800',
    text: '#fff',
    textSecondary: '#ccc',
    border: '#333'
  },
  spacing: {
    small: '8px',
    medium: '16px',
    large: '24px',
    xlarge: '32px'
  },
  borderRadius: {
    small: '6px',
    medium: '8px',
    large: '12px',
    xlarge: '16px'
  }
};

