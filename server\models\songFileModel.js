const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const SongFileModel = {
  create: ({ songs_master_id, file, file_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO song_files (songs_master_id, file, file_type_lookup_id)
        VALUES (?, ?, ?)
      `;
      db.run(
        query,
        [songs_master_id, file, file_type_lookup_id],
        function (err) {
          if (err) return reject(err);
          resolve({ id: this.lastID });
        }
      );
    });
  },

  getAll: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM song_files`, [], (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
      });
    });
  },

  getById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM song_files WHERE song_files_id = ?`,
        [id],
        (err, row) => {
          if (err) return reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, { songs_master_id, file, file_type_lookup_id }) => {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE song_files
        SET songs_master_id = ?, file = ?, file_type_lookup_id = ?
        WHERE song_files_id = ?
      `;
      db.run(
        query,
        [songs_master_id, file, file_type_lookup_id, id],
        function (err) {
          if (err) return reject(err);
          resolve({ updated: this.changes });
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM song_files WHERE song_files_id = ?`,
        [id],
        function (err) {
          if (err) return reject(err);
          resolve({ deleted: this.changes });
        }
      );
    });
  },

  getBySongId: (songId) => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT sf.*, ft.file_type_name 
       FROM song_files sf
       JOIN file_type_lookup ft ON sf.file_type_lookup_id = ft.file_type_lookup_id
       WHERE sf.songs_master_id = ?`,
        [songId],
        (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        }
      );
    });
  },
};

module.exports = SongFileModel;
