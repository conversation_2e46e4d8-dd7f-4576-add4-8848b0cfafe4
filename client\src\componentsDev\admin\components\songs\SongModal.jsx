import React, { useState, useEffect } from "react";
import {
  Modal,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SongForm from "./SongForm";
import { songService } from "./songService";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  maxHeight: "90vh",
  overflowY: "auto",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
};

const SongModal = ({ open, onClose, onSave, editingIndex, selectedSongId, songs }) => {
  const [songData, setSongData] = useState({
    name: "",
    artist: "",
    album: "",
    image: "",
    release_year: "",
    decade: "",
    duration: "",
    spotify_id: "",
  });
  const [selectedPlaylists, setSelectedPlaylists] = useState([]);
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [songUrls, setSongUrls] = useState([]);
  const [songFiles, setSongFiles] = useState([]);
  const [tempUrls, setTempUrls] = useState([]);
  const [tempFiles, setTempFiles] = useState([]);
  const [isDuplicate, setIsDuplicate] = useState(false);

  useEffect(() => {
    if (open) {
      if (editingIndex !== null && songs[editingIndex]) {
        loadSongData(songs[editingIndex]);
      } else {
        resetSongData();
      }
    }
  }, [open, editingIndex, songs, selectedSongId]);

  const loadSongData = async (songToEdit) => {
    setSongData({ ...songToEdit });
    setSelectedPlaylists([]);

    try {
      const [genresData, filesData, urlsData] = await Promise.all([
        songService.fetchSongGenres(songToEdit.songs_master_id),
        songService.fetchSongFiles(songToEdit.songs_master_id),
        songService.fetchSongUrls(songToEdit.songs_master_id),
      ]);

      setSelectedGenres(genresData);
      setSongFiles(filesData);
      setSongUrls(urlsData);
      setTempUrls([]);
      setTempFiles([]);
    } catch (err) {
      console.error("Failed to load song data:", err);
    }
  };

  const resetSongData = () => {
    setSongData({
      name: "",
      artist: "",
      album: "",
      image: "",
      release_year: "",
      decade: "",
      duration: "",
      spotify_id: "",
    });
    setSelectedPlaylists([]);
    setSelectedGenres([]);
    setSongUrls([]);
    setSongFiles([]);
    setTempUrls([]);
    setTempFiles([]);
    setIsDuplicate(false);
  };

  const handleClose = () => {
    resetSongData();
    onClose();
  };

  const handleSave = async () => {
    const success = await onSave(songData, selectedPlaylists, selectedGenres, tempUrls, tempFiles);
    if (success) {
      resetSongData();
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedSongData = {
      ...songData,
      [name]: value,
    };
    setSongData(updatedSongData);

    // Real-time duplicate check only when typing song name
    if (name === "name" || name === "spotify_id") {
      const duplicate = songs.find(
        (song) =>
          song.name.trim().toLowerCase() === updatedSongData.name.trim().toLowerCase() &&
          song.spotify_id === updatedSongData.spotify_id &&
          (editingIndex === null ||
            song.songs_master_id !== songs[editingIndex].songs_master_id)
      );
      setIsDuplicate(!!duplicate);
    }
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box sx={modalStyle}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6">
            {editingIndex !== null ? "Edit Song" : "Add Song"}
          </Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Box>

        <SongForm
          songData={songData}
          setSongData={setSongData}
          selectedPlaylists={selectedPlaylists}
          setSelectedPlaylists={setSelectedPlaylists}
          selectedGenres={selectedGenres}
          setSelectedGenres={setSelectedGenres}
          songUrls={songUrls}
          setSongUrls={setSongUrls}
          songFiles={songFiles}
          setSongFiles={setSongFiles}
          tempUrls={tempUrls}
          setTempUrls={setTempUrls}
          tempFiles={tempFiles}
          setTempFiles={setTempFiles}
          isDuplicate={isDuplicate}
          editingIndex={editingIndex}
          selectedSongId={selectedSongId}
          handleChange={handleChange}
          handleSave={handleSave}
        />
      </Box>
    </Modal>
  );
};

export default SongModal;