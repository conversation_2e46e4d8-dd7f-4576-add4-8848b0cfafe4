import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Stack,
  FormHelperText,
  Chip,
  IconButton,
  Divider,
  Alert,
  Collapse,
  CircularProgress,
  Radio,
  RadioGroup,
  FormControlLabel,
  Checkbox,
  Fade,
  Slide
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Preview as PreviewIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import axios from 'axios';
import { useSongs } from '../../../hooks/useSongs';

// Styled components following SongQ design system
const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
  '& .MuiCardContent-root': {
    padding: '24px',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiOutlinedInput-input': {
    color: '#fff',
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
    '&.Mui-focused': {
      color: '#4CAF50',
    },
  },
  '& .MuiSelect-select': {
    color: '#fff',
  },
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  padding: '10px 20px',
  ...(variant === 'contained' && {
    backgroundColor: '#4CAF50',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#45a049',
    },
  }),
  ...(variant === 'outlined' && {
    borderColor: '#333',
    color: '#ccc',
    '&:hover': {
      borderColor: '#4CAF50',
      backgroundColor: 'rgba(76, 175, 80, 0.1)',
    },
  }),
}));

const AnswerCard = styled(Card)(({ theme, isCorrect }) => ({
  backgroundColor: isCorrect ? 'rgba(76, 175, 80, 0.1)' : '#1A1A1A',
  border: `1px solid ${isCorrect ? '#4CAF50' : '#333'}`,
  borderRadius: '8px',
  marginBottom: '12px',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: isCorrect ? '#4CAF50' : '#555',
  },
}));

// Lookup service following the same pattern as QuizLookupManagement
const lookupService = {
  fetchItems: async (type) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lookups/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type}:`, error);
      throw error;
    }
  },

  getFieldMap: (type) => {
    const fieldMaps = {
      'difficulty': { id: 'difficulty_id', name: 'level_name', description: 'level_name' },
      'question-type': { id: 'question_type_id', name: 'type_name', description: 'display_name' },
      'answer-type': { id: 'answer_type_id', name: 'type_name', description: 'display_name' }
    };
    return fieldMaps[type];
  }
};

// Answer type configurations - now based on answer types instead of question types
const getAnswerTypeConfig = (answerTypes) => {
  const configs = {};

  answerTypes.forEach(at => {
    if (at.type_name === 'multiple_choice') {
      configs[at.type_name] = {
        minAnswers: 2,
        maxAnswers: 6,
        allowMultipleCorrect: true,
        description: 'Multiple answers can be marked as correct',
        icon: '☑️'
      };
    } else if (at.type_name === 'single_choice') {
      configs[at.type_name] = {
        minAnswers: 2,
        maxAnswers: 6,
        allowMultipleCorrect: false,
        description: 'Only one answer can be marked as correct',
        icon: '🔘'
      };
    } else if (at.type_name === 'true_false') {
      configs[at.type_name] = {
        minAnswers: 2,
        maxAnswers: 2,
        allowMultipleCorrect: false,
        description: 'True or False question with single correct answer',
        icon: '✓✗'
      };
    } else {
      // Default configuration for other types
      configs[at.type_name] = {
        minAnswers: 2,
        maxAnswers: 4,
        allowMultipleCorrect: false,
        description: 'Standard answer format',
        icon: '📝'
      };
    }
  });

  return configs;
};

const QuizQuestionForm = ({ onSuccess, editingQuestion = null }) => {
  const { enqueueSnackbar } = useSnackbar();
  const { data: songs = [], isLoading: songsLoading } = useSongs();

  // Form state
  const [formData, setFormData] = useState({
    songs_master_id: '',
    question_text: '',
    question_type: '',
    answer_type: '', // This will be explicitly selected by user
    difficulty_id: '',
    time_limit_seconds: 30,
    points_value: 10,
    answers: [
      { answer_text: '', is_correct: false, answer_order: 1 },
      { answer_text: '', is_correct: false, answer_order: 2 }
    ]
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [lookupData, setLookupData] = useState({
    difficulties: [],
    questionTypes: [],
    answerTypes: []
  });

  // Load lookup data using the same pattern as QuizLookupManagement with status filtering
  const loadAllData = useCallback(async () => {
    setDataLoading(true);
    try {
      const [difficulties, questionTypes, answerTypes] = await Promise.all([
        lookupService.fetchItems('difficulty'),
        lookupService.fetchItems('question-type'),
        lookupService.fetchItems('answer-type')
      ]);

      // Filter to only show active items (status = 1) following the existing pattern
      const activeDifficulties = (difficulties || []).filter(item => item.status === 1);
      const activeQuestionTypes = (questionTypes || []).filter(item => item.status === 1);
      const activeAnswerTypes = (answerTypes || []).filter(item => item.status === 1);

      console.log('Loaded lookup data:', {
        difficulties: `${activeDifficulties.length}/${difficulties?.length || 0} active`,
        questionTypes: `${activeQuestionTypes.length}/${questionTypes?.length || 0} active`,
        answerTypes: `${activeAnswerTypes.length}/${answerTypes?.length || 0} active`
      });

      setLookupData({
        difficulties: activeDifficulties,
        questionTypes: activeQuestionTypes,
        answerTypes: activeAnswerTypes
      });

      // Set default values if none selected and active data is available
      if (!formData.question_type && activeQuestionTypes.length > 0) {
        const defaultQuestionType = activeQuestionTypes.find(qt => qt.type_name === 'multiple_choice') || activeQuestionTypes[0];
        setFormData(prev => ({
          ...prev,
          question_type: defaultQuestionType.type_name
        }));
      }

      // Set default answer type if none selected
      if (!formData.answer_type && activeAnswerTypes.length > 0) {
        const defaultAnswerType = activeAnswerTypes.find(at => at.type_name === 'single_choice') || activeAnswerTypes[0];
        setFormData(prev => ({
          ...prev,
          answer_type: defaultAnswerType.type_name
        }));
      }

      // Validate that we have active items for all required lookups
      if (activeDifficulties.length === 0) {
        enqueueSnackbar('Warning: No active difficulty levels found. Please activate at least one difficulty level.', { variant: 'warning' });
      }
      if (activeQuestionTypes.length === 0) {
        enqueueSnackbar('Warning: No active question types found. Please activate at least one question type.', { variant: 'warning' });
      }
      if (activeAnswerTypes.length === 0) {
        enqueueSnackbar('Warning: No active answer types found. Please activate at least one answer type.', { variant: 'warning' });
      }

    } catch (error) {
      console.error('Error loading lookup data:', error);
      enqueueSnackbar('Error loading form data. Please refresh the page.', { variant: 'error' });
    } finally {
      setDataLoading(false);
    }
  }, [enqueueSnackbar]);

  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // Load editing question data
  useEffect(() => {
    const loadEditingQuestion = async () => {
      if (editingQuestion && lookupData.questionTypes.length > 0) {
        try {
          // Use the editing question data (should already be full data from management component)
          const fullQuestion = editingQuestion;

          setFormData({
            songs_master_id: fullQuestion.songs_master_id || '',
            question_text: fullQuestion.question_text || '',
            question_type: fullQuestion.q_type || fullQuestion.question_type_name || fullQuestion.question_type || '',
            answer_type: fullQuestion.a_type || fullQuestion.answer_type_name || fullQuestion.answer_type || '',
            difficulty_id: fullQuestion.difficulty_id || '',
            time_limit_seconds: fullQuestion.time_limit_seconds || 30,
            points_value: fullQuestion.points_value || 10,
            answers: fullQuestion.answers || [
              { answer_text: '', is_correct: false, answer_order: 1 },
              { answer_text: '', is_correct: false, answer_order: 2 }
            ]
          });
        } catch (error) {
          console.error('Error loading editing question:', error);
          enqueueSnackbar('Failed to load question for editing', { variant: 'error' });
        }
      }
    };

    // Only load if we have an editing question and haven't loaded it yet
    if (editingQuestion && lookupData.questionTypes.length > 0) {
      // Check if we need to load (either no form data or different question)
      const needsLoading = !formData.question_text ||
                          (formData.question_text && editingQuestion.question_id &&
                           formData.question_text !== editingQuestion.question_text);

      if (needsLoading) {
        loadEditingQuestion();
      }
    }
  }, [editingQuestion?.question_id, lookupData.questionTypes.length]);

  // Update answers when answer type changes
  useEffect(() => {
    if (!formData.answer_type || lookupData.answerTypes.length === 0) return;

    const answerTypeConfigs = getAnswerTypeConfig(lookupData.answerTypes);
    const config = answerTypeConfigs[formData.answer_type];

    if (!config) return;

    if (formData.answer_type === 'true_false') {
      setFormData(prev => ({
        ...prev,
        answers: [
          { answer_text: 'True', is_correct: false, answer_order: 1 },
          { answer_text: 'False', is_correct: false, answer_order: 2 }
        ]
      }));
    } else {
      // Ensure minimum answers for other types
      const currentAnswers = formData.answers.length;
      if (currentAnswers < config.minAnswers) {
        const newAnswers = [...formData.answers];
        for (let i = currentAnswers; i < config.minAnswers; i++) {
          newAnswers.push({
            answer_text: '',
            is_correct: false,
            answer_order: i + 1
          });
        }
        setFormData(prev => ({ ...prev, answers: newAnswers }));
      } else if (currentAnswers > config.maxAnswers) {
        // Trim excess answers
        setFormData(prev => ({
          ...prev,
          answers: prev.answers.slice(0, config.maxAnswers)
        }));
      }
    }
  }, [formData.answer_type, lookupData.answerTypes]);

  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Note: Answer type is now independent of question type - user selects both explicitly
  };

  const updateAnswer = (index, field, value) => {
    const newAnswers = [...formData.answers];
    newAnswers[index] = { ...newAnswers[index], [field]: value };
    setFormData(prev => ({ ...prev, answers: newAnswers }));
  };

  const addAnswer = () => {
    const answerTypeConfigs = getAnswerTypeConfig(lookupData.answerTypes);
    const config = answerTypeConfigs[formData.answer_type];

    if (!config || formData.answers.length >= config.maxAnswers) return;

    const newAnswer = {
      answer_text: '',
      is_correct: false,
      answer_order: formData.answers.length + 1
    };
    setFormData(prev => ({
      ...prev,
      answers: [...prev.answers, newAnswer]
    }));
  };

  const removeAnswer = (index) => {
    const answerTypeConfigs = getAnswerTypeConfig(lookupData.answerTypes);
    const config = answerTypeConfigs[formData.answer_type];

    if (!config || formData.answers.length <= config.minAnswers) return;

    const newAnswers = formData.answers.filter((_, i) => i !== index);
    // Reorder answers
    newAnswers.forEach((answer, i) => {
      answer.answer_order = i + 1;
    });
    setFormData(prev => ({ ...prev, answers: newAnswers }));
  };

  const setCorrectAnswer = (index) => {
    const answerTypeConfigs = getAnswerTypeConfig(lookupData.answerTypes);
    const config = answerTypeConfigs[formData.answer_type];

    const newAnswers = formData.answers.map((answer, i) => ({
      ...answer,
      is_correct: config?.allowMultipleCorrect ?
        (i === index ? !answer.is_correct : answer.is_correct) :
        i === index
    }));
    setFormData(prev => ({ ...prev, answers: newAnswers }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate that we have active lookup data
    if (lookupData.difficulties.length === 0) {
      newErrors.system = 'No active difficulty levels available. Please contact administrator.';
    }

    if (lookupData.questionTypes.length === 0) {
      newErrors.system = 'No active question types available. Please contact administrator.';
    }

    if (lookupData.answerTypes.length === 0) {
      newErrors.system = 'No active answer types available. Please contact administrator.';
    }

    if (!formData.songs_master_id) {
      newErrors.songs_master_id = 'Please select a song';
    }

    if (!formData.question_text.trim()) {
      newErrors.question_text = 'Question text is required';
    } else if (formData.question_text.trim().length < 10) {
      newErrors.question_text = 'Question text must be at least 10 characters';
    } else if (formData.question_text.trim().length > 500) {
      newErrors.question_text = 'Question text must be less than 500 characters';
    }

    if (!formData.question_type) {
      newErrors.question_type = 'Please select a question type';
    } else {
      // Validate that the selected question type is still active
      const isQuestionTypeActive = lookupData.questionTypes.some(qt => qt.type_name === formData.question_type);
      if (!isQuestionTypeActive) {
        newErrors.question_type = 'Selected question type is no longer active. Please select another.';
      }
    }

    if (!formData.answer_type) {
      newErrors.answer_type = 'Please select an answer type';
    } else {
      // Validate that the selected answer type is still active
      const isAnswerTypeActive = lookupData.answerTypes.some(at => at.type_name === formData.answer_type);
      if (!isAnswerTypeActive) {
        newErrors.answer_type = 'Selected answer type is no longer active. Please select another.';
      }
    }

    if (!formData.difficulty_id) {
      newErrors.difficulty_id = 'Please select a difficulty level';
    } else {
      // Validate that the selected difficulty is still active
      const isDifficultyActive = lookupData.difficulties.some(d => d.difficulty_id === parseInt(formData.difficulty_id));
      if (!isDifficultyActive) {
        newErrors.difficulty_id = 'Selected difficulty level is no longer active. Please select another.';
      }
    }

    if (formData.time_limit_seconds < 5 || formData.time_limit_seconds > 600) {
      newErrors.time_limit_seconds = 'Time limit must be between 5 and 600 seconds';
    }

    if (formData.points_value < 1 || formData.points_value > 100) {
      newErrors.points_value = 'Points value must be between 1 and 100';
    }

    // Validate answers
    const hasCorrectAnswer = formData.answers.some(answer => answer.is_correct);
    if (!hasCorrectAnswer) {
      newErrors.answers = 'At least one answer must be marked as correct';
    }

    const hasEmptyAnswers = formData.answers.some(answer => !answer.answer_text.trim());
    if (hasEmptyAnswers) {
      newErrors.answers = 'All answer fields must be filled';
    }

    // Validate answer text length
    const hasLongAnswers = formData.answers.some(answer => answer.answer_text.trim().length > 200);
    if (hasLongAnswers) {
      newErrors.answers = 'Answer text must be less than 200 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });
      return;
    }

    setLoading(true);
    try {
      // Find the question type and answer type objects for additional validation
      const selectedQuestionType = lookupData.questionTypes.find(qt => qt.type_name === formData.question_type);
      const selectedAnswerType = lookupData.answerTypes.find(at => at.type_name === formData.answer_type);
      const selectedDifficulty = lookupData.difficulties.find(d => d.difficulty_id === parseInt(formData.difficulty_id));

      console.log('Form submission data:', {
        questionType: selectedQuestionType,
        answerType: selectedAnswerType,
        difficulty: selectedDifficulty
      });

      // Prepare payload according to server schema
      const payload = {
        songs_master_id: parseInt(formData.songs_master_id),
        question_text: formData.question_text.trim(),
        difficulty_id: parseInt(formData.difficulty_id),
        time_limit_seconds: parseInt(formData.time_limit_seconds),
        answer_type: formData.answer_type || formData.question_type, // Use answer_type if available, fallback to question_type
        answers: formData.answers.map(answer => ({
          answer_text: answer.answer_text.trim(),
          is_correct: Boolean(answer.is_correct),
          answer_order: parseInt(answer.answer_order)
        }))
      };

      console.log('Submitting payload:', payload); // Debug logging

      const url = editingQuestion
        ? `${process.env.REACT_APP_API_URL}/api/admin/questions/${editingQuestion.question_id}`
        : `${process.env.REACT_APP_API_URL}/api/admin/questions`;

      const method = editingQuestion ? 'PUT' : 'POST';

      const response = await axios({
        method,
        url,
        data: payload,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Server response:', response.data); // Debug logging

      enqueueSnackbar(
        editingQuestion ? 'Quiz question updated successfully!' : 'Quiz question created successfully!',
        { variant: 'success' }
      );

      // Call success callback
      if (onSuccess) {
        onSuccess(response.data);
      }

      // Reset form if not editing
      if (!editingQuestion) {
        resetForm();
      }

    } catch (error) {
      console.error('Error submitting question:', error);
      console.error('Error response:', error.response?.data); // Debug logging

      let errorMessage = 'Failed to save quiz question';

      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        errorMessage = `Validation error: ${JSON.stringify(error.response.data.details)}`;
      } else if (error.message) {
        errorMessage = error.message;
      }

      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    const defaultQuestionType = lookupData.questionTypes.find(qt => qt.type_name === 'multiple_choice') || lookupData.questionTypes[0];
    const defaultAnswerType = lookupData.answerTypes.find(at => at.type_name === 'single_choice') || lookupData.answerTypes[0];

    setFormData({
      songs_master_id: '',
      question_text: '',
      question_type: defaultQuestionType?.type_name || '',
      answer_type: defaultAnswerType?.type_name || '',
      difficulty_id: '',
      time_limit_seconds: 30,
      points_value: 10,
      answers: [
        { answer_text: '', is_correct: false, answer_order: 1 },
        { answer_text: '', is_correct: false, answer_order: 2 }
      ]
    });
    setErrors({});
  };

  const selectedSong = songs.find(song => song.songs_master_id === parseInt(formData.songs_master_id));
  const answerTypeConfigs = getAnswerTypeConfig(lookupData.answerTypes);
  const currentAnswerConfig = answerTypeConfigs[formData.answer_type];
  const selectedQuestionType = lookupData.questionTypes.find(qt => qt.type_name === formData.question_type);
  const selectedAnswerType = lookupData.answerTypes.find(at => at.type_name === formData.answer_type);

  // Show loading state while data is being fetched
  if (dataLoading) {
    return (
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3, textAlign: 'center' }}>
        <CircularProgress sx={{ color: '#4CAF50', mb: 2 }} />
        <Typography variant="h6" sx={{ color: '#fff' }}>
          Loading form data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" sx={{ color: '#fff', mb: 3, fontWeight: 600 }}>
        {editingQuestion ? 'Edit Quiz Question' : 'Create Quiz Question'}
      </Typography>

      <StyledCard>
        <CardContent>
          <Box component="form" onSubmit={handleSubmit}>
            <Stack spacing={3}>
              {/* System Error Alert */}
              {errors.system && (
                <Alert severity="error" sx={{ backgroundColor: 'rgba(244, 67, 54, 0.1)' }}>
                  {errors.system}
                </Alert>
              )}
              {/* Song Selection */}
              <StyledFormControl fullWidth error={!!errors.songs_master_id}>
                <InputLabel>Select Song</InputLabel>
                <Select
                  value={formData.songs_master_id}
                  onChange={(e) => updateFormField('songs_master_id', e.target.value)}
                  label="Select Song"
                  disabled={loading || songsLoading}
                >
                  <MenuItem value="">
                    <em>Choose a song...</em>
                  </MenuItem>
                  {songs.map((song) => (
                    <MenuItem key={song.songs_master_id} value={song.songs_master_id}>
                      <Box>
                        <Typography variant="body1" sx={{ color: '#fff' }}>
                          {song.name}
                        </Typography>
                        {song.artist && (
                          <Typography variant="caption" sx={{ color: '#ccc' }}>
                            by {song.artist}
                          </Typography>
                        )}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                {errors.songs_master_id && (
                  <FormHelperText>{errors.songs_master_id}</FormHelperText>
                )}
              </StyledFormControl>

              {/* Selected Song Preview */}
              {selectedSong && (
                <Fade in={!!selectedSong}>
                  <Alert
                    icon={<PreviewIcon />}
                    severity="info"
                    sx={{
                      backgroundColor: 'rgba(33, 150, 243, 0.1)',
                      border: '1px solid #2196F3',
                      '& .MuiAlert-message': { color: '#fff' }
                    }}
                  >
                    <Typography variant="body2">
                      <strong>Selected:</strong> {selectedSong.name}
                      {selectedSong.artist && ` by ${selectedSong.artist}`}
                      {selectedSong.album && ` (${selectedSong.album})`}
                    </Typography>
                  </Alert>
                </Fade>
              )}

              {/* Question Text */}
              <StyledTextField
                label="Question Text"
                multiline
                rows={3}
                value={formData.question_text}
                onChange={(e) => updateFormField('question_text', e.target.value)}
                placeholder="Enter your quiz question here..."
                error={!!errors.question_text}
                helperText={errors.question_text}
                disabled={loading}
                fullWidth
              />

              {/* Question Type and Answer Type Row */}
              <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={2}>
                <StyledFormControl error={!!errors.question_type}>
                  <InputLabel>Question Type</InputLabel>
                  <Select
                    value={formData.question_type}
                    onChange={(e) => updateFormField('question_type', e.target.value)}
                    label="Question Type"
                    disabled={loading || dataLoading}
                  >
                    <MenuItem value="">
                      <em>Select question type...</em>
                    </MenuItem>
                    {lookupData.questionTypes.map((type) => (
                      <MenuItem key={type.question_type_id} value={type.type_name}>
                        <Box>
                          <Typography variant="body1" sx={{ color: '#fff' }}>
                            {type.display_name}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#ccc' }}>
                            {type.type_name}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.question_type && (
                    <FormHelperText>{errors.question_type}</FormHelperText>
                  )}
                </StyledFormControl>

                <StyledFormControl error={!!errors.answer_type}>
                  <InputLabel>Answer Type</InputLabel>
                  <Select
                    value={formData.answer_type}
                    onChange={(e) => updateFormField('answer_type', e.target.value)}
                    label="Answer Type"
                    disabled={loading || dataLoading}
                  >
                    <MenuItem value="">
                      <em>Select answer type...</em>
                    </MenuItem>
                    {lookupData.answerTypes.map((type) => (
                      <MenuItem key={type.answer_type_id} value={type.type_name}>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body1" sx={{ color: '#fff' }}>
                            {currentAnswerConfig && currentAnswerConfig[type.type_name] ?
                              currentAnswerConfig[type.type_name].icon : '📝'
                            }
                          </Typography>
                          <Box>
                            <Typography variant="body1" sx={{ color: '#fff' }}>
                              {type.display_name}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#ccc' }}>
                              {answerTypeConfigs[type.type_name]?.description || type.type_name}
                            </Typography>
                          </Box>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.answer_type && (
                    <FormHelperText>{errors.answer_type}</FormHelperText>
                  )}
                </StyledFormControl>
              </Box>

              {/* Difficulty and Settings Row */}
              <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={2}>

                <StyledFormControl error={!!errors.difficulty_id}>
                  <InputLabel>Difficulty Level</InputLabel>
                  <Select
                    value={formData.difficulty_id}
                    onChange={(e) => updateFormField('difficulty_id', e.target.value)}
                    label="Difficulty Level"
                    disabled={loading}
                  >
                    <MenuItem value="">
                      <em>Select difficulty...</em>
                    </MenuItem>
                    {lookupData.difficulties.map((difficulty) => (
                      <MenuItem key={difficulty.difficulty_id} value={difficulty.difficulty_id}>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography sx={{ color: '#fff' }}>{difficulty.level_name}</Typography>
                          {difficulty.bonus_points && (
                            <Chip
                              label={`+${difficulty.bonus_points}pts`}
                              size="small"
                              sx={{
                                backgroundColor: '#4CAF50',
                                color: '#fff',
                                fontSize: '0.7rem'
                              }}
                            />
                          )}
                          {difficulty.time_multiplier && difficulty.time_multiplier !== 1 && (
                            <Chip
                              label={`${difficulty.time_multiplier}x time`}
                              size="small"
                              sx={{
                                backgroundColor: '#2196F3',
                                color: '#fff',
                                fontSize: '0.7rem'
                              }}
                            />
                          )}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.difficulty_id && (
                    <FormHelperText>{errors.difficulty_id}</FormHelperText>
                  )}
                </StyledFormControl>

                <StyledTextField
                  label="Time Limit (seconds)"
                  type="number"
                  value={formData.time_limit_seconds}
                  onChange={(e) => updateFormField('time_limit_seconds', parseInt(e.target.value) || 30)}
                  error={!!errors.time_limit_seconds}
                  helperText={errors.time_limit_seconds || 'Between 5-300 seconds'}
                  disabled={loading}
                  inputProps={{ min: 5, max: 300 }}
                />

                <StyledTextField
                  label="Base Points"
                  type="number"
                  value={formData.points_value}
                  onChange={(e) => updateFormField('points_value', parseInt(e.target.value) || 10)}
                  error={!!errors.points_value}
                  helperText={errors.points_value || 'Between 1-100 points'}
                  disabled={loading}
                  inputProps={{ min: 1, max: 100 }}
                />
              </Box>

              <Divider sx={{ borderColor: '#333', my: 2 }} />

              {/* Answer Options Section */}
              <Box>
                <Typography variant="h6" sx={{ color: '#fff', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                  Answer Options
                  {selectedAnswerType && (
                    <Chip
                      label={`${currentAnswerConfig?.icon || '📝'} ${selectedAnswerType.display_name}`}
                      size="small"
                      sx={{ backgroundColor: '#2196F3', color: '#fff' }}
                    />
                  )}
                  {currentAnswerConfig && (
                    <Chip
                      label={`${currentAnswerConfig.minAnswers}-${currentAnswerConfig.maxAnswers} answers`}
                      size="small"
                      sx={{ backgroundColor: '#333', color: '#ccc' }}
                    />
                  )}
                  {currentAnswerConfig && (
                    <Chip
                      label={currentAnswerConfig.allowMultipleCorrect ? 'Multiple Correct' : 'Single Correct'}
                      size="small"
                      sx={{ backgroundColor: currentAnswerConfig.allowMultipleCorrect ? '#4CAF50' : '#FF9800', color: '#fff' }}
                    />
                  )}
                </Typography>

                {errors.answers && (
                  <Alert severity="error" sx={{ mb: 2, backgroundColor: 'rgba(244, 67, 54, 0.1)' }}>
                    {errors.answers}
                  </Alert>
                )}

                <Stack spacing={2}>
                  {formData.answers.map((answer, index) => (
                    <Slide key={index} direction="up" in={true} timeout={300 + index * 100}>
                      <AnswerCard isCorrect={answer.is_correct}>
                        <CardContent sx={{ p: 2 }}>
                          <Box display="flex" alignItems="center" gap={2}>
                            {/* Correct Answer Selection */}
                            {currentAnswerConfig?.allowMultipleCorrect && (
                              <Checkbox
                                checked={answer.is_correct}
                                onChange={() => setCorrectAnswer(index)}
                                disabled={loading}
                                sx={{
                                  color: '#4CAF50',
                                  '&.Mui-checked': {
                                    color: '#4CAF50',
                                  },
                                }}
                              />
                            )}

                            {currentAnswerConfig && !currentAnswerConfig.allowMultipleCorrect && (
                              <Radio
                                checked={answer.is_correct}
                                onChange={() => setCorrectAnswer(index)}
                                disabled={loading}
                                sx={{
                                  color: '#4CAF50',
                                  '&.Mui-checked': {
                                    color: '#4CAF50',
                                  },
                                }}
                              />
                            )}

                            {/* Answer Text */}
                            <StyledTextField
                              label={`Answer ${index + 1}`}
                              value={answer.answer_text}
                              onChange={(e) => updateAnswer(index, 'answer_text', e.target.value)}
                              disabled={loading || (formData.answer_type === 'true_false')}
                              fullWidth
                              placeholder={`Enter answer option ${index + 1}...`}
                            />

                            {/* Remove Answer Button */}
                            {currentAnswerConfig &&
                             formData.answer_type !== 'true_false' &&
                             formData.answers.length > currentAnswerConfig.minAnswers && (
                              <IconButton
                                onClick={() => removeAnswer(index)}
                                disabled={loading}
                                sx={{
                                  color: '#F44336',
                                  '&:hover': { backgroundColor: 'rgba(244, 67, 54, 0.1)' }
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            )}
                          </Box>

                          {answer.is_correct && (
                            <Box mt={1} display="flex" alignItems="center" gap={1}>
                              <CheckCircleIcon sx={{ color: '#4CAF50', fontSize: 16 }} />
                              <Typography variant="caption" sx={{ color: '#4CAF50' }}>
                                Correct Answer
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </AnswerCard>
                    </Slide>
                  ))}

                  {/* Add Answer Button */}
                  {currentAnswerConfig &&
                   formData.answer_type !== 'true_false' &&
                   formData.answers.length < currentAnswerConfig.maxAnswers && (
                    <ActionButton
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={addAnswer}
                      disabled={loading}
                      sx={{ alignSelf: 'flex-start' }}
                    >
                      Add Answer Option
                    </ActionButton>
                  )}
                </Stack>
              </Box>

              <Divider sx={{ borderColor: '#333', my: 2 }} />

              {/* Form Actions */}
              <Box display="flex" gap={2} justifyContent="flex-end">
                <ActionButton
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={resetForm}
                  disabled={loading}
                >
                  Reset Form
                </ActionButton>

                <ActionButton
                  type="submit"
                  variant="contained"
                  startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
                  disabled={loading}
                >
                  {loading
                    ? (editingQuestion ? 'Updating...' : 'Creating...')
                    : (editingQuestion ? 'Update Question' : 'Create Question')
                  }
                </ActionButton>
              </Box>
            </Stack>
          </Box>
        </CardContent>
      </StyledCard>
    </Box>
  );
};

export default QuizQuestionForm;
