import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
  Paper,
  Typography,
  Checkbox,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Snackbar,
  Tooltip,
  Chip,
  LinearProgress,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Publish as PublishIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
} from "@mui/icons-material";
import axios from "axios";

const SpotifyTracksTable = ({
  tracks,
  onEdit,
  fetchTracks,
  onMigrationComplete,
  onSongUpdate,
  urlRefreshTrigger,
}) => {
  // State management
  const [selectedIds, setSelectedIds] = useState([]);
  const [trackUrls, setTrackUrls] = useState({}); // Store URLs for each track
  const [loadingStates, setLoadingStates] = useState({
    migrate: {},
    delete: {},
    batch: false,
    urls: false,
  });

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: "",
    message: "",
    onConfirm: null,
    loading: false,
  });

  // Filter out any migrated tracks that might be in the list
  const filteredTracks = tracks.filter((track) => !track.is_migrated);

  // Fetch URLs for all tracks
  const fetchAllTrackUrls = useCallback(async () => {
    if (filteredTracks.length === 0) return;

    setLoadingStates((prev) => ({ ...prev, urls: true }));

    try {
      const urlPromises = filteredTracks.map((track) =>
        axios
          .get(
            `${process.env.REACT_APP_API_URL}/api/import/track-urls/${track.id}`
          )
          .then((response) => ({ trackId: track.id, urls: response.data }))
          .catch((error) => ({ trackId: track.id, urls: [] }))
      );

      const urlResults = await Promise.all(urlPromises);

      const urlsMap = {};
      urlResults.forEach(({ trackId, urls }) => {
        urlsMap[trackId] = urls;
      });

      setTrackUrls(urlsMap);
    } catch (error) {
      console.error("Error fetching track URLs:", error);
    } finally {
      setLoadingStates((prev) => ({ ...prev, urls: false }));
    }
  }, []);

  const filteredTrackIds = React.useMemo(
    () =>
      tracks
        .filter((track) => !track.is_migrated)
        .map((track) => track.id)
        .join(","),
    [tracks]
  );

  useEffect(() => {
    fetchAllTrackUrls();
  }, [filteredTrackIds, urlRefreshTrigger]); // Re-fetch when tracks change or URL refresh is triggered

  // Determine row color based on completion status
  const getStatusInfo = (track) => {
    const hasSpotifyId = !!track.spotify_track_id;
    const trackUrlList = trackUrls[track.id] || [];
    const hasAnyUrl = trackUrlList.length > 0;

    if (hasSpotifyId && hasAnyUrl) {
      return {
        text: "Ready to Migrate",
        color: "success",
        icon: <CheckCircleIcon fontSize="small" />,
        bgColor: "rgba(46, 125, 50, 0.08)",
      };
    } else if (hasSpotifyId || hasAnyUrl) {
      return {
        text: hasSpotifyId ? "Needs URL" : "Needs Spotify ID",
        color: "warning",
        icon: <WarningIcon fontSize="small" />,
        bgColor: "rgba(237, 108, 2, 0.08)",
      };
    }
    return {
      text: "Incomplete",
      color: "error",
      icon: <InfoIcon fontSize="small" />,
      bgColor: "rgba(211, 47, 47, 0.08)",
    };
  };

  // Selection handlers
  const toggleSelect = (trackId) => {
    setSelectedIds((prev) =>
      prev.includes(trackId)
        ? prev.filter((id) => id !== trackId)
        : [...prev, trackId]
    );
  };

  const handleSelectAll = (event) => {
    setSelectedIds(
      event.target.checked ? filteredTracks.map((track) => track.id) : []
    );
  };

  // Migration handlers
  const handleMigrate = async (id) => {
    setConfirmDialog({
      open: true,
      title: "Confirm Migration",
      message: "This will move the track to the main database. Continue?",
      onConfirm: async () => {
        setLoadingStates((prev) => ({
          ...prev,
          migrate: { ...prev.migrate, [id]: true },
        }));
        setConfirmDialog((prev) => ({ ...prev, loading: true }));

        try {
          await axios.post(
            `${process.env.REACT_APP_API_URL}/api/migrate/single/${id}`
          );
          setSnackbar({
            open: true,
            message: "Track migrated successfully!",
            severity: "success",
          });
          fetchTracks();
          onMigrationComplete?.();
        } catch (error) {
          setSnackbar({
            open: true,
            message: error.response?.data?.error || "Migration failed",
            severity: "error",
          });
        } finally {
          setLoadingStates((prev) => ({
            ...prev,
            migrate: { ...prev.migrate, [id]: false },
          }));
          setConfirmDialog((prev) => ({
            ...prev,
            open: false,
            loading: false,
          }));
        }
      },
    });
  };

  const handleBatchMigrate = () => {
    setConfirmDialog({
      open: true,
      title: "Batch Migration",
      message: `Migrate ${selectedIds.length} selected tracks to the main database?`,
      onConfirm: async () => {
        setLoadingStates((prev) => ({ ...prev, batch: true }));
        setConfirmDialog((prev) => ({ ...prev, loading: true }));

        try {
          const response = await axios.post(
            `${process.env.REACT_APP_API_URL}/api/migrate/all`,
            { track_ids: selectedIds }
          );

          setSnackbar({
            open: true,
            message: response.data.success
              ? `Successfully migrated ${response.data.migrated} tracks`
              : `${response.data.details.failed.length} tracks failed to migrate`,
            severity: response.data.success ? "success" : "error",
          });

          setSelectedIds([]);
          fetchTracks();
          onMigrationComplete?.();
        } catch (error) {
          setSnackbar({
            open: true,
            message: error.response?.data?.error || "Batch migration failed",
            severity: "error",
          });
        } finally {
          setLoadingStates((prev) => ({ ...prev, batch: false }));
          setConfirmDialog((prev) => ({
            ...prev,
            open: false,
            loading: false,
          }));
        }
      },
    });
  };

  // Delete handler
  const handleDelete = async (id) => {
    setConfirmDialog({
      open: true,
      title: "Delete Track",
      message: "This will permanently remove the temporary track. Continue?",
      onConfirm: async () => {
        setLoadingStates((prev) => ({
          ...prev,
          delete: { ...prev.delete, [id]: true },
        }));
        setConfirmDialog((prev) => ({ ...prev, loading: true }));

        try {
          await axios.delete(
            `${process.env.REACT_APP_API_URL}/api/migrate/temp/${id}`
          );
          setSnackbar({
            open: true,
            message: "Track deleted successfully",
            severity: "success",
          });
          fetchTracks();
        } catch (error) {
          setSnackbar({
            open: true,
            message: error.response?.data?.error || "Deletion failed",
            severity: "error",
          });
        } finally {
          setLoadingStates((prev) => ({
            ...prev,
            delete: { ...prev.delete, [id]: false },
          }));
          setConfirmDialog((prev) => ({
            ...prev,
            open: false,
            loading: false,
          }));
        }
      },
    });
  };

  if (filteredTracks.length === 0) {
    return (
      <Paper elevation={0} sx={{ p: 4, textAlign: "center", borderRadius: 2 }}>
        <Typography variant="subtitle1" color="text.secondary">
          No temporary tracks awaiting migration
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          All tracks have been processed or migrated.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper
      elevation={0}
      sx={{
        p: { xs: 1.5, sm: 3 },
        borderRadius: 2,
        bgcolor: "#121212",
        color: "white",
        width: "100%",
        overflowX: "auto",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-between",
          alignItems: { xs: "stretch", sm: "center" },
          mb: 3,
          gap: 2,
        }}
      >
        <Typography
          variant="h6"
          fontWeight="medium"
          sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}
        >
          Tracks Pending Migration ({filteredTracks.length})
        </Typography>

        {selectedIds.length > 0 && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleBatchMigrate}
            disabled={loadingStates.batch}
            startIcon={<PublishIcon />}
            sx={{
              borderRadius: 2,
              textTransform: "none",
              px: 3,
              py: 1,
              boxShadow: "none",
              fontSize: { xs: "0.8rem", sm: "0.9rem" },
            }}
          >
            {loadingStates.batch ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Migrating...
              </>
            ) : (
              `Migrate Selected (${selectedIds.length})`
            )}
          </Button>
        )}
      </Box>

      {loadingStates.urls && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Loading track URLs...
          </Typography>
          <LinearProgress />
        </Box>
      )}

      <TableContainer sx={{ overflowX: "auto" }}>
        <Table size="small">
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: "#1DB954",
                "& th": {
                  color: "white",
                  fontWeight: "bold",
                  borderBottom: "2px solid #1DB954",
                },
              }}
            >
              <TableCell
                padding="checkbox"
                sx={{
                  borderTopLeftRadius: 8,
                  borderBottomLeftRadius: 0,
                  color: "white",
                }}
              >
                <Checkbox
                  indeterminate={
                    selectedIds.length > 0 &&
                    selectedIds.length < filteredTracks.length
                  }
                  checked={
                    filteredTracks.length > 0 &&
                    selectedIds.length === filteredTracks.length
                  }
                  onChange={handleSelectAll}
                  sx={{
                    color: "white",
                    "&.Mui-checked": { color: "white" },
                  }}
                />
              </TableCell>
              <TableCell>Track</TableCell>
              <TableCell>Artist</TableCell>
              <TableCell>Album</TableCell>
              <TableCell>Year</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {filteredTracks.map((track) => {
              const isSelected = selectedIds.includes(track.id);
              const isMigrating = loadingStates.migrate[track.id];
              const isDeleting = loadingStates.delete[track.id];
              const statusInfo = getStatusInfo(track);

              return (
                <TableRow
                  key={track.id}
                  hover
                  selected={isSelected}
                  sx={{
                    "&:last-child td": { borderBottom: 0 },
                    backgroundColor: statusInfo.bgColor,
                    "&:hover": {
                      backgroundColor: `${statusInfo.bgColor.replace(
                        "0.08",
                        "0.12"
                      )}`,
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => toggleSelect(track.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography fontWeight="medium" fontSize="0.9rem">
                      {track.name}
                    </Typography>
                  </TableCell>
                  <TableCell>{track.artist}</TableCell>
                  <TableCell>{track.album}</TableCell>
                  <TableCell>{track.release_year}</TableCell>
                  <TableCell>{track.duration}</TableCell>
                  <TableCell>
                    <Chip
                      label={statusInfo.text}
                      color={statusInfo.color}
                      size="small"
                      icon={statusInfo.icon}
                      sx={{
                        px: 1,
                        fontSize: "0.75rem",
                        "& .MuiChip-icon": {
                          color: `${statusInfo.color}.main`,
                          ml: 0.5,
                        },
                      }}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: 1,
                      }}
                    >
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={() => onEdit(track)}
                          size="small"
                          sx={{
                            backgroundColor: "action.hover",
                            "&:hover": { backgroundColor: "action.selected" },
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Migrate">
                        <IconButton
                          onClick={() => handleMigrate(track.id)}
                          disabled={isMigrating}
                          size="small"
                          sx={{
                            backgroundColor: "success.light",
                            color: "success.dark",
                            "&:hover": {
                              backgroundColor: "success.main",
                              color: "common.white",
                            },
                            "&:disabled": {
                              backgroundColor: "action.disabledBackground",
                            },
                          }}
                        >
                          {isMigrating ? (
                            <CircularProgress size={20} />
                          ) : (
                            <PublishIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => handleDelete(track.id)}
                          disabled={isDeleting}
                          size="small"
                          sx={{
                            backgroundColor: "error.light",
                            color: "error.dark",
                            "&:hover": {
                              backgroundColor: "error.main",
                              color: "common.white",
                            },
                            "&:disabled": {
                              backgroundColor: "action.disabledBackground",
                            },
                          }}
                        >
                          {isDeleting ? (
                            <CircularProgress size={20} />
                          ) : (
                            <DeleteIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() =>
          !confirmDialog.loading &&
          setConfirmDialog((prev) => ({ ...prev, open: false }))
        }
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ fontWeight: "medium" }}>
          {confirmDialog.title}
        </DialogTitle>
        <DialogContent>
          <Typography>{confirmDialog.message}</Typography>
          {confirmDialog.loading && <LinearProgress sx={{ mt: 2 }} />}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() =>
              setConfirmDialog((prev) => ({ ...prev, open: false }))
            }
            disabled={confirmDialog.loading}
            sx={{ borderRadius: 2, textTransform: "none" }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDialog.onConfirm}
            color="primary"
            variant="contained"
            disabled={confirmDialog.loading}
            sx={{ borderRadius: 2, textTransform: "none", px: 3 }}
          >
            {confirmDialog.loading ? "Processing..." : "Confirm"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          severity={snackbar.severity}
          sx={{ width: "100%", borderRadius: 2, alignItems: "center" }}
          onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default SpotifyTracksTable;
