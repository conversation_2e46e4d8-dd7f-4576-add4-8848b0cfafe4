const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./database/app.db");

const ImportTrackUrlModel = {
  addUrl: (track_id, song_url, url_type_lookup_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `
        INSERT INTO ImportTrackUrlsTemp (track_id, song_url, url_type_lookup_id, created_at)
        VALUES (?, ?, ?, datetime('now'))
      `,
        [track_id, song_url, url_type_lookup_id],
        function (err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });
  },

  getUrlsByTrackId: (track_id) => {
    return new Promise((resolve, reject) => {
      db.all(
        `
       SELECT 
        t.id AS song_url_id,
        t.track_id,
        t.song_url,
        l.url_type_name
      FROM ImportTrackUrlsTemp t
      JOIN url_type_lookup l ON t.url_type_lookup_id = l.url_type_lookup_id
      WHERE t.track_id = ?
      ORDER BY t.created_at DESC
      `,
        [track_id],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  },

  deleteUrlById: (url_id) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM ImportTrackUrlsTemp WHERE id = ?`,
        [url_id],
        function (err) {
          if (err) reject(err);
          else resolve({ changes: this.changes });
        }
      );
    });
  },
};

module.exports = ImportTrackUrlModel;
