import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow, 
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  TextField,
  InputAdornment,
  Collapse,
  Alert, 
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText, 
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  // FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  FileCopy as DuplicateIcon,
  // GetApp as ExportIcon,
  // Publish as ImportIcon,
  Clear as ClearIcon,
  CheckCircle as CheckCircleIcon,
  // Error as ErrorIcon,
  AccessTime as TimeIcon,
  Quiz as QuizIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import {
  useQuizQuestions,
  // useQuizQuestion,
  useDeleteQuizQuestion,
  useDuplicateQuizQuestion,
  useExportQuizQuestions,
  useQuizQuestionStats
} from '../../../hooks/useQuizQuestions';
import QuizQuestionForm from './QuizQuestionForm';
import QuestionViewDialog from './QuestionViewDialog';

// Styled components following SongQ design system
const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  border: '1px solid #333',
  borderRadius: '12px',
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  backgroundColor: '#1A1A1A',
  '& .MuiTableHead-root': {
    backgroundColor: '#121212',
  },
  '& .MuiTableCell-root': {
    borderColor: '#333',
    color: '#fff',
  },
  '& .MuiTableCell-head': {
    backgroundColor: '#121212',
    fontWeight: 600,
    color: '#4CAF50',
  },
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  ...(variant === 'contained' && {
    backgroundColor: '#4CAF50',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#45a049',
    },
  }),
  ...(variant === 'outlined' && {
    borderColor: '#333',
    color: '#ccc',
    '&:hover': {
      borderColor: '#4CAF50',
      backgroundColor: 'rgba(76, 175, 80, 0.1)',
    },
  }),
}));

const SearchTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#121212',
    '& fieldset': {
      borderColor: '#333',
    },
    '&:hover fieldset': {
      borderColor: '#4CAF50',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4CAF50',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#ccc',
  },
  '& .MuiOutlinedInput-input': {
    color: '#fff',
  },
}));

const QuizQuestionManagement = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // State management
  const [showForm, setShowForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);
  const [viewDialog, setViewDialog] = useState({ open: false, question: null, answers: [] });
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteDialog, setDeleteDialog] = useState({ open: false, question: null });
  const [actionMenu, setActionMenu] = useState({ anchorEl: null, question: null });

  // API hooks
  const { data: questions = [], isLoading, error, refetch } = useQuizQuestions();
  const { data: stats } = useQuizQuestionStats();
  const deleteQuestionMutation = useDeleteQuizQuestion();
  const duplicateQuestionMutation = useDuplicateQuizQuestion();
  const exportQuestionsMutation = useExportQuizQuestions();

  // Filter questions based on search term
  const filteredQuestions = questions.filter(question =>
    question.question_text?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    question.song_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    question.level_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    question.artist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    question.answer_type?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteQuestion = async () => {
    if (!deleteDialog.question) return;

    try {
      await deleteQuestionMutation.mutateAsync(deleteDialog.question.question_id);
      enqueueSnackbar('Question deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, question: null });
    } catch (error) {
      enqueueSnackbar('Failed to delete question', { variant: 'error' });
    }
  };

  const handleDuplicateQuestion = async (question) => {
    try {
      await duplicateQuestionMutation.mutateAsync(question.question_id);
      enqueueSnackbar('Question duplicated successfully', { variant: 'success' });
      closeActionMenu();
    } catch (error) {
      enqueueSnackbar('Failed to duplicate question', { variant: 'error' });
    }
  };

  const handleExportQuestions = async () => {
    try {
      await exportQuestionsMutation.mutateAsync();
      enqueueSnackbar('Questions exported successfully', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('Failed to export questions', { variant: 'error' });
    }
  };

  const handleViewQuestion = async (question) => {
    try {
      // Fetch full question details including answers
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/${question.question_id}`);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch question details: ${response.status} - ${errorText}`);
      }

      const fullQuestion = await response.json();

      setViewDialog({
        open: true,
        question: fullQuestion,
        answers: fullQuestion.answers || []
      });
      closeActionMenu();
    } catch (error) {
      enqueueSnackbar(`Failed to load question details: ${error.message}`, { variant: 'error' });
    }
  };

  const handleEditQuestion = async (question) => {
    try {
      // Fetch full question details including answers for editing
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/questions/${question.question_id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch question details for editing');
      }

      const fullQuestion = await response.json();

      setEditingQuestion(fullQuestion);
      setShowForm(true);
      closeActionMenu();
    } catch (error) {
      enqueueSnackbar(`Failed to prepare question for editing: ${error.message}`, { variant: 'error' });
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingQuestion(null);
    refetch(); // Refresh the questions list
  };

  const openActionMenu = (event, question) => {
    setActionMenu({ anchorEl: event.currentTarget, question });
  };

  const closeActionMenu = () => {
    setActionMenu({ anchorEl: null, question: null });
  };

  const getDifficultyColor = (level) => {
    const colors = {
      'Easy': '#4CAF50',
      'Medium': '#FF9800',
      'Hard': '#F44336',
      'Very Hard': '#9C27B0',
      'Expert': '#E91E63'
    };
    return colors[level] || '#2196F3';
  };

  const formatTimeLimit = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load quiz questions. Please try again.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600, mb: 1 }}>
            Quiz Question Management
          </Typography>
          {stats && (
            <Typography variant="body2" sx={{ color: '#ccc' }}>
              {stats.totalQuestions} questions • Average time: {formatTimeLimit(stats.averageTimeLimit)}
            </Typography>
          )}
        </Box>
        
        <Stack direction="row" spacing={2}>
          {/* <ActionButton
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExportQuestions}
            disabled={exportQuestionsMutation.isLoading}
          >
            Export
          </ActionButton> */}
          
          <ActionButton
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setEditingQuestion(null);
              setShowForm(!showForm);
            }}
          >
            {showForm ? 'Hide Form' : 'Create Question'}
          </ActionButton>
        </Stack>
      </Box>

      {/* Statistics Cards */}
      {stats && (
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={2} mb={3}>
          <StyledCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <QuizIcon sx={{ fontSize: 40, color: '#4CAF50', mb: 1 }} />
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                {stats.totalQuestions}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Total Questions
              </Typography>
            </CardContent>
          </StyledCard>

          <StyledCard>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <TimeIcon sx={{ fontSize: 40, color: '#2196F3', mb: 1 }} />
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                {formatTimeLimit(stats.averageTimeLimit)}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Avg. Time Limit
              </Typography>
            </CardContent>
          </StyledCard>

          {stats.questionsByDifficulty.length > 0 && (
            <StyledCard>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <CheckCircleIcon sx={{ fontSize: 40, color: '#FF9800', mb: 1 }} />
                <Typography variant="h4" sx={{ color: '#fff', fontWeight: 600 }}>
                  {stats.questionsByDifficulty[0]?.count || 0}
                </Typography>
                <Typography variant="body2" sx={{ color: '#ccc' }}>
                  Most Common: {stats.questionsByDifficulty[0]?.level_name || 'N/A'}
                </Typography>
              </CardContent>
            </StyledCard>
          )}
        </Box>
      )}

      {/* Question Form */}
      <Collapse in={showForm}>
        <Box mb={3}>
          <QuizQuestionForm
            editingQuestion={editingQuestion}
            onSuccess={handleFormSuccess}
          />
        </Box>
      </Collapse>

      {/* Search and Filters */}
      <StyledCard sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center">
            <SearchTextField
              placeholder="Search questions, songs, or difficulty..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#ccc' }} />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setSearchTerm('')} size="small">
                      <ClearIcon sx={{ color: '#ccc' }} />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
            
            <Typography variant="body2" sx={{ color: '#ccc', minWidth: 'fit-content' }}>
              {filteredQuestions.length} of {questions.length} questions
            </Typography>
          </Box>
        </CardContent>
      </StyledCard>

      {/* Questions Table */}
      <StyledCard>
        <StyledTableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Question</TableCell>
                <TableCell>Song</TableCell>
                <TableCell>Difficulty</TableCell>
                <TableCell>Answer Type</TableCell>
                <TableCell>Time Limit</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <CircularProgress sx={{ color: '#4CAF50' }} />
                  </TableCell>
                </TableRow>
              ) : filteredQuestions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" sx={{ color: '#ccc' }}>
                      {searchTerm ? 'No questions match your search' : 'No questions created yet'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredQuestions.map((question) => (
                  <TableRow key={question.question_id} hover>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: '#fff', maxWidth: 300 }}>
                        {question.question_text?.length > 100 
                          ? `${question.question_text.substring(0, 100)}...`
                          : question.question_text
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ color: '#fff' }}>
                          {question.song_name}
                        </Typography>
                        {question.artist && (
                          <Typography variant="caption" sx={{ color: '#ccc' }}>
                            by {question.artist}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={question.level_name}
                        size="small"
                        sx={{
                          backgroundColor: getDifficultyColor(question.level_name),
                          color: '#fff',
                          fontWeight: 600
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={question.answer_type_display || question.answer_type || 'Multiple Choice'}
                        size="small"
                        sx={{
                          backgroundColor: '#2196F3',
                          color: '#fff',
                          fontWeight: 500
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: '#ccc' }}>
                        {formatTimeLimit(question.time_limit_seconds)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ color: '#ccc' }}>
                        {new Date(question.created_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        onClick={(e) => openActionMenu(e, question)}
                        sx={{ color: '#ccc' }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </StyledTableContainer>
      </StyledCard>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenu.anchorEl}
        open={Boolean(actionMenu.anchorEl)}
        onClose={closeActionMenu}
        PaperProps={{
          sx: {
            backgroundColor: '#1A1A1A',
            border: '1px solid #333',
            '& .MuiMenuItem-root': {
              color: '#fff',
              '&:hover': {
                backgroundColor: '#333',
              },
            },
          },
        }}
      >
        <MenuItem onClick={() => handleViewQuestion(actionMenu.question)}>
          <ListItemIcon>
            <ViewIcon sx={{ color: '#2196F3' }} />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => handleEditQuestion(actionMenu.question)}>
          <ListItemIcon>
            <EditIcon sx={{ color: '#FF9800' }} />
          </ListItemIcon>
          <ListItemText>Edit Question</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleDuplicateQuestion(actionMenu.question)}>
          <ListItemIcon>
            <DuplicateIcon sx={{ color: '#4CAF50' }} />
          </ListItemIcon>
          <ListItemText>Duplicate</ListItemText>
        </MenuItem>
        
        <MenuItem 
          onClick={() => {
            setDeleteDialog({ open: true, question: actionMenu.question });
            closeActionMenu();
          }}
        >
          <ListItemIcon>
            <DeleteIcon sx={{ color: '#F44336' }} />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, question: null })}
        PaperProps={{
          sx: {
            backgroundColor: '#1A1A1A',
            border: '1px solid #333',
          },
        }}
      >
        <DialogTitle sx={{ color: '#fff' }}>
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ color: '#ccc' }}>
            Are you sure you want to delete this quiz question? This action cannot be undone.
          </Typography>
          {deleteDialog.question && (
            <Box mt={2} p={2} sx={{ backgroundColor: '#121212', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ color: '#fff' }}>
                "{deleteDialog.question.question_text}"
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialog({ open: false, question: null })}
            sx={{ color: '#ccc' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteQuestion}
            variant="contained"
            color="error"
            disabled={deleteQuestionMutation.isLoading}
          >
            {deleteQuestionMutation.isLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Question View Dialog */}
      <QuestionViewDialog
        open={viewDialog.open}
        onClose={() => setViewDialog({ open: false, question: null, answers: [] })}
        question={viewDialog.question}
        answers={viewDialog.answers}
      />
    </Box>
  );
};

export default QuizQuestionManagement;
