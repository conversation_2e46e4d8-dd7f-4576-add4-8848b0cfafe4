import React from "react";
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Switch,
  FormControlLabel,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { STORAGE_KEYS, FIELD_MAP } from "./constants";
import { lookupService } from "./lookupService";

const LookupList = ({
  tab,
  data,
  setItems,
  handleDelete,
  handleStatusToggle,
  handleSettingsToggle,
  setEditIndex,
  setOpenModal,
}) => {
  // Debug logging
  console.log('LookupList - Current tab:', tab);
  console.log('LookupList - Data for tab:', data[tab]);
  console.log('LookupList - All data:', data);

  if (!data[tab] || !Array.isArray(data[tab])) {
    return (
      <div style={{ color: '#fff', padding: '20px' }}>
        <p>No data available for this tab (tab {tab})</p>
        <p>Data type: {typeof data[tab]}</p>
        <p>Data value: {JSON.stringify(data[tab])}</p>
      </div>
    );
  }

  if (data[tab].length === 0) {
    return (
      <div style={{ color: '#fff', padding: '20px' }}>
        <p>No records found for {STORAGE_KEYS[tab]} (tab {tab})</p>
        <p>This tab appears to be empty. Try adding some data!</p>
      </div>
    );
  }

  return (
    <List>
      {data[tab].map((item, index) => {
        if (tab === 4) {
          return (
            <ListItem key={item.id || index}>
              <ListItemText
                primary={item.name}
                secondary={item.description}
              />
              <ListItemSecondaryAction>
                <FormControlLabel
                  control={
                    <Switch
                      checked={item.value}
                      onChange={(e) =>
                        handleSettingsToggle(e.target.checked)
                      }
                    />
                  }
                  label={item.value ? 'Show All Options' : 'Show Only "Sing for Me"'}
                />
              </ListItemSecondaryAction>
            </ListItem>
          );
        }

        const type = STORAGE_KEYS[tab];
        const fields = FIELD_MAP[type];
        return (
          <ListItem key={item[fields.id] || index}>
            <ListItemText
              primary={`${item[fields.name] || "Unnamed"} (${item[fields.status] === 1 ? "Active" : "Inactive"
                })`}
              secondary={
                <>
                  {item[fields.description] || "No description available"}{" "}
                  <br />
                  <strong>Created:</strong>{" "}
                  {item[fields.createdAt]
                    ? new Date(item[fields.createdAt]).toLocaleString()
                    : "Not specified"}
                </>
              }
            />
            <ListItemSecondaryAction sx={{ display: "flex", gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={item[fields.status] === 1}
                    onChange={(e) =>
                      handleStatusToggle(
                        e,
                        index,
                        tab,
                        data,
                        setItems,
                        STORAGE_KEYS[tab]
                      )
                    }
                  />
                }
                label=""
              />
              <IconButton
                edge="end"
                onClick={() => {
                  setEditIndex(index);
                  setOpenModal(true);
                }}
              >
                <EditIcon />
              </IconButton>
              <IconButton
                edge="end"
                onClick={() => handleDelete(tab, index)}
              >
                <DeleteIcon />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        );
      })}
    </List>
  );
};

export default LookupList;