import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  Container,
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Pagination,
  Stack,
  IconButton,
} from "@mui/material";
import KaraokeIcon from '@mui/icons-material/MusicNote';
import LyricsIcon from '@mui/icons-material/LibraryMusic';
import Header from './Header';


const TrackListPage = () => {
  const { playlistId } = useParams();
  const [tracks, setTracks] = useState([]);
  const [playlistTitle, setPlaylistTitle] = useState("");
  const [playlistImage, setPlaylistImage] = useState("");
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTrack, setSelectedTrack] = useState(null);
  const [karaokeUrl, setKaraokeUrl] = useState("");
  const [lyricsUrl, setLyricsUrl] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const tracksPerPage = 100;
  const [trackName, setTrackName] = useState("");
  const [trackArtist, setTrackArtist] = useState("");

  useEffect(() => {
    const fetchTracks = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/playlist-details?playlistId=${playlistId}`);
        const data = await response.json();
        setTracks(data.tracks);
        setPlaylistTitle(data.title);
        setPlaylistImage(data.image);
      } catch (error) {
        console.error("Failed to fetch tracks:", error);
      }
    };

    if (playlistId) {
      fetchTracks();
    }
  }, [playlistId]);

  const handleTrackEdit = (track) => {
    setSelectedTrack(track);
    setTrackName(track.name || "");
    setTrackArtist(track.artist || "");
    setKaraokeUrl(track.karaokeUrl || "");
    setLyricsUrl(track.lyricsUrl || "");
    setOpenEditDialog(true);
  };

  const handleEditSubmit = async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlists/${playlistId}/tracks/${selectedTrack.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: trackName,
            artist: trackArtist,
            karaokeUrl,
            lyricsUrl,
          }),
        }
      );
      const updatedPlaylist = await response.json();
      setTracks(updatedPlaylist.tracks);
      setOpenEditDialog(false);
    } catch (error) {
      console.error("Failed to update track:", error);
    }
  };

  const handleTrackDelete = async () => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/playlists/${playlistId}/tracks/${selectedTrack.id}`,
        { method: "DELETE" }
      );
      const updatedPlaylist = await response.json();
      setTracks(updatedPlaylist.tracks);
      setOpenDeleteDialog(false);
    } catch (error) {
      console.error("Failed to delete track:", error);
    }
  };

  const handleDeleteClick = (track) => {
    setSelectedTrack(track);
    setOpenDeleteDialog(true);
  };

  const handleDeleteCancel = () => {
    setOpenDeleteDialog(false);
    setSelectedTrack(null);
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const startIndex = (currentPage - 1) * tracksPerPage;
  const currentTracks = tracks.slice(startIndex, startIndex + tracksPerPage);

  return (
    <Container>
      <Header />
      <Box mt={4}>
        {playlistImage && (
          <Box
            component="img"
            src={playlistImage}
            alt={playlistTitle}
            sx={{
              width: "100%",
              height: 300,
              objectFit: "cover",
              borderRadius: 2,
              mb: 4,
            }}
          />
        )}
        <Typography variant="h5" gutterBottom>
          Tracks in {playlistTitle}
        </Typography>
        <Box display="flex" flexDirection="column" gap={2}>
          {currentTracks.map((track) => (
            <Paper key={track.id} elevation={2} sx={{ padding: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box display="flex" alignItems="center" gap={2}>
                  <Box
                    component="img"
                    src={track.imageUrl}
                    alt={track.name}
                    sx={{
                      width: 50,
                      height: 50,
                      objectFit: "cover",
                      borderRadius: "50%",
                    }}
                  />
                  <Box>
                    <Typography variant="h6">{track.name}</Typography>
                    <Typography variant="subtitle1">{track.artist}</Typography>
                    <Box display="flex" gap={1}>
                      {track.karaokeUrl ? (
                        <IconButton component="a" href={track.karaokeUrl} target="_blank" rel="noopener noreferrer" sx={{ color: 'green' }}>
                          <KaraokeIcon />
                        </IconButton>
                      ) : (
                        <IconButton sx={{ color: 'red' }}>
                          <KaraokeIcon />
                        </IconButton>
                      )}
                      {track.lyricsUrl ? (
                        <IconButton component="a" href={track.lyricsUrl} target="_blank" rel="noopener noreferrer" sx={{ color: 'green' }}>
                          <LyricsIcon />
                        </IconButton>
                      ) : (
                        <IconButton sx={{ color: 'red' }}>
                          <LyricsIcon />
                        </IconButton>
                      )}
                    </Box>
                  </Box>
                </Box>
                <Box>
                  <Button variant="outlined" onClick={() => handleTrackEdit(track)}>
                    Edit
                  </Button>
                  <Button variant="outlined" color="secondary" onClick={() => handleDeleteClick(track)}>
                    Delete
                  </Button>
                </Box>
              </Box>
            </Paper>
          ))}
        </Box>
        <Stack spacing={2} alignItems="center" sx={{ my: 4 }}>
          <Pagination
            count={Math.ceil(tracks.length / tracksPerPage)}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
          />
        </Stack>
      </Box>

      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)}>
        <DialogTitle>Edit Track</DialogTitle>
        <DialogContent>
          <TextField
            label="Track Name"
            value={trackName}
            onChange={(e) => setTrackName(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Artist"
            value={trackArtist}
            onChange={(e) => setTrackArtist(e.target.value)}
            fullWidth
            margin="normal"
          />

          <TextField
            label="Karaoke URL"
            value={karaokeUrl}
            onChange={(e) => setKaraokeUrl(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Lyrics URL"
            value={lyricsUrl}
            onChange={(e) => setLyricsUrl(e.target.value)}
            fullWidth
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>Cancel</Button>
          <Button onClick={handleEditSubmit} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>


      <Dialog open={openDeleteDialog} onClose={handleDeleteCancel}>
        <DialogTitle>Delete Track</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the track "{selectedTrack?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button onClick={handleTrackDelete} color="secondary">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TrackListPage;