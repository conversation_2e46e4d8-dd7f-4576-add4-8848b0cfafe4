import React, { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Box,
  CircularProgress,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import RequestHandler from "./RequestHandler";
import { useSongCart } from "../../context/SongCartContext";
import { getDeviceId } from "../../utils/cookieUtils";
import SongCard from "./SongCard";

const SearchModal = ({ open, onClose }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [songs, setSongs] = useState([]);
  const [filteredSongs, setFilteredSongs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedSong, setSelectedSong] = useState(null);
  const [openRequestModal, setOpenRequestModal] = useState(false);
  const [step, setStep] = useState(1);
  const [activeRequests, setActiveRequests] = useState({});
  const deviceId = getDeviceId();
  const { cart } = useSongCart();

  const dataLoadedRef = useRef(false);

  useEffect(() => {
    if (open && !dataLoadedRef.current) {
      fetchSongs();
      fetchActiveRequests();
      dataLoadedRef.current = true;
    }
    if (!open) {
      // reset flag so data reloads next time modal opens
      dataLoadedRef.current = false;
    }
  }, [open]);

  const fetchSongs = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/api/songs`
      );
      const data = await response.json();

      const uniqueSongs = Array.from(
        new Map(
          data.map((song) => [
            song.songs_master_id,
            { ...song, id: song.songs_master_id },
          ])
        ).values()
      );

      setSongs(uniqueSongs);
      setFilteredSongs(uniqueSongs);
    } catch (error) {
      console.error("Error fetching songs:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveRequests = async () => {
    try {
      const res = await fetch(
        `${process.env.REACT_APP_API_URL}/api/song-requests/active-requests?device_id=${deviceId}`
      );
      const data = await res.json();
      setActiveRequests(data); // { song_id: expires_at }
    } catch (error) {
      console.error("Error fetching active requests:", error);
    }
  };

  const handleRequestSuccess = () => {
    fetchActiveRequests();
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    const filtered = songs.filter((song) =>
      song.name.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredSongs(filtered);
  };

  const handleRequestOpen = (song) => {
    if (cart.length >= 3) {
      setStep(3);
    } else {
      setSelectedSong(song);
      setStep(1);
    }
    setOpenRequestModal(true);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        sx={{
          "& .MuiDialog-paper": {
            width: {
              xs: "90vw",
              sm: "80vw",
              md: "500px",
            },
            height: "100vh",
            overflowY: "auto",
            margin: {
              xs: "16px",
              sm: "24px",
            },
            borderRadius: "12px",
            border: "1px solid",
            backgroundColor: "#000000",
          },
        }}
        slotProps={{
          backdrop: {
            sx: {
              backdropFilter: "blur(8px)",
              backgroundColor: "rgba(0, 0, 0, 0.4)",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            backgroundColor: "#000000",
          }}
        >
          Search Songs
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{ color: "#FFFFFF" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            backgroundColor: "#000000",
            color: "#FFFFFF",
            padding: "15px 24px 24px 24px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <TextField
            fullWidth
            placeholder="Search song name..."
            value={searchQuery}
            onChange={handleSearchChange}
            size="small"
            InputProps={{
              sx: {
                backgroundColor: "#1a1a1a",
                color: "#fff",
                borderRadius: "8px",
              },
            }}
            sx={{ mb: 2 }}
          />

          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", my: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {filteredSongs.length === 0 ? (
                <Typography>No songs found</Typography>
              ) : (
                filteredSongs.map((song) => (
                  <Box key={song.id} sx={{ mb: 2 }}>
                    <SongCard
                      song={song}
                      onRequestOpen={handleRequestOpen}
                      activeRequests={activeRequests}
                      cart={cart}
                    />
                  </Box>
                ))
              )}
            </Box>
          )}
        </DialogContent>
      </Dialog>

      <RequestHandler
        selectedSong={selectedSong}
        setSelectedSong={setSelectedSong}
        openRequestModal={openRequestModal}
        setOpenRequestModal={setOpenRequestModal}
        onRequestSuccess={handleRequestSuccess}
        step={step}
        setStep={setStep}
      />
    </>
  );
};

export default SearchModal;
