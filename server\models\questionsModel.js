const db = require('../database/db');

const runAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
        if (err) return reject(err);
        resolve(this);
    });
});

const getAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
        if (err) return reject(err);
        resolve(row);
    });
});

const allAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
    });
});

class QuestionsModel {
    // Get question type by name
    static async getQuestionType(typeName = 'text') {
        return await getAsync(
            `SELECT question_type_id FROM question_type_lookup WHERE type_name=? AND status=1`,
            [typeName]
        );
    }

    // Get answer type by name
    static async getAnswerType(typeName) {
        return await getAsync(
            `SELECT answer_type_id FROM answer_type_lookup WHERE type_name=? AND status=1`,
            [typeName]
        );
    }

    // Get difficulty by ID
    static async getDifficulty(difficultyId) {
        return await getAsync(
            `SELECT difficulty_id FROM difficulty_level_lookup WHERE difficulty_id=? AND status=1`,
            [difficultyId]
        );
    }

    // Check if question exists
    static async getQuestionById(questionId) {
        return await getAsync(
            `SELECT question_id FROM quiz_questions WHERE question_id=?`,
            [questionId]
        );
    }

    // Create a new question
    static async createQuestion(questionData) {
        const {
            songs_master_id,
            question_text,
            question_type_id,
            answer_type_id,
            difficulty_id,
            time_limit_seconds
        } = questionData;

        const insertInfo = await runAsync(`
            INSERT INTO quiz_questions (
                songs_master_id, question_text, question_image_url, question_video_url, question_audio_url,
                question_type_id, answer_type_id, difficulty_id, time_limit_seconds, question_order, created_by_admin
            ) VALUES (?, ?, NULL, NULL, NULL, ?, ?, ?, ?, COALESCE( (SELECT IFNULL(MAX(question_order),0)+1 FROM quiz_questions WHERE songs_master_id=?), 1), NULL)
        `, [
            songs_master_id,
            question_text,
            question_type_id,
            answer_type_id,
            difficulty_id,
            time_limit_seconds,
            songs_master_id,
        ]);

        return insertInfo.lastID;
    }

    // Create question answers
    static async createAnswers(questionId, answers) {
        for (const answer of answers) {
            await runAsync(`
                INSERT INTO question_answers (question_id, answer_text, answer_image_url, is_correct, answer_order)
                VALUES (?, ?, NULL, ?, ?)
            `, [questionId, answer.answer_text, answer.is_correct ? 1 : 0, answer.answer_order]);
        }
    }

    // Get full question details with joins
    static async getFullQuestionDetails(questionId) {
        return await getAsync(`
            SELECT q.*, d.level_name, qt.type_name as q_type, at.type_name as a_type, s.name as song_name
            FROM quiz_questions q
            JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
            JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
            JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
            JOIN songs_master s ON s.songs_master_id = q.songs_master_id
            WHERE q.question_id = ?
        `, [questionId]);
    }

    // Get question answers
    static async getQuestionAnswers(questionId) {
        return await allAsync(`
            SELECT answer_id, answer_text, is_correct, answer_order
            FROM question_answers
            WHERE question_id = ?
            ORDER BY answer_order ASC
        `, [questionId]);
    }

    // List all questions with pagination
    static async listQuestions() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT q.question_id, q.question_text, q.time_limit_seconds, q.question_order, q.created_at,
                       s.songs_master_id, s.name as song_name, s.artist,
                       d.level_name, d.difficulty_id,
                       qt.type_name as question_type, qt.display_name as question_type_display,
                       at.type_name as answer_type, at.display_name as answer_type_display
                FROM quiz_questions q
                JOIN songs_master s ON s.songs_master_id = q.songs_master_id
                JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
                JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
                JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
                ORDER BY q.question_id DESC`;
            
            db.all(sql, [], (err, rows) => {
                if (err) return reject(err);
                resolve(rows);
            });
        });
    }

    // Get single question with details
    static async getQuestionWithDetails(questionId) {
        return new Promise((resolve, reject) => {
            db.get(`
                SELECT q.*,
                       at.type_name AS answer_type_name, at.display_name AS answer_type_display,
                       qt.type_name AS question_type_name, qt.display_name AS question_type_display,
                       d.level_name, d.bonus_points, d.time_multiplier,
                       s.name AS song_name, s.artist
                FROM quiz_questions q
                JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
                JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
                JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
                JOIN songs_master s ON s.songs_master_id = q.songs_master_id
                WHERE q.question_id = ?
            `, [questionId], (err, question) => {
                if (err) return reject(err);
                if (!question) return resolve(null);
                
                db.all(`
                    SELECT answer_id, answer_text, is_correct, answer_order
                    FROM question_answers
                    WHERE question_id = ?
                    ORDER BY answer_order ASC
                `, [questionId], (aErr, answers) => {
                    if (aErr) return reject(aErr);
                    resolve({ ...question, answers });
                });
            });
        });
    }

    // Update question
    static async updateQuestion(questionId, questionData) {
        const {
            songs_master_id,
            question_text,
            question_type_id,
            answer_type_id,
            difficulty_id,
            time_limit_seconds
        } = questionData;

        await runAsync(`
            UPDATE quiz_questions SET
                songs_master_id = ?, question_text = ?, question_type_id = ?, answer_type_id = ?,
                difficulty_id = ?, time_limit_seconds = ?, updated_at = CURRENT_TIMESTAMP
            WHERE question_id = ?
        `, [
            songs_master_id,
            question_text,
            question_type_id,
            answer_type_id,
            difficulty_id,
            time_limit_seconds,
            questionId
        ]);
    }

    // Delete question answers
    static async deleteQuestionAnswers(questionId) {
        return await runAsync(`DELETE FROM question_answers WHERE question_id = ?`, [questionId]);
    }

    // Delete question
    static async deleteQuestion(questionId) {
        return await runAsync(`DELETE FROM quiz_questions WHERE question_id = ?`, [questionId]);
    }

    // Transaction helpers
    static async beginTransaction() {
        return await runAsync('BEGIN IMMEDIATE');
    }

    static async commitTransaction() {
        return await runAsync('COMMIT');
    }

    static async rollbackTransaction() {
        try {
            await runAsync('ROLLBACK');
        } catch (_) {
            // Ignore rollback errors
        }
    }

    // Get question statistics
    static async getQuestionStats() {
        const totalQuestions = await getAsync('SELECT COUNT(*) as count FROM quiz_questions');

        const questionsByDifficulty = await allAsync(`
            SELECT d.level_name, COUNT(*) as count
            FROM quiz_questions q
            JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
            GROUP BY q.difficulty_id, d.level_name
            ORDER BY count DESC
        `);

        const questionsByType = await allAsync(`
            SELECT at.display_name, COUNT(*) as count
            FROM quiz_questions q
            JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
            GROUP BY q.answer_type_id, at.display_name
            ORDER BY count DESC
        `);

        const averageTimeLimit = await getAsync('SELECT AVG(time_limit_seconds) as avg_time FROM quiz_questions');

        const mostUsedSongs = await allAsync(`
            SELECT s.name, s.artist, COUNT(*) as question_count
            FROM quiz_questions q
            JOIN songs_master s ON s.songs_master_id = q.songs_master_id
            GROUP BY q.songs_master_id, s.name, s.artist
            ORDER BY question_count DESC
            LIMIT 10
        `);

        return {
            total_questions: totalQuestions.count,
            by_difficulty: questionsByDifficulty,
            by_type: questionsByType,
            average_time_limit: Math.round(averageTimeLimit.avg_time || 30),
            most_used_songs: mostUsedSongs
        };
    }

    // Duplicate question
    static async duplicateQuestion(questionId) {
        // Get the original question
        const originalQuestion = await getAsync(`
            SELECT * FROM quiz_questions WHERE question_id = ?
        `, [questionId]);

        if (!originalQuestion) {
            return null;
        }

        // Get the original answers
        const originalAnswers = await allAsync(`
            SELECT * FROM question_answers WHERE question_id = ? ORDER BY answer_order
        `, [questionId]);

        // Insert the duplicated question
        const insertInfo = await runAsync(`
            INSERT INTO quiz_questions (
                songs_master_id, question_text, question_image_url, question_video_url, question_audio_url,
                question_type_id, answer_type_id, difficulty_id, time_limit_seconds, question_order, created_by_admin
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?,
                COALESCE((SELECT IFNULL(MAX(question_order),0)+1 FROM quiz_questions WHERE songs_master_id=?), 1),
                ?
            )
        `, [
            originalQuestion.songs_master_id,
            `${originalQuestion.question_text} (Copy)`,
            originalQuestion.question_image_url,
            originalQuestion.question_video_url,
            originalQuestion.question_audio_url,
            originalQuestion.question_type_id,
            originalQuestion.answer_type_id,
            originalQuestion.difficulty_id,
            originalQuestion.time_limit_seconds,
            originalQuestion.songs_master_id,
            originalQuestion.created_by_admin
        ]);

        const newQuestionId = insertInfo.lastID;

        // Insert the duplicated answers
        for (const answer of originalAnswers) {
            await runAsync(`
                INSERT INTO question_answers (question_id, answer_text, answer_image_url, is_correct, answer_order)
                VALUES (?, ?, ?, ?, ?)
            `, [
                newQuestionId,
                answer.answer_text,
                answer.answer_image_url,
                answer.is_correct,
                answer.answer_order
            ]);
        }

        return newQuestionId;
    }

    // Bulk delete questions
    static async bulkDeleteQuestions(questionIds) {
        const placeholders = questionIds.map(() => '?').join(',');

        // First, delete all associated answers for these questions
        const deleteAnswersResult = await runAsync(
            `DELETE FROM question_answers WHERE question_id IN (${placeholders})`,
            questionIds
        );

        // Then delete the questions
        const deleteQuestionsResult = await runAsync(
            `DELETE FROM quiz_questions WHERE question_id IN (${placeholders})`,
            questionIds
        );

        return {
            deletedCount: deleteQuestionsResult.changes,
            deletedAnswersCount: deleteAnswersResult.changes
        };
    }

    // Get quiz settings
    static async getQuizSettings() {
        return await getAsync(`SELECT * FROM quiz_settings WHERE id = 1`);
    }

    // Save quiz settings
    static async saveQuizSettings(settingsData) {
        const settingsJson = JSON.stringify(settingsData);

        // Create table if it doesn't exist
        await runAsync(`
            CREATE TABLE IF NOT EXISTS quiz_settings (
                id INTEGER PRIMARY KEY,
                settings_json TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Insert or update settings
        await runAsync(`
            INSERT OR REPLACE INTO quiz_settings (id, settings_json, updated_at)
            VALUES (1, ?, CURRENT_TIMESTAMP)
        `, [settingsJson]);
    }
}

module.exports = QuestionsModel;
