import React, { useState, useEffect } from "react";
import {
  Paper,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
} from "@mui/material";
import YouTubeIcon from "@mui/icons-material/YouTube";
import YouTubeSearchAndSaveDialog from "../ImportedSongs/YouTubeSearchAndSaveDialog";
import { songService } from "./songService";

const YouTubeSection = ({ songData, selectedSongId, songUrls, setSongUrls }) => {
  const [urlTypes, setUrlTypes] = useState([]);
  const [youtubeSearchTerms, setYouTubeSearchTerms] = useState({});
  const [openYouTubeDialog, setOpenYouTubeDialog] = useState(false);

  useEffect(() => {
    const fetchUrlTypes = async () => {
      try {
        const urlTypesData = await songService.fetchUrlTypes();
        setUrlTypes(urlTypesData);
        
        // Set default checked state: <PERSON><PERSON> checked, others unchecked
        const initialTerms = {};
        urlTypesData.forEach((type) => {
          initialTerms[type.url_type_name] =
            type.url_type_name.toLowerCase() === "karaoke";
        });
        setYouTubeSearchTerms(initialTerms);
      } catch (err) {
        console.error("Failed to fetch URL types:", err);
      }
    };
    fetchUrlTypes();
  }, []);

  const fetchSongUrls = async () => {
    if (selectedSongId) {
      try {
        const updatedUrls = await songService.fetchSongUrls(selectedSongId);
        setSongUrls(updatedUrls);
      } catch (error) {
        console.error("Failed to fetch song URLs:", error);
      }
    }
  };

  return (
    <>
      <Divider sx={{ my: 2 }} />
      <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
        <Typography variant="subtitle1" sx={{ mb: 1 }}>
          YouTube Search
        </Typography>
        <FormGroup row sx={{ mb: 1 }}>
          {urlTypes.map((type) => (
            <FormControlLabel
              key={type.url_type_lookup_id}
              control={
                <Checkbox
                  checked={!!youtubeSearchTerms[type.url_type_name]}
                  onChange={(e) =>
                    setYouTubeSearchTerms((prev) => ({
                      ...prev,
                      [type.url_type_name]: e.target.checked,
                    }))
                  }
                />
              }
              label={type.url_type_name}
            />
          ))}
        </FormGroup>
        <Button
          variant="outlined"
          startIcon={<YouTubeIcon />}
          onClick={() => setOpenYouTubeDialog(true)}
          sx={{ mb: 1 }}
        >
          Search YouTube
        </Button>
        <YouTubeSearchAndSaveDialog
          open={openYouTubeDialog}
          onClose={() => setOpenYouTubeDialog(false)}
          song={songData}
          songs_master_id={selectedSongId}
          urlTypes={urlTypes}
          searchTerms={Object.keys(youtubeSearchTerms).filter(
            (k) => youtubeSearchTerms[k]
          )}
          onUrlSaved={fetchSongUrls}
          songUrls={songUrls}
        />
      </Paper>
      <Divider sx={{ my: 2 }} />
    </>
  );
};

export default YouTubeSection;