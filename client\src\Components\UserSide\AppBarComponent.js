// AppBarComponent.js
import React from 'react';
import { AppBar, Toolbar, IconButton, Box } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { Link as ScrollLink } from 'react-scroll';

const AppBarComponent = ({ handleSetActive }) => (
  <AppBar
    position="fixed"
    sx={{
      background: "rgba(41, 32, 14, 0.8)",
      backgroundImage: `linear-gradient(to left, #291f0c, #322314, #3c271d, #462c26, #4f302f, #583638, #613c40, #6a4248)`,
    }}
  >
    <Toolbar sx={{ height: "90px", justifyContent: "space-between" }}>
      <ScrollLink
        to="home"
        smooth={true}
        duration={500}
        offset={-80}
        onSetActive={() => handleSetActive("home")}
        style={{ color: "inherit", textDecoration: "none" }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <img
            src={"white_logo.png"}
            alt="IM Logo"
            style={{ height: "50px", cursor: "pointer" }}
          />
        </Box>
      </ScrollLink>
      <IconButton color="inherit">
        <MenuIcon fontSize="large" />
      </IconButton>
    </Toolbar>
  </AppBar>
);

export default AppBarComponent;