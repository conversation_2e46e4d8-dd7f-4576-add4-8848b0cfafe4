// components/ArtistMaster.jsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Typography,
    IconButton,
    Collapse,
    CircularProgress,
    Alert,
} from '@mui/material';
import { Element } from 'react-scroll';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArtistListSection from './ArtistListSection';
import { useArtists } from '../../../hooks/useArtists';

const ArtistMaster = () => {
    const navigate = useNavigate();
    const [showArtists, setShowArtists] = useState(true);
    const { data: artists = [], isLoading, error } = useArtists();

     

    const handleSelectSongByArtist = (artistID) => {
        navigate(`/artist/${artistID}`);
    };

    const renderContent = () => {
        if (isLoading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                    <CircularProgress size={40} />
                </Box>
            );
        }

        if (error) {
            return (
                <Box sx={{ py: 2 }}>
                    <Alert severity="error">
                        Failed to load artists. Please try again later.
                    </Alert>
                </Box>
            );
        }

        if (artists.length === 0) {
            return (
                <Box sx={{ py: 3, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                        No artists available at the moment.
                    </Typography>
                </Box>
            );
        }

        return (
            <ArtistListSection
                artists={artists}
                onArtistSelect={handleSelectSongByArtist}
            />
        );
    };

    return (
        <Element name="artists" id="artists">
            <Box
                sx={{
                    position: 'sticky',
                    top: 90,
                    zIndex: 1,
                    backgroundColor: 'background.default',
                    py: 2,
                    display: 'block',
                    marginInline: 'auto',
                    width: '98% !important',
                    px: 2,
                }}
            />

            <Box
                sx={{
                    px: 2,
                    mb: 3,
                    marginTop: showArtists ? '2rem' : '0rem',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <Box
                    onClick={() => setShowArtists((prev) => !prev)}
                    sx={{
                        position: 'sticky',
                        top: 110,
                        zIndex: 1,
                        backgroundColor: 'background.default',
                        py: showArtists ? 1 : 0.5,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: '24px',
                        marginBottom: showArtists ? '32px' : '8px',
                        width: '100% !important',
                        border: '1px solid #966300',
                        paddingLeft: '15px',
                        borderRadius: '5px',
                        marginInline: 'auto',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '24px',
                        }}
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            style={{ flexShrink: 0 }}
                        >
                            <path
                                fill="none"
                                stroke="#966300"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
                            />
                        </svg>
                        <Typography
                            variant="h6"
                            sx={{
                                fontSize: '20px',
                                fontWeight: 'bold',
                                color: '#FFF',
                                lineHeight: '1.2',
                            }}
                        >
                            Artists {artists.length ? `(${artists.length})` : ''}
                        </Typography>
                    </Box>
                    <IconButton sx={{ color: '#966300', marginRight: '1.5rem' }}>
                        {showArtists ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={showArtists} timeout="auto" unmountOnExit>
                    {renderContent()}
                </Collapse>
            </Box>
        </Element>
    );
};

export default ArtistMaster;