import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  List,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  Divider,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSongCart } from "../../context/SongCartContext";
import { getAction } from "../../lib/utils";
import { getDeviceId } from "../../utils/cookieUtils";
import { useMode } from "../../utils/useMode";

const DedicationModal2 = ({
  open,
  onClose,
  song,
  dedication,
  onChange,
  onMakeRequest,
  onAddMoreSongs,
  actionType,
  handleBack,
  userName,
  setUserName,
  handleAction,
  setStep,
  step,
}) => {
  const { addToCart, removeFromCart, cart, updateCartItemDedication } = useSongCart();
  const requestMode = useMode();
  const [selectedOption, setSelectedOption] = useState(
    requestMode === "jukebox" ? "singForMe" :
      requestMode === "openmic" ? "singWithYou" :
        "default"
  );

  const [isAnonymous, setIsAnonymous] = useState(false);
  const [requesterName, setRequesterName] = useState("");
  // const [showAllOptions, setShowAllOptions] = useState(true);

  useEffect(() => {
    if (open && step === 0) {
      setStep(1); // Only reset step if not already defined
    }

    // Retrieve the requester name from local storage
    const storedRequesterName = localStorage.getItem(
      `requesterName_${getDeviceId()}`
    );
    if (storedRequesterName) {
      setRequesterName(storedRequesterName);
      setUserName(storedRequesterName); // Set the userName to the stored value
    }
  }, [open, setStep, setUserName, setRequesterName, step]);

  // useEffect(() => {
  //   // Fetch settings when modal opens
  //   if (open) {
  //     fetchRequestOptionsSettings();
  //   }
  // }, [open]);

  // const fetchRequestOptionsSettings = async () => {
  //   try {
  //     const response = await fetch(
  //       `${process.env.REACT_APP_API_URL}/api/settings/public/request-options-mode`
  //     );
  //     const data = await response.json();
  //     setShowAllOptions(data.showAllOptions);

  //     // If only "singForMe" is available, ensure it's selected
  //     if (!data.showAllOptions) {
  //       setSelectedOption("singForMe");
  //     }
  //   } catch (error) {
  //     console.error("Failed to fetch request options settings:", error);
  //     // Default to showing all options if fetch fails
  //     setShowAllOptions(true);
  //   }
  // };

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
  };

  const handleAddSong = () => {
    const isDuplicate = cart.some(
      (item) => item.songs_master_id === song.songs_master_id
    );

    if (isDuplicate) {
      // Update dedication for the existing song in the cart
      updateCartItemDedication(song.songs_master_id, dedication);
      setStep(3);
      return;
    }

    const songToAdd = {
      ...song,
      actionType: selectedOption,
      dedication,
    };
    addToCart(songToAdd);
    setStep(3);
  };

  const handleMakeRequestNow = () => {
    setStep(4);
  };

  const handleMakeRequest = () => {
    const finalUserName = isAnonymous ? "Anonymous" : requesterName;
    onMakeRequest(finalUserName);

    // Store the requester name in local storage
    if (!isAnonymous) {
      localStorage.setItem(`requesterName_${getDeviceId()}`, requesterName);
    }
    onClose();
    setIsAnonymous(false);
  };

  const handleBeAnonymous = () => {
    setIsAnonymous(true);
    setRequesterName("Anonymous");
    setUserName("Anonymous");
  };

  const handleRemoveSong = (songId) => {
    removeFromCart(songId);
  };

  const renderStepOne = () => (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          marginBottom: "24px",
        }}
      >
        <Avatar
          src={song?.image}
          alt={song?.name}
          sx={{
            width: 40,
            height: 40,
            marginRight: "12px",
            border: "2px solid #f59e0b",
          }}
        />
        <Box>
          <Typography
            variant="body1"
            sx={{
              fontWeight: "600",
              fontSize: "16px",
              lineHeight: "16px",
              letterSpacing: "0.2px",
              color: "#966300",
            }}
          >
            {song?.name}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontWeight: "400",
              fontSize: "13px",
              lineHeight: "22px",
              letterSpacing: "-0.41px",
              color: "#757575",
              mt: 0.5,
            }}
          >
            by {song?.artist}
          </Typography>
        </Box>
      </Box>
      <Divider sx={{ color: "#202020", height: "1px" }} />
      <Typography
        sx={{
          fontSize: "16px",
          fontWeight: "600",
          lineHeight: "16px",
          letterSpacing: "0.20000000298023224px",
          textAlign: "left",
          color: "#F9F9F9",
          my: 2,
        }}
      >
        <span>What would you like?</span>
      </Typography>
      <RadioGroup
        value={selectedOption}
        onChange={handleOptionChange}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          gap: "16px",
        }}
      >

        {/* Option 1 - Shown in default mode and jukebox mode */}
        {(requestMode === "jukebox" || requestMode === "default") && (
          <FormControlLabel
            value="singForMe"
            control={
              <Radio
                sx={{
                  color: "#976609",
                  "&, &.Mui-checked": {
                    color: "#976609",
                  },
                }}
              />
            }
            label={
              <Typography
                variant="body1"
                sx={{
                  color: "#FFFFFF",
                  textTransform: "none",
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "20px",
                }}
              >
                Sing this song for me
              </Typography>
            }
          />
        )}

        {/* Option 2 - Shown in default mode and openmic mode */}
        {(requestMode === "openmic" || requestMode === "default") && (
          <FormControlLabel
            value="singWithYou"
            control={
              <Radio
                sx={{
                  color: "#976609",
                  "&, &.Mui-checked": {
                    color: "#976609",
                  },
                }}
              />
            }
            label={
              <Typography
                variant="body1"
                sx={{
                  color: "#FFFFFF",
                  textTransform: "none",
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "20px",
                }}
              >
                I would like to sing this song with you
              </Typography>
            }
          />
        )}

        {/* Option 3 - Shown in default mode and openmic mode */}
        {(requestMode === "openmic" || requestMode === "default") && (
          <FormControlLabel
            value="singAlone"
            control={
              <Radio
                sx={{
                  color: "#976609",
                  "&, &.Mui-checked": {
                    color: "#976609",
                  },
                }}
              />
            }
            label={
              <Typography
                variant="body1"
                sx={{
                  color: "#FFFFFF",
                  textTransform: "none",
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "20px",
                }}
              >
                I would like to sing this song solo
              </Typography>
            }
          />
        )}
      </RadioGroup>

      <Button
        onClick={() => {
          handleAction(selectedOption, song);
        }}
        variant="contained"
        sx={{
          width: "100%",
          height: "40px",
          backgroundColor: "#966300",
          color: "#ffff",
          marginTop: "24px",
          textTransform: "uppercase",
        }}
      >
        Continue
      </Button>
    </>
  );

  const renderStepTwo = () => (
    <>
      <Box sx={{ display: "flex", alignItems: "center", marginBottom: "24px" }}>
        <Avatar
          src={song.image}
          alt={song.name}
          sx={{
            width: 40,
            height: 40,
            marginRight: "12px",
            border: "2px solid #f59e0b",
          }}
        />
        <Box>
          <Typography
            variant="body1"
            sx={{ fontWeight: "600", fontSize: "16px", color: "#966300" }}
          >
            {song.name}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontWeight: "400",
              fontSize: "13px",
              color: "#757575",
              mt: 0.5,
            }}
          >
            by {song.artist}
          </Typography>
        </Box>
      </Box>
      <Divider sx={{ color: "#202020", height: "1px" }} />
      <Typography
        sx={{ fontSize: "16px", fontWeight: "600", color: "#F9F9F9", my: 2 }}
      >
        <span>Enter name or message for</span>
        <br />
        <span>dedication.</span>
      </Typography>
      <TextField
        autoFocus
        margin="dense"
        id="dedication"
        placeholder="Type your dedication message here..."
        type="text"
        fullWidth
        multiline
        rows={3}
        variant="outlined"
        value={dedication}
        onChange={onChange}
        sx={{
          mb: 4,
          "& label.Mui-focused": { color: "white" },
          "& .MuiOutlinedInput-root": {
            "&.Mui-focused fieldset": { borderColor: "#976609" },
          },
        }}
      />
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="standard"
          onClick={() => handleBack(2, setStep)}
          sx={{ mt: 2, width: "100%" }}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleAddSong}
          sx={{
            mt: 2,
            width: "100%",
            backgroundColor: "#976609",
            color: "#ffff",
          }}
        >
          Next
        </Button>
      </Box>
    </>
  );

  const renderStepThree = () => (
    <>
      {cart.length === 0 ? (
        <Typography
          variant="h6"
          onClick={onAddMoreSongs}
          sx={{
            fontWeight: "bold",
            fontSize: "24px",
            textAlign: "center",
            color: "#757575",
            my: 2,
          }}
        >
          Please add a song / Queue a song
        </Typography>
      ) : (
        <List sx={{ width: "100%", overflow: "auto", mb: 2 }}>
          {cart.map((item, index) => (
            <Box
              key={index}
              sx={{ display: "flex", flexDirection: "column", my: 1, p: 1 }}
            >
              <Box
                sx={{
                  display: "flex",
                  marginBottom: "24px",
                  justifyContent: "space-between",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Avatar
                    src={item.image}
                    alt={item.name}
                    sx={{
                      width: 40,
                      height: 40,
                      marginRight: "12px",
                      border: "2px solid #f59e0b",
                    }}
                  />
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: "600",
                        fontSize: "16px",
                        color: "#966300",
                      }}
                    >
                      {item.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: "400",
                        fontSize: "13px",
                        color: "#757575",
                        mt: 0.5,
                      }}
                    >
                      by {item.artist}
                    </Typography>
                  </Box>
                </Box>
                <IconButton
                  edge="end"
                  aria-label="delete"
                  onClick={() => handleRemoveSong(item.songs_master_id)}
                  sx={{ marginRight: "7px !important" }}
                >
                  <img
                    src="/assets/cross-red.svg"
                    alt="Cross Icon"
                    style={{ height: "24px" }}
                  />
                </IconButton>
              </Box>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: "600", fontSize: "14px" }}
                >
                  <span style={{ color: "#966300", marginRight: "5px" }}>
                    Action:
                  </span>
                  {getAction(item.actionType)}
                </Typography>
                {item.dedication_msg && (
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: "600", fontSize: "14px" }}
                  >
                    <span style={{ color: "#966300", marginRight: "5px" }}>
                      Dedication Message:
                    </span>
                    {item.dedication_msg}
                  </Typography>
                )}
              </Box>
            </Box>
          ))}
        </List>
      )}
      <Box sx={{ my: 1 }}>
        {cart.length < 3 ? (
          <>
            <Typography
              variant="body2"
              sx={{
                fontWeight: "600",
                fontSize: "16px",
                color: "#757575",
                textAlign: "center",
              }}
            >
              You can queue up to 3 songs,
              <span
                onClick={onAddMoreSongs}
                style={{
                  fontWeight: "600",
                  fontSize: "18px",
                  color: "#966300",
                  textAlign: "center",
                  textDecoration: "underline",
                  cursor: "pointer",
                  marginLeft: "5px",
                }}
              >
                Queue Another Song
              </span>
            </Typography>
          </>
        ) : (
          <Typography
            variant="body2"
            sx={{
              fontWeight: "600",
              fontSize: "18px",
              color: "#757575",
              textAlign: "center",
            }}
          >
            Please remove a song if you want to add another, as you can queue up
            to only three songs.
          </Typography>
        )}
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="standard"
          onClick={() => handleBack(3, setStep)}
          sx={{ mt: 2, width: "100%" }}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleMakeRequestNow}
          disabled={cart.length > 3}
          sx={{
            mt: 2,
            width: "100%",
            backgroundColor: "#976609",
            color: "#ffff",
            "&.Mui-disabled": {
              backgroundColor: "#aaaaaa",
              color: "#fff",
            },
          }}
        >
          Next
        </Button>
      </Box>
    </>
  );

  const renderStepFour = () => (
    <>
      <Box>
        <TextField
          autoFocus
          margin="dense"
          id="user-name"
          placeholder="Type name"
          type="text"
          fullWidth
          variant="outlined"
          value={userName}
          onChange={(e) => {
            const value = e.target.value;
            setRequesterName(value);
            setUserName(value);
            if (value !== "Anonymous") {
              setIsAnonymous(false);
            }
          }}
          sx={{
            "& label.Mui-focused": { color: "white" },
            "& .MuiOutlinedInput-root": {
              "&.Mui-focused fieldset": { borderColor: "#976609" },
            },
          }}
        />
        <Typography sx={{ fontSize: "12px", color: "#545454", mt: 2, mb: 2 }}>
          If you want to be Anonymous, click on{" "}
          <Box
            component="span"
            onClick={handleBeAnonymous}
            sx={{
              fontWeight: "bold",
              textDecoration: "underline",
              color: isAnonymous ? "#aaa" : "#976609",
              cursor: isAnonymous ? "not-allowed" : "pointer",
              pointerEvents: isAnonymous ? "none" : "auto",
            }}
          >
            Be Anonymous
          </Box>
        </Typography>
      </Box>
      <List sx={{ width: "100%", overflow: "auto", mb: 2 }}>
        {cart.map((item, index) => (
          <Box
            key={index}
            sx={{ display: "flex", flexDirection: "column", my: 2, p: 1 }}
          >
            <Box sx={{ display: "flex", marginBottom: "24px" }}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Avatar
                  src={item.image}
                  alt={item.name}
                  sx={{
                    width: 40,
                    height: 40,
                    marginRight: "12px",
                    border: "2px solid #f59e0b",
                  }}
                />
                <Box>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: "600",
                      fontSize: "16px",
                      color: "#966300",
                    }}
                  >
                    {item.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: "400",
                      fontSize: "13px",
                      color: "#757575",
                      mt: 0.5,
                    }}
                  >
                    by {item.artist}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Typography
                variant="body2"
                sx={{ fontWeight: "600", fontSize: "14px" }}
              >
                <span style={{ color: "#966300", marginRight: "5px" }}>
                  Action:
                </span>
                {getAction(item.actionType)}
              </Typography>
              {item.dedication_msg && (
                <Typography
                  variant="body2"
                  sx={{ fontWeight: "600", fontSize: "14px" }}
                >
                  <span style={{ color: "#966300", marginRight: "5px" }}>
                    Dedication Message:
                  </span>
                  {item.dedication_msg}
                </Typography>
              )}
            </Box>
          </Box>
        ))}
      </List>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="standard"
          onClick={() => handleBack(4, setStep)}
          sx={{ mt: 2 }}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleMakeRequest}
          sx={{ mt: 2, backgroundColor: "#976609", color: "#ffff" }}
        >
          Send Request
        </Button>
      </Box>
    </>
  );

  return (
    <>
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        aria-labelledby="song-request-dialog-title"
        maxWidth="sm"
        sx={{
          "& .MuiDialog-paper": {
            width: {
              xs: "90vw",
              sm: "80vw",
              md: "500px",
            },
            margin: {
              xs: "16px",
              sm: "24px",
            },
            borderRadius: "12px",
            border: "1px solid",
            backgroundColor: "#000000",
          },
        }}
        slotProps={{
          backdrop: {
            sx: {
              backdropFilter: "blur(8px)",
              backgroundColor: "rgba(0, 0, 0, 0.4)",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            backgroundColor: "#000000",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Nunito",
              fontSize: "32px",
              fontWeight: "700",
              lineHeight: "40px",
              letterSpacing: "0.2px",
              marginBottom: "8px",
              textAlign: "left",
            }}
          >
            {step === 1 && "Queue your Song Requests"}
            {step === 2 &&
              (actionType === "singForMe" ? "Dedication message" : "Add Song")}
            {step === 3 && "Queuing Cart"}
            {step === 4 && "Confirm"}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{ color: "#FFFFFF" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            backgroundColor: "#000000",
            color: "#FFFFFF",
            padding: "24px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {step === 1 && renderStepOne()}
          {step === 2 && renderStepTwo()}
          {step === 3 && renderStepThree()}
          {step === 4 && renderStepFour()}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DedicationModal2;
