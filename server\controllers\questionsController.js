const db = require('../database/db');
const { z } = require('zod');

const runAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
        if (err) return reject(err);
        resolve(this);
    });
});

const getAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
        if (err) return reject(err);
        resolve(row);
    });
});

const allAsync = (sql, params = []) => new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
        if (err) return reject(err);
        resolve(rows);
    });
});

// Only text question + multiple choice answers (text) for now
const AnswerSchema = z.object({
    answer_text: z.string().min(1),
    is_correct: z.boolean(),
    answer_order: z.number().int().positive()
});

const CreateQuestionSchema = z.object({
    songs_master_id: z.number().int().positive(),
    question_text: z.string().min(1),
    difficulty_id: z.number().int().positive(),
    time_limit_seconds: z.number().int().positive().max(600).default(30),
    answer_type: z.enum(["multiple_choice", "single_choice", "true_false"]).default("multiple_choice"),
    answers: z.array(AnswerSchema).min(2)
});

exports.createQuestion = async (req, res) => {
    const parse = CreateQuestionSchema.safeParse(req.body);
    if (!parse.success) {
        return res.status(400).json({ error: 'Invalid payload', details: parse.error.flatten() });
    }
    const payload = parse.data;

    const correctCount = payload.answers.filter(a => a.is_correct).length;
    if (payload.answer_type === 'true_false') {
        if (payload.answers.length !== 2) {
            return res.status(400).json({ error: 'True/False requires exactly 2 answers' });
        }
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for True/False' });
        }
    } else if (payload.answer_type === 'single_choice') {
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for Single Choice' });
        }
    } else if (payload.answer_type === 'multiple_choice') {
        if (correctCount < 1) {
            return res.status(400).json({ error: 'At least one correct answer required for Multiple Choice' });
        }
    }

    try {
        console.log('Creating question with payload:', JSON.stringify(payload, null, 2));

        const qType = await getAsync(`SELECT question_type_id FROM question_type_lookup WHERE type_name='text' AND status=1`);
        const aType = await getAsync(`SELECT answer_type_id FROM answer_type_lookup WHERE type_name=? AND status=1`, [payload.answer_type]);

        console.log('Found question type:', qType);
        console.log('Found answer type:', aType);

        if (!qType || !aType) {
            console.log('Missing types - qType:', qType, 'aType:', aType);
            return res.status(500).json({ error: 'Question/Answer types not seeded' });
        }

        // Check if difficulty exists
        const difficulty = await getAsync(`SELECT difficulty_id FROM difficulty_level_lookup WHERE difficulty_id=? AND status=1`, [payload.difficulty_id]);
        console.log('Found difficulty:', difficulty);

        if (!difficulty) {
            return res.status(400).json({ error: `Invalid difficulty_id: ${payload.difficulty_id}` });
        }

        await runAsync('BEGIN IMMEDIATE');

        const insertInfo = await runAsync(`
      INSERT INTO quiz_questions (
        songs_master_id, question_text, question_image_url, question_video_url, question_audio_url,
        question_type_id, answer_type_id, difficulty_id, time_limit_seconds, question_order, created_by_admin
      ) VALUES (?, ?, NULL, NULL, NULL, ?, ?, ?, ?, COALESCE( (SELECT IFNULL(MAX(question_order),0)+1 FROM quiz_questions WHERE songs_master_id=?), 1), NULL)
    `, [
            payload.songs_master_id,
            payload.question_text,
            qType.question_type_id,
            aType.answer_type_id,
            payload.difficulty_id,
            payload.time_limit_seconds,
            payload.songs_master_id,
        ]);

        const questionId = insertInfo.lastID;
        for (const a of payload.answers) {
            await runAsync(`
        INSERT INTO question_answers (question_id, answer_text, answer_image_url, is_correct, answer_order)
        VALUES (?, ?, NULL, ?, ?)
      `, [questionId, a.answer_text, a.is_correct ? 1 : 0, a.answer_order]);
        }

        await runAsync('COMMIT');

        const full = await getAsync(`
      SELECT q.*, d.level_name, qt.type_name as q_type, at.type_name as a_type, s.name as song_name
      FROM quiz_questions q
      JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
      JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
      JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
      JOIN songs_master s ON s.songs_master_id = q.songs_master_id
      WHERE q.question_id = ?
    `, [questionId]);
        const answers = await allAsync(`
      SELECT answer_id, answer_text, is_correct, answer_order
      FROM question_answers
      WHERE question_id = ?
      ORDER BY answer_order ASC
    `, [questionId]);
        res.status(201).json({ ...full, answers });
    } catch (err) {
        try { await runAsync('ROLLBACK'); } catch (_) { }
        res.status(500).json({ error: 'Failed to create question', details: err.message });
    }
};

exports.listQuestions = (req, res) => {
    const sql = `
    SELECT q.question_id, q.question_text, q.time_limit_seconds, q.question_order, q.created_at,
           s.songs_master_id, s.name as song_name, s.artist,
           d.level_name, d.difficulty_id,
           qt.type_name as question_type, qt.display_name as question_type_display,
           at.type_name as answer_type, at.display_name as answer_type_display
    FROM quiz_questions q
    JOIN songs_master s ON s.songs_master_id = q.songs_master_id
    JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
    JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
    JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
    ORDER BY q.question_id DESC`;
    db.all(sql, [], (err, rows) => {
        if (err) return res.status(500).json({ error: 'DB error', details: err.message });
        res.json(rows);
    });
};

exports.getQuestion = (req, res) => {
    const id = Number(req.params.id);
    db.get(`
      SELECT q.*,
             at.type_name AS answer_type_name, at.display_name AS answer_type_display,
             qt.type_name AS question_type_name, qt.display_name AS question_type_display,
             d.level_name, d.bonus_points, d.time_multiplier,
             s.name AS song_name, s.artist
      FROM quiz_questions q
      JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
      JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
      JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
      JOIN songs_master s ON s.songs_master_id = q.songs_master_id
      WHERE q.question_id = ?
    `, [id], (err, q) => {
        if (err) return res.status(500).json({ error: 'DB error', details: err.message });
        if (!q) return res.status(404).json({ error: 'Not found' });
        db.all(`
        SELECT answer_id, answer_text, is_correct, answer_order
        FROM question_answers
        WHERE question_id = ?
        ORDER BY answer_order ASC
      `, [id], (aErr, answers) => {
            if (aErr) return res.status(500).json({ error: 'DB error', details: aErr.message });
            res.json({ ...q, answers });
        });
    });
};

exports.updateQuestion = async (req, res) => {
    const parse = CreateQuestionSchema.safeParse(req.body);
    if (!parse.success) {
        return res.status(400).json({ error: 'Invalid payload', details: parse.error.flatten() });
    }
    const payload = parse.data;
    const questionId = Number(req.params.id);

    const correctCount = payload.answers.filter(a => a.is_correct).length;
    if (payload.answer_type === 'true_false') {
        if (payload.answers.length !== 2) {
            return res.status(400).json({ error: 'True/False requires exactly 2 answers' });
        }
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for True/False' });
        }
    } else if (payload.answer_type === 'single_choice') {
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for Single Choice' });
        }
    } else if (payload.answer_type === 'multiple_choice') {
        if (correctCount < 1) {
            return res.status(400).json({ error: 'At least one correct answer required for Multiple Choice' });
        }
    }

    try {
        console.log('Updating question with payload:', JSON.stringify(payload, null, 2));

        const qType = await getAsync(`SELECT question_type_id FROM question_type_lookup WHERE type_name='text' AND status=1`);
        const aType = await getAsync(`SELECT answer_type_id FROM answer_type_lookup WHERE type_name=? AND status=1`, [payload.answer_type]);

        if (!qType || !aType) {
            return res.status(500).json({ error: 'Question/Answer types not seeded' });
        }

        // Check if difficulty exists
        const difficulty = await getAsync(`SELECT difficulty_id FROM difficulty_level_lookup WHERE difficulty_id=? AND status=1`, [payload.difficulty_id]);
        if (!difficulty) {
            return res.status(400).json({ error: `Invalid difficulty_id: ${payload.difficulty_id}` });
        }

        // Check if question exists
        const existingQuestion = await getAsync(`SELECT question_id FROM quiz_questions WHERE question_id=?`, [questionId]);
        if (!existingQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        await runAsync('BEGIN IMMEDIATE');

        // Update the question
        await runAsync(`
            UPDATE quiz_questions SET
                songs_master_id = ?, question_text = ?, question_type_id = ?, answer_type_id = ?,
                difficulty_id = ?, time_limit_seconds = ?, updated_at = CURRENT_TIMESTAMP
            WHERE question_id = ?
        `, [
            payload.songs_master_id,
            payload.question_text,
            qType.question_type_id,
            aType.answer_type_id,
            payload.difficulty_id,
            payload.time_limit_seconds,
            questionId
        ]);

        // Delete existing answers
        await runAsync(`DELETE FROM question_answers WHERE question_id = ?`, [questionId]);

        // Insert new answers
        for (const a of payload.answers) {
            await runAsync(`
                INSERT INTO question_answers (question_id, answer_text, answer_image_url, is_correct, answer_order)
                VALUES (?, ?, NULL, ?, ?)
            `, [questionId, a.answer_text, a.is_correct ? 1 : 0, a.answer_order]);
        }

        await runAsync('COMMIT');

        // Return updated question with full details
        const full = await getAsync(`
            SELECT q.*, d.level_name, qt.type_name as q_type, at.type_name as a_type, s.name as song_name
            FROM quiz_questions q
            JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
            JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
            JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
            JOIN songs_master s ON s.songs_master_id = q.songs_master_id
            WHERE q.question_id = ?
        `, [questionId]);

        const answers = await allAsync(`
            SELECT answer_id, answer_text, is_correct, answer_order
            FROM question_answers
            WHERE question_id = ?
            ORDER BY answer_order ASC
        `, [questionId]);

        res.json({ ...full, answers });
    } catch (err) {
        try { await runAsync('ROLLBACK'); } catch (_) { }
        res.status(500).json({ error: 'Failed to update question', details: err.message });
    }
};

exports.deleteQuestion = async (req, res) => {
    const id = Number(req.params.id);

    try {
        await runAsync('BEGIN TRANSACTION');

        // First, delete all associated answers
        const deleteAnswersResult = await runAsync(
            `DELETE FROM question_answers WHERE question_id = ?`,
            [id]
        );

        // Then delete the question
        const deleteQuestionResult = await runAsync(
            `DELETE FROM quiz_questions WHERE question_id = ?`,
            [id]
        );

        if (deleteQuestionResult.changes === 0) {
            await runAsync('ROLLBACK');
            return res.status(404).json({ error: 'Question not found' });
        }

        await runAsync('COMMIT');

        res.json({
            success: true,
            message: `Question deleted successfully along with ${deleteAnswersResult.changes} associated answers`
        });

    } catch (err) {
        try { await runAsync('ROLLBACK'); } catch (_) { }
        res.status(500).json({ error: 'Failed to delete question', details: err.message });
    }
};

// Get quiz question statistics
exports.getQuestionStats = async (req, res) => {
    try {
        const totalQuestions = await getAsync('SELECT COUNT(*) as count FROM quiz_questions');

        const questionsByDifficulty = await allAsync(`
            SELECT d.level_name, COUNT(*) as count
            FROM quiz_questions q
            JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
            GROUP BY q.difficulty_id, d.level_name
            ORDER BY count DESC
        `);

        const questionsByType = await allAsync(`
            SELECT at.display_name, COUNT(*) as count
            FROM quiz_questions q
            JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
            GROUP BY q.answer_type_id, at.display_name
            ORDER BY count DESC
        `);

        const averageTimeLimit = await getAsync('SELECT AVG(time_limit_seconds) as avg_time FROM quiz_questions');

        const mostUsedSongs = await allAsync(`
            SELECT s.name, s.artist, COUNT(*) as question_count
            FROM quiz_questions q
            JOIN songs_master s ON s.songs_master_id = q.songs_master_id
            GROUP BY q.songs_master_id, s.name, s.artist
            ORDER BY question_count DESC
            LIMIT 10
        `);

        res.json({
            total_questions: totalQuestions.count,
            by_difficulty: questionsByDifficulty,
            by_type: questionsByType,
            average_time_limit: Math.round(averageTimeLimit.avg_time || 30),
            most_used_songs: mostUsedSongs
        });
    } catch (err) {
        res.status(500).json({ error: 'Failed to get statistics', details: err.message });
    }
};

// Duplicate a quiz question
exports.duplicateQuestion = async (req, res) => {
    const id = Number(req.params.id);

    try {
        await runAsync('BEGIN TRANSACTION');

        // Get the original question
        const originalQuestion = await getAsync(`
            SELECT * FROM quiz_questions WHERE question_id = ?
        `, [id]);

        if (!originalQuestion) {
            await runAsync('ROLLBACK');
            return res.status(404).json({ error: 'Question not found' });
        }

        // Get the original answers
        const originalAnswers = await allAsync(`
            SELECT * FROM question_answers WHERE question_id = ? ORDER BY answer_order
        `, [id]);

        // Insert the duplicated question
        const insertInfo = await runAsync(`
            INSERT INTO quiz_questions (
                songs_master_id, question_text, question_image_url, question_video_url, question_audio_url,
                question_type_id, answer_type_id, difficulty_id, time_limit_seconds, question_order, created_by_admin
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?,
                COALESCE((SELECT IFNULL(MAX(question_order),0)+1 FROM quiz_questions WHERE songs_master_id=?), 1),
                ?
            )
        `, [
            originalQuestion.songs_master_id,
            `${originalQuestion.question_text} (Copy)`,
            originalQuestion.question_image_url,
            originalQuestion.question_video_url,
            originalQuestion.question_audio_url,
            originalQuestion.question_type_id,
            originalQuestion.answer_type_id,
            originalQuestion.difficulty_id,
            originalQuestion.time_limit_seconds,
            originalQuestion.songs_master_id,
            originalQuestion.created_by_admin  
        ]);

        const newQuestionId = insertInfo.lastID;

        // Insert the duplicated answers
        for (const answer of originalAnswers) {
            await runAsync(`
                INSERT INTO question_answers (question_id, answer_text, answer_image_url, is_correct, answer_order)
                VALUES (?, ?, ?, ?, ?)
            `, [
                newQuestionId,
                answer.answer_text,
                answer.answer_image_url,
                answer.is_correct,
                answer.answer_order
            ]);
        }

        await runAsync('COMMIT');

        // Get the complete duplicated question
        const duplicatedQuestion = await getAsync(`
            SELECT q.*, d.level_name, qt.type_name as q_type, at.type_name as a_type, s.name as song_name
            FROM quiz_questions q
            JOIN difficulty_level_lookup d ON d.difficulty_id = q.difficulty_id
            JOIN question_type_lookup qt ON qt.question_type_id = q.question_type_id
            JOIN answer_type_lookup at ON at.answer_type_id = q.answer_type_id
            JOIN songs_master s ON s.songs_master_id = q.songs_master_id
            WHERE q.question_id = ?
        `, [newQuestionId]);

        const answers = await allAsync(`
            SELECT answer_id, answer_text, is_correct, answer_order
            FROM question_answers
            WHERE question_id = ?
            ORDER BY answer_order ASC
        `, [newQuestionId]);

        res.status(201).json({ ...duplicatedQuestion, answers });
    } catch (err) {
        try { await runAsync('ROLLBACK'); } catch (_) { }
        res.status(500).json({ error: 'Failed to duplicate question', details: err.message });
    }
};

// Bulk delete questions
exports.bulkDeleteQuestions = async (req, res) => {
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({ error: 'Invalid question IDs' });
    }

    try {
        await runAsync('BEGIN TRANSACTION');

        const placeholders = questionIds.map(() => '?').join(',');

        // First, delete all associated answers for these questions
        const deleteAnswersResult = await runAsync(
            `DELETE FROM question_answers WHERE question_id IN (${placeholders})`,
            questionIds
        );

        // Then delete the questions
        const deleteQuestionsResult = await runAsync(
            `DELETE FROM quiz_questions WHERE question_id IN (${placeholders})`,
            questionIds
        );

        await runAsync('COMMIT');

        res.json({
            message: `${deleteQuestionsResult.changes} questions deleted successfully along with ${deleteAnswersResult.changes} associated answers`,
            deletedCount: deleteQuestionsResult.changes,
            deletedAnswersCount: deleteAnswersResult.changes
        });
    } catch (err) {
        try { await runAsync('ROLLBACK'); } catch (_) { }
        res.status(500).json({ error: 'Failed to delete questions', details: err.message });
    }
};

// Get quiz settings
exports.getQuizSettings = async (req, res) => {
    try {
        const settings = await getAsync(`
            SELECT * FROM quiz_settings WHERE id = 1
        `);

        if (!settings) {
            // Return default settings if none exist
            const defaultSettings = {
                defaultTimeLimit: 30,
                defaultPoints: 10,
                defaultDifficulty: '',
                minQuestionLength: 10,
                maxQuestionLength: 500,
                minAnswerLength: 1,
                maxAnswerLength: 200,
                minAnswersRequired: 2,
                maxAnswersAllowed: 6,
                enableBonusPoints: true,
                enableTimeMultiplier: true,
                penaltyForWrongAnswer: 0,
                exportFormat: 'csv',
                includeAnswersInExport: true,
                includeDifficultyInExport: true,
                requireApprovalForNewQuestions: false,
                allowDuplicateQuestions: false,
                enableQuestionVersioning: false,
                showQuestionPreview: true,
                enableAutoSave: true,
                autoSaveInterval: 30
            };
            return res.json(defaultSettings);
        }

        res.json(JSON.parse(settings.settings_json));
    } catch (err) {
        res.status(500).json({ error: 'Failed to get quiz settings', details: err.message });
    }
};

// Save quiz settings
exports.saveQuizSettings = async (req, res) => {
    try {
        const settingsJson = JSON.stringify(req.body);

        // Create table if it doesn't exist
        await runAsync(`
            CREATE TABLE IF NOT EXISTS quiz_settings (
                id INTEGER PRIMARY KEY,
                settings_json TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Insert or update settings
        await runAsync(`
            INSERT OR REPLACE INTO quiz_settings (id, settings_json, updated_at)
            VALUES (1, ?, CURRENT_TIMESTAMP)
        `, [settingsJson]);

        res.json({ message: 'Quiz settings saved successfully' });
    } catch (err) {
        res.status(500).json({ error: 'Failed to save quiz settings', details: err.message });
    }
};
