const QuestionsModel = require('../models/questionsModel');

exports.createQuestion = async (req, res) => {
    const payload = req.body;

    // Basic validation
    if (!payload.songs_master_id || !payload.question_text || !payload.difficulty_id || !payload.answers) {
        return res.status(400).json({ error: 'Missing required fields' });
    }

    if (!Array.isArray(payload.answers) || payload.answers.length < 2) {
        return res.status(400).json({ error: 'At least 2 answers required' });
    }

    // Set defaults
    payload.time_limit_seconds = payload.time_limit_seconds || 30;
    payload.answer_type = payload.answer_type || 'multiple_choice';

    const correctCount = payload.answers.filter(a => a.is_correct).length;
    if (payload.answer_type === 'true_false') {
        if (payload.answers.length !== 2) {
            return res.status(400).json({ error: 'True/False requires exactly 2 answers' });
        }
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for True/False' });
        }
    } else if (payload.answer_type === 'single_choice') {
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for Single Choice' });
        }
    } else if (payload.answer_type === 'multiple_choice') {
        if (correctCount < 1) {
            return res.status(400).json({ error: 'At least one correct answer required for Multiple Choice' });
        }
    }

    try {
        console.log('Creating question with payload:', JSON.stringify(payload, null, 2));

        const qType = await QuestionsModel.getQuestionType('text');
        const aType = await QuestionsModel.getAnswerType(payload.answer_type);

        console.log('Found question type:', qType);
        console.log('Found answer type:', aType);

        if (!qType || !aType) {
            console.log('Missing types - qType:', qType, 'aType:', aType);
            return res.status(500).json({ error: 'Question/Answer types not seeded' });
        }

        // Check if difficulty exists
        const difficulty = await QuestionsModel.getDifficulty(payload.difficulty_id);
        console.log('Found difficulty:', difficulty);

        if (!difficulty) {
            return res.status(400).json({ error: `Invalid difficulty_id: ${payload.difficulty_id}` });
        }

        await QuestionsModel.beginTransaction();

        const questionId = await QuestionsModel.createQuestion({
            songs_master_id: payload.songs_master_id,
            question_text: payload.question_text,
            question_type_id: qType.question_type_id,
            answer_type_id: aType.answer_type_id,
            difficulty_id: payload.difficulty_id,
            time_limit_seconds: payload.time_limit_seconds
        });

        await QuestionsModel.createAnswers(questionId, payload.answers);
        await QuestionsModel.commitTransaction();

        const full = await QuestionsModel.getFullQuestionDetails(questionId);
        const answers = await QuestionsModel.getQuestionAnswers(questionId);

        res.status(201).json({ ...full, answers });
    } catch (err) {
        await QuestionsModel.rollbackTransaction();
        res.status(500).json({ error: 'Failed to create question', details: err.message });
    }
};

exports.listQuestions = async (req, res) => {
    try {
        const questions = await QuestionsModel.listQuestions();
        res.json(questions);
    } catch (err) {
        res.status(500).json({ error: 'DB error', details: err.message });
    }
};

exports.getQuestion = async (req, res) => {
    const id = Number(req.params.id);
    try {
        const question = await QuestionsModel.getQuestionWithDetails(id);
        if (!question) {
            return res.status(404).json({ error: 'Not found' });
        }
        res.json(question);
    } catch (err) {
        res.status(500).json({ error: 'DB error', details: err.message });
    }
};

exports.updateQuestion = async (req, res) => {
    const payload = req.body;
    const questionId = Number(req.params.id);

    // Basic validation
    if (!payload.songs_master_id || !payload.question_text || !payload.difficulty_id || !payload.answers) {
        return res.status(400).json({ error: 'Missing required fields' });
    }

    if (!Array.isArray(payload.answers) || payload.answers.length < 2) {
        return res.status(400).json({ error: 'At least 2 answers required' });
    }

    // Set defaults
    payload.time_limit_seconds = payload.time_limit_seconds || 30;
    payload.answer_type = payload.answer_type || 'multiple_choice';

    const correctCount = payload.answers.filter(a => a.is_correct).length;
    if (payload.answer_type === 'true_false') {
        if (payload.answers.length !== 2) {
            return res.status(400).json({ error: 'True/False requires exactly 2 answers' });
        }
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for True/False' });
        }
    } else if (payload.answer_type === 'single_choice') {
        if (correctCount !== 1) {
            return res.status(400).json({ error: 'Exactly one correct answer required for Single Choice' });
        }
    } else if (payload.answer_type === 'multiple_choice') {
        if (correctCount < 1) {
            return res.status(400).json({ error: 'At least one correct answer required for Multiple Choice' });
        }
    }

    try {
        console.log('Updating question with payload:', JSON.stringify(payload, null, 2));

        const qType = await QuestionsModel.getQuestionType('text');
        const aType = await QuestionsModel.getAnswerType(payload.answer_type);

        if (!qType || !aType) {
            return res.status(500).json({ error: 'Question/Answer types not seeded' });
        }

        // Check if difficulty exists
        const difficulty = await QuestionsModel.getDifficulty(payload.difficulty_id);
        if (!difficulty) {
            return res.status(400).json({ error: `Invalid difficulty_id: ${payload.difficulty_id}` });
        }

        // Check if question exists
        const existingQuestion = await QuestionsModel.getQuestionById(questionId);
        if (!existingQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        await QuestionsModel.beginTransaction();

        // Update the question
        await QuestionsModel.updateQuestion(questionId, {
            songs_master_id: payload.songs_master_id,
            question_text: payload.question_text,
            question_type_id: qType.question_type_id,
            answer_type_id: aType.answer_type_id,
            difficulty_id: payload.difficulty_id,
            time_limit_seconds: payload.time_limit_seconds
        });

        // Delete existing answers and insert new ones
        await QuestionsModel.deleteQuestionAnswers(questionId);
        await QuestionsModel.createAnswers(questionId, payload.answers);

        await QuestionsModel.commitTransaction();

        // Return updated question with full details
        const full = await QuestionsModel.getFullQuestionDetails(questionId);
        const answers = await QuestionsModel.getQuestionAnswers(questionId);

        res.json({ ...full, answers });
    } catch (err) {
        await QuestionsModel.rollbackTransaction();
        res.status(500).json({ error: 'Failed to update question', details: err.message });
    }
};

exports.deleteQuestion = async (req, res) => {
    const id = Number(req.params.id);

    try {
        await QuestionsModel.beginTransaction();

        // First, delete all associated answers
        const deleteAnswersResult = await QuestionsModel.deleteQuestionAnswers(id);

        // Then delete the question
        const deleteQuestionResult = await QuestionsModel.deleteQuestion(id);

        if (deleteQuestionResult.changes === 0) {
            await QuestionsModel.rollbackTransaction();
            return res.status(404).json({ error: 'Question not found' });
        }

        await QuestionsModel.commitTransaction();

        res.json({
            success: true,
            message: `Question deleted successfully along with ${deleteAnswersResult.changes} associated answers`
        });

    } catch (err) {
        await QuestionsModel.rollbackTransaction();
        res.status(500).json({ error: 'Failed to delete question', details: err.message });
    }
};

// Get quiz question statistics
exports.getQuestionStats = async (req, res) => {
    try {
        const stats = await QuestionsModel.getQuestionStats();
        res.json(stats);
    } catch (err) {
        res.status(500).json({ error: 'Failed to get statistics', details: err.message });
    }
};

// Duplicate a quiz question
exports.duplicateQuestion = async (req, res) => {
    const id = Number(req.params.id);

    try {
        await QuestionsModel.beginTransaction();

        const newQuestionId = await QuestionsModel.duplicateQuestion(id);

        if (!newQuestionId) {
            await QuestionsModel.rollbackTransaction();
            return res.status(404).json({ error: 'Question not found' });
        }

        await QuestionsModel.commitTransaction();

        // Get the complete duplicated question
        const duplicatedQuestion = await QuestionsModel.getFullQuestionDetails(newQuestionId);
        const answers = await QuestionsModel.getQuestionAnswers(newQuestionId);

        res.status(201).json({ ...duplicatedQuestion, answers });
    } catch (err) {
        await QuestionsModel.rollbackTransaction();
        res.status(500).json({ error: 'Failed to duplicate question', details: err.message });
    }
};

// Bulk delete questions
exports.bulkDeleteQuestions = async (req, res) => {
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({ error: 'Invalid question IDs' });
    }

    try {
        await QuestionsModel.beginTransaction();

        const result = await QuestionsModel.bulkDeleteQuestions(questionIds);

        await QuestionsModel.commitTransaction();

        res.json({
            message: `${result.deletedCount} questions deleted successfully along with ${result.deletedAnswersCount} associated answers`,
            deletedCount: result.deletedCount,
            deletedAnswersCount: result.deletedAnswersCount
        });
    } catch (err) {
        await QuestionsModel.rollbackTransaction();
        res.status(500).json({ error: 'Failed to delete questions', details: err.message });
    }
};

// Get quiz settings
exports.getQuizSettings = async (req, res) => {
    try {
        const settings = await QuestionsModel.getQuizSettings();

        if (!settings) {
            // Return default settings if none exist
            const defaultSettings = {
                defaultTimeLimit: 30,
                defaultPoints: 10,
                defaultDifficulty: '',
                minQuestionLength: 10,
                maxQuestionLength: 500,
                minAnswerLength: 1,
                maxAnswerLength: 200,
                minAnswersRequired: 2,
                maxAnswersAllowed: 6,
                enableBonusPoints: true,
                enableTimeMultiplier: true,
                penaltyForWrongAnswer: 0,
                exportFormat: 'csv',
                includeAnswersInExport: true,
                includeDifficultyInExport: true,
                requireApprovalForNewQuestions: false,
                allowDuplicateQuestions: false,
                enableQuestionVersioning: false,
                showQuestionPreview: true,
                enableAutoSave: true,
                autoSaveInterval: 30
            };
            return res.json(defaultSettings);
        }

        res.json(JSON.parse(settings.settings_json));
    } catch (err) {
        res.status(500).json({ error: 'Failed to get quiz settings', details: err.message });
    }
};

// Save quiz settings
exports.saveQuizSettings = async (req, res) => {
    try {
        await QuestionsModel.saveQuizSettings(req.body);
        res.json({ message: 'Quiz settings saved successfully' });
    } catch (err) {
        res.status(500).json({ error: 'Failed to save quiz settings', details: err.message });
    }
};
