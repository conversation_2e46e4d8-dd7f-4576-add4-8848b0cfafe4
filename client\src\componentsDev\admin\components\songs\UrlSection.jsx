import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Button,
  IconButton,
  Modal,
  TextField,
  MenuItem,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { songService } from "./songService";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 500,
  maxHeight: "90vh",
  overflowY: "auto",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
};

const UrlSection = ({
  songUrls,
  setSongUrls,
  tempUrls,
  setTempUrls,
  editingIndex,
  selectedSongId,
}) => {
  const [urlTypes, setUrlTypes] = useState([]);
  const [selectedUrlType, setSelectedUrlType] = useState("");
  const [newUrl, setNewUrl] = useState("");
  const [openUrlModal, setOpenUrlModal] = useState(false);

  useEffect(() => {
    const fetchUrlTypes = async () => {
      try {
        const urlTypesData = await songService.fetchUrlTypes();
        setUrlTypes(urlTypesData);
      } catch (err) {
        console.error("Failed to fetch URL types:", err);
      }
    };
    fetchUrlTypes();
  }, []);

  const handleOpenUrlModal = () => {
    setOpenUrlModal(true);
    setSelectedUrlType("");
    setNewUrl("");
  };

  const handleCloseUrlModal = () => {
    setOpenUrlModal(false);
  };

  const handleAddUrl = async () => {
    if (!selectedUrlType || !newUrl) return;

    const urlType = urlTypes.find(
      (type) => type.url_type_name === selectedUrlType
    );
    if (!urlType) return;

    const urlPayload = {
      song_url: newUrl,
      url_type_lookup_id: urlType.url_type_lookup_id,
      url_type_name: urlType.url_type_name,
    };

    if (selectedSongId) {
      // Existing song - add to API immediately
      try {
        await songService.addSongUrl(selectedSongId, urlPayload);
        const updatedUrls = await songService.fetchSongUrls(selectedSongId);
        setSongUrls(updatedUrls);
      } catch (error) {
        console.error("Failed to add URL:", error);
      }
    } else {
      // New song - store in temp state
      setTempUrls((prev) => [...prev, urlPayload]);
    }

    handleCloseUrlModal();
    setSelectedUrlType("");
    setNewUrl("");
  };

  const handleDeleteSongUrl = async (songUrlID, isTemp = false) => {
    try {
      if (isTemp) {
        setTempUrls((prev) => prev.filter((_, index) => index !== songUrlID));
      } else {
        await songService.deleteSongUrl(songUrlID);
        if (selectedSongId) {
          const updatedUrls = await songService.fetchSongUrls(selectedSongId);
          setSongUrls(updatedUrls);
        }
      }
    } catch (error) {
      console.error("Failed to delete song url:", error);
    }
  };

  return (
    <Box>
      <Typography variant="subtitle1">Song URLs</Typography>
      <Button variant="outlined" onClick={handleOpenUrlModal}>
        + Add URL
      </Button>
      {(editingIndex !== null ? songUrls : tempUrls).map(
        (urlItem, index) => (
          <Box
            key={urlItem.song_url_id || index}
            sx={{ display: "flex", alignItems: "center", mb: 1 }}
          >
            <Typography>
              {urlItem.url_type_name}:{" "}
              <a
                href={urlItem.song_url}
                target="_blank"
                rel="noopener noreferrer"
              >
                {urlItem.song_url}
              </a>
            </Typography>
            <IconButton
              onClick={() =>
                handleDeleteSongUrl(
                  editingIndex !== null
                    ? urlItem.song_urls_id
                    : index,
                  editingIndex === null
                )
              }
              size="small"
              sx={{ ml: 1 }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        )
      )}

      {(editingIndex !== null
        ? songUrls.length === 0
        : tempUrls.length === 0) && (
        <Typography variant="body2" sx={{ color: "gray" }}>
          No URLs added for this song.
        </Typography>
      )}

      <Modal open={openUrlModal} onClose={handleCloseUrlModal}>
        <Box sx={modalStyle}>
          <Typography variant="h6">Add Song URL</Typography>
          <TextField
            select
            label="URL Type"
            value={selectedUrlType}
            onChange={(e) => setSelectedUrlType(e.target.value)}
            fullWidth
            margin="normal"
          >
            {urlTypes.map((type) => (
              <MenuItem
                key={type.url_type_lookup_id}
                value={type.url_type_name}
              >
                {type.url_type_name}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            label="URL"
            value={newUrl}
            onChange={(e) => setNewUrl(e.target.value)}
            fullWidth
            margin="normal"
          />
          <Button variant="contained" onClick={handleAddUrl}>
            Add
          </Button>
        </Box>
      </Modal>
    </Box>
  );
};

export default UrlSection;