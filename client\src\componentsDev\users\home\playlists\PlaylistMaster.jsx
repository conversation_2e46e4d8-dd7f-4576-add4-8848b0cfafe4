import { useState } from "react";
import {
  <PERSON>,
  Typography,
  IconButton,
  Collapse,
  Grid,
  Card,
  CardActionArea,
  CardMedia,
  Button,
  CircularProgress
} from "@mui/material";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { getImageUrl } from "../../../../utils/imageHelper";
import { usePlaylists } from "../../../hooks/usePlaylists";
import { useNavigate } from "react-router-dom";

const INITIAL_PLAYLIST_COUNT = 20;
const PLAYLISTS_PER_PAGE = 10;

const PlaylistMaster = () => {
  const [showPlaylists, setShowPlaylists] = useState(true);
  const [visibleCount, setVisibleCount] = useState(INITIAL_PLAYLIST_COUNT);
  const [loadingMore, setLoadingMore] = useState(false);
  const navigate = useNavigate();

  // Playlist
  const { data: playlists = []
  } = usePlaylists();

  const onPlaylistSelect = (playlistId) => {
    navigate(`/playlist/${playlistId}`);
  };


  const visiblePlaylists = playlists.slice(0, visibleCount);
  const hasMore = visibleCount < playlists.length;

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setVisibleCount((prev) => prev + PLAYLISTS_PER_PAGE);
      setLoadingMore(false);
    }, 800);
  };

  const cardStyle = {
    backgroundColor: "#4d4d4d",
    borderRadius: "14px",
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.3)",
    overflow: "hidden",
    width: "100%",
    border: "1px solid #666",
  };

  const cardMediaStyle = {
    width: "100%",
    height: "auto",
    aspectRatio: "1 / 1",
    objectFit: "cover",
  };

  const textStyle = {
    marginTop: "10px",
    textAlign: "center",
    color: "#FFF",
    fontSize: {
      xs: "12px",
      sm: "14px",
      md: "16px",
      lg: "18px",
    },
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    lineHeight: "1.2",
    maxWidth: "100%",
  };

  return (
    <Box name="playlists" id="playlists" sx={{ mb: 4 }}>
      {/* Sticky header that toggles the playlist section */}
      <Box
        sx={{
          position: "sticky",
          top: 90,
          zIndex: 1,
          backgroundColor: "background.default",
          py: 2,
          marginInline: "auto",
          width: "98% !important",
          px: 2,
          display: showPlaylists ? "block" : "none",
        }}
      />

      <Box sx={{ px: 2, display: "flex", flexDirection: "column" }}>
        {/* Toggle header */}
        <Box
          onClick={() => setShowPlaylists((prev) => !prev)}
          sx={{
            position: "sticky",
            top: 110,
            zIndex: 1,
            backgroundColor: "background.default",
            py: showPlaylists ? 1 : 0.5,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: "24px",
            marginBottom: showPlaylists ? "32px" : "8px",
            width: "100% !important",
            border: "1px solid #966300",
            paddingLeft: "15px",
            borderRadius: "5px",
            cursor: "pointer",
            transition: "all 0.3s ease",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: "24px" }}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              style={{ flexShrink: 0 }}
            >
              <path
                fill="none"
                stroke="#966300"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M8.625 17.65c0 1.574-1.26 2.85-2.812 2.85C4.259 20.5 3 19.224 3 17.65c0-1.573 1.26-2.849 2.813-2.849s2.812 1.276 2.812 2.85m0 0V5.462c0-.52.394-.954.909-1.001l10.375-.956A1 1 0 0 1 21 4.506V16.51m0 0c0 1.573-1.26 2.85-2.812 2.85c-1.554 0-2.813-1.277-2.813-2.85s1.26-2.85 2.813-2.85S21 14.936 21 16.51"
              />
            </svg>
            <Typography
              variant="h6"
              sx={{
                fontSize: "20px",
                fontWeight: "bold",
                color: "#FFF",
                lineHeight: "1.2",
              }}
            >
              Playlists {playlists.length !== 0 ? `(${playlists.length})` : ""}
            </Typography>
          </Box>

          <IconButton sx={{ color: "#966300", marginRight: "1.5rem" }}>
            {showPlaylists ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        {/* Playlist grid with collapse animation */}
        <Collapse in={showPlaylists} timeout="auto" unmountOnExit>
          <Box
            sx={{
              width: "100%",
              maxWidth: "1280px",
              margin: "0 auto",
              padding: "0 16px",
            }}
          >
            <Grid container spacing={3}>
              {visiblePlaylists.map((playlist, index) => (
                <Grid
                  item
                  xs={6}
                  sm={4}
                  md={3}
                  lg={2.4}
                  key={`${playlist.playlist_master_id || "playlist"}-${index}`}
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Card sx={cardStyle}>
                    <CardActionArea
                      onClick={() => onPlaylistSelect(playlist.playlist_master_id)}
                    >
                      <CardMedia
                        component="img"
                        sx={cardMediaStyle}
                        image={getImageUrl(playlist.image)}
                        alt={playlist.title}
                        onError={(e) => {
                          e.target.src = "/default-playlist.png"; // Fallback image
                        }}
                      />
                    </CardActionArea>
                  </Card>
                  <Typography sx={textStyle}>{playlist.title}</Typography>
                </Grid>
              ))}
            </Grid>

            {/* Load More Button */}
            {hasMore && (
              <Box sx={{ display: "flex", justifyContent: "center", mt: 4, mb: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleLoadMore}
                  disabled={loadingMore}
                  sx={{
                    background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                    color: "#fff",
                    fontWeight: "bold",
                    fontSize: "1rem",
                    px: 4,
                    py: 1,
                    borderRadius: "2rem",
                    boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
                    transition: "all 0.3s ease-in-out",
                    "&:hover": {
                      background: "linear-gradient(45deg, #ffa040, #ff8f00)",
                      transform: "scale(1.05)",
                    },
                    "&:disabled": {
                      opacity: 0.7,
                    },
                  }}
                >
                  {loadingMore ? (
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <CircularProgress size={20} thickness={5} color="inherit" />
                      <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                        Loading...
                      </Typography>
                    </Box>
                  ) : (
                    "Show More Playlists"
                  )}
                </Button>
              </Box>
            )}
          </Box>
        </Collapse>
      </Box>
    </Box>
  );
};

export default PlaylistMaster;