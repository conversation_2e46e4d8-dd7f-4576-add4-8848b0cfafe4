import {
  Container,
  Box,
  Paper,
  Typography,
  AppBar,
  Toolbar,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { Link } from "react-router-dom";
import Header from "./Header";

const AdminHomePage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const linkStyle = {
    textDecoration: "none",
    color: "inherit",
  };

  return (
    <>
      <AppBar position="sticky" style={{ backgroundColor: "#121212" }}>
        <Toolbar>
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, fontFamily: "Helvetica, Arial, sans-serif" }}
          >
            <Header />
          </Typography>
        </Toolbar>
      </AppBar>
      <Container maxWidth="lg" sx={{ mt: 3, mb: 3 }}>
        <Box
          display="flex"
          flexDirection={isMobile ? "column" : "row"}
          // justifyContent="center"
          // alignItems="center"
          gap={2} // Adding a gap for better spacing between cards
        >
          <Link to="/AdminRequestCompact" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px", // Consistent border radius
              }}
            >
              <img
                src="/pull_req.png"
                alt="Playlist Management"
                style={{
                  width: "100%",
                  height: 300,
                  objectFit: "cover",
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Request Compact
              </Typography>
            </Paper>
          </Link>
          <Link to="/adminrequests" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px",
              }}
            >
              <img
                src="/req.png"
                alt="Request List"
                style={{
                  width: "100%", // Full width of the card
                  height: 300, // Fixed height
                  objectFit: "cover", // Cover to ensure the image fills the area
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Request List
              </Typography>
            </Paper>
          </Link>
          <Link to="/allplaylistscreen" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px",
              }}
            >
              <img
                src="/req.png"
                alt="Request List"
                style={{
                  width: "100%", // Full width of the card
                  height: 300, // Fixed height
                  objectFit: "cover", // Cover to ensure the image fills the area
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Playlist
              </Typography>
            </Paper>
          </Link>
          <Link to="/allsongscreen" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px",
              }}
            >
              <img
                src="/req.png"
                alt="Request List"
                style={{
                  width: "100%", // Full width of the card
                  height: 300, // Fixed height
                  objectFit: "cover", // Cover to ensure the image fills the area
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Songs
              </Typography>
            </Paper>
          </Link>
          <Link to="/lookuptabs" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px",
              }}
            >
              <img
                src="/req.png"
                alt="Request List"
                style={{
                  width: "100%", // Full width of the card
                  height: 300, // Fixed height
                  objectFit: "cover", // Cover to ensure the image fills the area
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Look Ups
              </Typography>
            </Paper>
          </Link>
          <Link to="/ImportedSongs" style={linkStyle}>
            <Paper
              elevation={10}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                p: 2,
                backgroundColor: "#282828",
                "&:hover": { backgroundColor: "#383838" },
                borderRadius: "8px",
              }}
            >
              <img
                src="/req.png"
                alt="imported Songs"
                style={{
                  width: "100%", // Full width of the card
                  height: 300, // Fixed height
                  objectFit: "cover", // Cover to ensure the image fills the area
                }}
              />
              <Typography variant="h6" align="center" sx={{ mt: 1 }}>
                Import Songs
              </Typography>
            </Paper>
          </Link>
        </Box>
      </Container>
    </>
  );
};

export default AdminHomePage;