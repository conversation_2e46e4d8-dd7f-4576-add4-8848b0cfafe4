import React, { useState, useEffect } from "react";
import {
  Ta<PERSON>,
  Tab,
  <PERSON>,
  <PERSON><PERSON>,
  Typography,
  Container,
} from "@mui/material";
import CommonModal from "../CommonModal";
import LookupForm from "./LookupsForm";
import LookupList from "./LookupsList";
import { lookupService } from "./lookupService";
import { STORAGE_KEYS, TITLES } from "./constants";
import QuizLookupManagement from "./QuizLookupManagement";

const LookupsMaster = () => {
  const [tab, setTab] = useState(0);
  const [data, setData] = useState([[], [], [], [], [], []]);
  const [openModal, setOpenModal] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [showAllOptions, setShowAllOptions] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      await loadSettings();
    };
    loadData();
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('Loading data for types:', STORAGE_KEYS);
        const loadedData = await Promise.all(
          STORAGE_KEYS.map(async (type) => {
            try {
              const data = await lookupService.fetchItems(type, showAllOptions);
              console.log(`Loaded data for ${type}:`, data);
              return data;
            } catch (error) {
              console.error(`Failed to load data for ${type}:`, error);
              return []; // Return empty array on error
            }
          })
        );
        console.log('All loaded data:', loadedData);
        setData(loadedData);
      } catch (error) {
        console.error('Failed to load lookup data:', error);
      }
    };
    loadData();
  }, [showAllOptions]);

  const loadSettings = async () => {
    try {
      const response = await lookupService.getSettings();
      setShowAllOptions(response.data.showAllOptions);
    } catch (error) {
      console.error("Failed to load settings:", error);
      // Set default value if settings fail to load
      setShowAllOptions(true);
    }
  };

  const handleSettingsToggle = async (newValue) => {
    try {
      await lookupService.updateSettings(newValue);
      setShowAllOptions(newValue);

      const updatedSettings = [
        {
          id: 'request_options_mode',
          name: 'Request Options Mode',
          description: 'Controls which request options are shown to users',
          value: newValue,
          type: 'toggle'
        }
      ];

      setData((prevData) => {
        const updatedData = [...prevData];
        updatedData[4] = updatedSettings;
        return updatedData;
      });
    } catch (error) {
      console.error("Failed to update setting:", error);
    }
  };

  const handleDelete = async (typeIndex, itemIndex) => {
    const type = STORAGE_KEYS[typeIndex];
    const itemId = lookupService.getItemId(data[typeIndex][itemIndex], type);
    await lookupService.deleteItem(type, itemId);
    const updated = data[typeIndex].filter((_, i) => i !== itemIndex);
    setItems(typeIndex, updated);
  };

  const handleSave = async (newItem) => {
    const type = STORAGE_KEYS[tab];
    const payload = lookupService.createPayload(newItem, type);

    if (editIndex !== null) {
      const itemId = lookupService.getItemId(data[tab][editIndex], type);
      await lookupService.updateItem(type, itemId, payload);
    } else {
      await lookupService.createItem(type, payload);
    }

    const loadedData = await lookupService.fetchItems(type, showAllOptions);
    setData((prevData) => {
      const updatedData = [...prevData];
      updatedData[tab] = loadedData;
      return updatedData;
    });
    handleClose();
  };

  const handleClose = () => {
    setOpenModal(false);
    setEditIndex(null);
  };

  const setItems = (index, newItems) => {
    const updatedData = [...data];
    updatedData[index] = newItems;
    setData(updatedData);
  };

  const handleStatusToggle = async (e, index, tab, data, setItems, type) => {
    const updated = [...data[tab]];
    const item = updated[index];

    updated[index] = {
      ...updated[index],
      [lookupService.getFieldMap(type).status]: e.target.checked ? 1 : 0,
    };

    const payload = lookupService.createPayload(updated[index], type);

    try {
      const itemId = lookupService.getItemId(item, type);
      await lookupService.updateItem(type, itemId, payload);
      setItems(tab, updated);
    } catch (error) {
      console.error("Failed to update item status:", error);
    }
  };

  return (
    <>
      <Container
        sx={{
          mt: 2,
          pb: 4,
          bgcolor: "#121212",
          minHeight: "90vh",
          border: "1px solid",
        }}
      >
        <Box>
          <Tabs value={tab} onChange={(e, newValue) => setTab(newValue)}>
            {TITLES.map((label, index) => (
              <Tab
                key={label}
                label={`${label} ${index === 4 || index === 5 || index === 6 ? '' : 'Type'}`}
              />
            ))}
          </Tabs>

          {tab === 5 ? ( // Quiz tab (index 5)
            <QuizLookupManagement />
          ) : (
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mt: 2,
                  pb: 2,
                  borderBottom: "1px solid",
                }}
              >
                <Typography variant="h4">
                  {TITLES[tab]} {tab === 4 || tab === 5 ? '' : 'Type'}
                </Typography>
                {tab !== 4 && tab !== 5 && (
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => setOpenModal(true)}
                  >
                    Create New {TITLES[tab]} Type
                  </Button>
                )}
              </Box>

              {/* Display data for all tabs except Quiz (5) */}
              {tab !== 5 && (
                <LookupList
                  tab={tab}
                  data={data}
                  setItems={setItems}
                  handleDelete={handleDelete}
                  handleStatusToggle={handleStatusToggle}
                  handleSettingsToggle={handleSettingsToggle}
                  setEditIndex={setEditIndex}
                  setOpenModal={setOpenModal}
                />
              )}

              {/* Modal for tabs that need forms (not Settings or Quiz) */}
              {tab !== 4 && tab !== 5 && (
                <CommonModal
                  open={openModal}
                  onClose={handleClose}
                  title={
                    editIndex !== null
                      ? `Edit ${TITLES[tab]} Type`
                      : `Create ${TITLES[tab]} Type`
                  }
                >
                  <LookupForm
                    typeIndex={tab}
                    items={data[tab]}
                    handleClose={handleClose}
                    editIndex={editIndex}
                    handleSave={handleSave}
                  />
                </CommonModal>
              )}
            </>
          )}
        </Box>
      </Container>
    </>
  );
};

export default LookupsMaster;