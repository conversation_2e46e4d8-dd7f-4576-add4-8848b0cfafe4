-- Database Indexes for Performance Optimization
-- These indexes will be automatically created during schema synchronization

-- Songs Master Indexes
CREATE INDEX IF NOT EXISTS idx_songs_master_name ON songs_master(name);
CREATE INDEX IF NOT EXISTS idx_songs_master_artist ON songs_master(artist);
CREATE INDEX IF NOT EXISTS idx_songs_master_spotify_id ON songs_master(spotify_id);

-- Playlist Indexes
CREATE INDEX IF NOT EXISTS idx_playlist_songs_playlist ON playlist_songs(playlist_master_id);
CREATE INDEX IF NOT EXISTS idx_playlist_songs_song ON playlist_songs(songs_master_id);

-- Song URLs and Files Indexes
CREATE INDEX IF NOT EXISTS idx_song_urls_song ON song_urls(songs_master_id);
CREATE INDEX IF NOT EXISTS idx_song_files_song ON song_files(songs_master_id);

-- Song Genres Indexes
CREATE INDEX IF NOT EXISTS idx_song_genres_song ON song_genres(songs_master_id);
CREATE INDEX IF NOT EXISTS idx_song_genres_genre ON song_genres(genre_type_lookup_id);

-- Song Artists Indexes
CREATE INDEX IF NOT EXISTS idx_song_artists_song ON song_artists(songs_master_id);
CREATE INDEX IF NOT EXISTS idx_song_artists_artist ON song_artists(artist_id);

-- Song Requests Indexes
CREATE INDEX IF NOT EXISTS idx_song_requests_status ON song_requests(status);
CREATE INDEX IF NOT EXISTS idx_song_requests_created ON song_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_song_requests_expires ON song_requests(expires_at);
CREATE INDEX IF NOT EXISTS idx_song_requests_device ON song_requests(device_id);

-- Import Tables Indexes
CREATE INDEX IF NOT EXISTS idx_import_spotify_track_migrated ON ImportSpotifyTrackTemp(is_migrated);
CREATE INDEX IF NOT EXISTS idx_import_spotify_track_song ON ImportSpotifyTrackTemp(import_song_id);
CREATE INDEX IF NOT EXISTS idx_import_spotify_artist_song ON ImportSpotifyArtistTemp(import_song_id);
CREATE INDEX IF NOT EXISTS idx_import_track_urls_track ON ImportTrackUrlsTemp(track_id);

-- Spotify Tables Indexes
CREATE INDEX IF NOT EXISTS idx_spotify_songs_playlist ON spotify_songs(playlist_id);
CREATE INDEX IF NOT EXISTS idx_spotify_songs_name ON spotify_songs(name);
CREATE INDEX IF NOT EXISTS idx_spotify_songs_artist ON spotify_songs(artist);

-- Quiz Tables Indexes
CREATE INDEX IF NOT EXISTS idx_quiz_questions_song ON quiz_questions(songs_master_id);
CREATE INDEX IF NOT EXISTS idx_question_answers_q ON question_answers(question_id);

-- Unique Indexes for Import Tables
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_song_artist ON ImportSongTableTemp(song_name, artist_name);
