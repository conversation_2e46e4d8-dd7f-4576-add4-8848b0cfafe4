import React, { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Typography,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Box,
  Snackbar,
  Stack,
} from "@mui/material";
import axios from "axios";
import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import IconButton from "@mui/material/IconButton";

const YouTubeSearchAndSaveDialog = ({
  open,
  onClose,
  song,
  songs_master_id,
  urlTypes,
  searchTerms = ["karaoke"],
  onUrlSaved,
  songUrls = [],
}) => {
  const [loading, setLoading] = useState(false);
  const [videos, setVideos] = useState([]);
  const [selectingTypeFor, setSelectingTypeFor] = useState(null); // video object
  const [selectedTypeId, setSelectedTypeId] = useState("");
  const [snackbar, setSnackbar] = useState({ open: false, message: "", severity: "success" });

  useEffect(() => {
    if (open && song) {
      fetchYouTubeVideos();
    }
    // eslint-disable-next-line
  }, [open, song, JSON.stringify(searchTerms)]);

  const fetchYouTubeVideos = async () => {
    setLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/youtube/search`,
        {
          params: {
            song: song.name || song.song_name,
            artist: song.artist || song.artist_name,
            searchTerms: searchTerms.join(","),
          },
        }
      );
      setVideos(res.data);
    } catch (error) {
      console.error("YouTube fetch error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveUrl = async () => {
    if (!selectingTypeFor || !selectedTypeId) return;
    const urlType = urlTypes.find((type) => type.url_type_lookup_id === selectedTypeId);
    try {
      await axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
        songs_master_id,
        song_url: selectingTypeFor.url,
        url_type_lookup_id: urlType.url_type_lookup_id,
        url_type_name: urlType.url_type_name,
      });
      setSelectingTypeFor(null);
      setSelectedTypeId("");
      if (onUrlSaved) onUrlSaved();
    } catch (err) {
      alert("Failed to save URL");
    }
  };

  const handleQuickSave = async (video) => {
    // Find checked search terms
    const checkedTypes = urlTypes.filter((type) => searchTerms.includes(type.url_type_name));
    if (checkedTypes.length === 1) {
      const urlType = checkedTypes[0];
      try {
        await axios.post(`${process.env.REACT_APP_API_URL}/api/song-urls`, {
          songs_master_id,
          song_url: video.url,
          url_type_lookup_id: urlType.url_type_lookup_id,
          url_type_name: urlType.url_type_name,
        });
        setSnackbar({ open: true, message: "URL saved!", severity: "success" });
        if (onUrlSaved) onUrlSaved();
      } catch (err) {
        setSnackbar({ open: true, message: "Failed to save URL", severity: "error" });
      }
      return;
    }
    // If multiple or none, open the modal for selection
    setSelectingTypeFor(video);
  };

  // Helper to check if a video is already attached
  const isAttached = (video) => songUrls.some((url) => url.song_url === video.url);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ bgcolor: "#ff0000", color: "white" }}>
        🎬 YouTube Results for: {song?.name || song?.song_name} - {song?.artist || song?.artist_name}
        <br />
        <Typography variant="caption" color="white">
          Search Terms: {searchTerms.join(", ")}
        </Typography>
      </DialogTitle>
      <DialogContent dividers sx={{ p: { xs: 1, sm: 2 } }}>
        {loading ? (
          <CircularProgress />
        ) : videos.length === 0 ? (
          <Typography>No videos found.</Typography>
        ) : (
          <Stack spacing={2} mt={2}>
            {videos.map((video) => {
              const attached = isAttached(video);
              const formattedViews = Number(video.views).toLocaleString();
              const formattedLikes = Number(video.likes).toLocaleString();
              const publishedDate = video.publishedAt ? new Date(video.publishedAt).toLocaleDateString() : "";
              return (
                <Stack
                  key={video.videoId}
                  direction={{ xs: "column", sm: "row" }}
                  alignItems={{ xs: "flex-start", sm: "center" }}
                  spacing={2}
                  sx={{
                    border: "1px solid #ccc",
                    borderRadius: 2,
                    p: 1,
                    backgroundColor: attached ? "#333" : "#000",
                    opacity: attached ? 0.5 : 1,
                    transition: "all 0.3s ease",
                  }}
                >
                  <iframe
                    width="100%"
                    height="200"
                    src={`https://www.youtube.com/embed/${video.videoId}`}
                    title={video.title}
                    allowFullScreen
                    style={{ borderRadius: 4, maxWidth: 300 }}
                  />
                  <Stack flexGrow={1} spacing={0.5} sx={{ width: "100%" }}>
                    <Typography
                      color="white"
                      fontWeight="bold"
                      variant="subtitle2"
                      sx={{ wordBreak: "break-word" }}
                    >
                      {video.title}
                    </Typography>
                    <Typography
                      color="lightgray"
                      variant="caption"
                      sx={{ wordBreak: "break-word" }}
                    >
                      <a
                        href={video.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: "#90caf9", wordBreak: "break-word" }}
                      >
                        {video.url}
                      </a>
                    </Typography>
                    <Typography color="lightgray" variant="caption">
                      👁️ {formattedViews} views • 👍 {formattedLikes} likes
                      {publishedDate && ` • 📅 ${publishedDate}`}
                    </Typography>
                  </Stack>
                  <IconButton
                    color={attached ? "success" : "primary"}
                    disabled={attached}
                    onClick={() => !attached && handleQuickSave(video)}
                    sx={{
                      alignSelf: { xs: "flex-end", sm: "center" },
                      opacity: attached ? 1 : 0.7,
                      "&:hover": { opacity: 1 },
                    }}
                    aria-label={attached ? "Already added" : "Add YouTube URL"}
                  >
                    {attached ? <CheckIcon /> : <AddIcon />}
                  </IconButton>
                </Stack>
              );
            })}
          </Stack>
        )}
        {/* URL Type Selection Modal */}
        {selectingTypeFor && (
          <Box sx={{ mt: 2, p: 2, border: "1px solid #ccc", borderRadius: 2 }}>
            <Typography gutterBottom>
              Select URL Type for: <b>{selectingTypeFor.title}</b>
            </Typography>
            <FormControl fullWidth>
              <InputLabel>URL Type</InputLabel>
              <Select
                value={selectedTypeId}
                label="URL Type"
                onChange={(e) => setSelectedTypeId(e.target.value)}
              >
                {urlTypes.map((type) => (
                  <MenuItem key={type.url_type_lookup_id} value={type.url_type_lookup_id}>
                    {type.url_type_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Box sx={{ mt: 2, display: "flex", gap: 2 }}>
              <Button
                variant="contained"
                color="success"
                onClick={handleSaveUrl}
                disabled={!selectedTypeId}
              >
                Save URL
              </Button>
              <Button onClick={() => setSelectingTypeFor(null)}>Cancel</Button>
            </Box>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={2000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        message={snackbar.message}
      />
    </Dialog>
  );
};

export default YouTubeSearchAndSaveDialog; 