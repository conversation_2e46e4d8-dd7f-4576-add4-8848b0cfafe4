import React from 'react';
import { Paper, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

const DashboardCard = ({ to, imageSrc, title }) => {
  return (
    <Link to={to} style={{ textDecoration: 'none', color: 'inherit' }}>
      <Paper
        elevation={10}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          p: 2,
          backgroundColor: '#282828',
          '&:hover': { backgroundColor: '#383838' },
          borderRadius: '8px',
        }}
      >
        <img
          src={imageSrc}
          alt={title}
          style={{ width: '100%', height: 300, objectFit: 'cover' }}
        />
        <Typography variant="h6" align="center" sx={{ mt: 1 }}>
          {title}
        </Typography>
      </Paper>
    </Link>
  );
};

export default DashboardCard;