// components/dedication/Step2DedicationForm.jsx
import { useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Divider,
} from "@mui/material";
import SongHeader from "./SongHeader";

const Step2DedicationForm = ({ song, dedication, onDedicationSubmit, onBack }) => {
  const [dedicationText, setDedicationText] = useState(dedication || "");

  const handleSubmit = () => {
    onDedicationSubmit(dedicationText);
  };

  return (
    <>
      <SongHeader song={song} />
      <Divider sx={{ color: "#202020", height: "1px" }} />
      
      <Typography
        sx={{
          fontSize: "16px",
          fontWeight: "600",
          color: "#F9F9F9",
          my: 2,
        }}
      >
        Enter name or message for dedication.
      </Typography>
      
      <TextField
        autoFocus
        margin="dense"
        id="dedication"
        placeholder="Type your dedication message here..."
        type="text"
        fullWidth
        multiline
        rows={3}
        variant="outlined"
        value={dedicationText}
        onChange={(e) => setDedicationText(e.target.value)}
        sx={{
          mb: 4,
          "& label.Mui-focused": { color: "white" },
          "& .MuiOutlinedInput-root": {
            "&.Mui-focused fieldset": { borderColor: "#976609" },
            color: "#FFFFFF",
            "& textarea": {
              color: "#FFFFFF",
            },
          },
        }}
      />
      
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Button
          variant="outlined"
          onClick={onBack}
          sx={{
            mt: 2,
            width: "100%",
            color: "#976609",
            borderColor: "#976609",
            "&:hover": {
              borderColor: "#b7830e",
            },
          }}
        >
          Back
        </Button>
        
        <Button
          variant="contained"
          onClick={handleSubmit}
          sx={{
            mt: 2,
            width: "100%",
            backgroundColor: "#976609",
            color: "#ffff",
            "&:hover": {
              backgroundColor: "#b7830e",
            },
          }}
        >
          Next
        </Button>
      </Box>
    </>
  );
};

export default Step2DedicationForm;