const SettingsModel = require("../models/settingsModel");

const SettingsController = {
  // Get all settings
  getAll: async (req, res) => {
    try {
      const settings = await SettingsModel.getAll();
      res.json(settings);
    } catch (error) {
      console.error("Failed to fetch settings:", error);
      res.status(500).json({ 
        message: "Failed to fetch settings", 
        error: error.message 
      });
    }
  },

  // Get a specific setting by key
  getByKey: async (req, res) => {
    try {
      const { key } = req.params;
      const setting = await SettingsModel.getByKey(key);
      
      if (!setting) {
        return res.status(404).json({ message: "Setting not found" });
      }
      
      res.json(setting);
    } catch (error) {
      console.error("Failed to fetch setting:", error);
      res.status(500).json({ 
        message: "Failed to fetch setting", 
        error: error.message 
      });
    }
  },

  // Create or update a setting
  upsert: async (req, res) => {
    try {
      const { setting_key, setting_value, setting_description } = req.body;
      
      if (!setting_key || setting_value === undefined) {
        return res.status(400).json({ 
          message: "setting_key and setting_value are required" 
        });
      }
      
      const result = await SettingsModel.upsert(
        setting_key, 
        setting_value, 
        setting_description
      );
      
      res.json(result);
    } catch (error) {
      console.error("Failed to save setting:", error);
      res.status(500).json({ 
        message: "Failed to save setting", 
        error: error.message 
      });
    }
  },

  // Delete a setting
  delete: async (req, res) => {
    try {
      const { key } = req.params;
      const result = await SettingsModel.delete(key);
      
      if (result.deleted === 0) {
        return res.status(404).json({ message: "Setting not found" });
      }
      
      res.json({ message: "Setting deleted successfully" });
    } catch (error) {
      console.error("Failed to delete setting:", error);
      res.status(500).json({ 
        message: "Failed to delete setting", 
        error: error.message 
      });
    }
  },

  // Get request options mode specifically
  getRequestOptionsMode: async (req, res) => {
    try {
      const setting = await SettingsModel.getByKey('request_options_mode');
      const showAllOptions = setting ? setting.setting_value === 'true' : true; // default to true

      res.json({ showAllOptions });
    } catch (error) {
      console.error("Failed to fetch request options mode:", error);
      res.status(500).json({
        message: "Failed to fetch request options mode",
        error: error.message
      });
    }
  },

  // Update request options mode specifically
  updateRequestOptionsMode: async (req, res) => {
    try {
      const { showAllOptions } = req.body;

      if (typeof showAllOptions !== 'boolean') {
        return res.status(400).json({
          message: "showAllOptions must be a boolean"
        });
      }

      await SettingsModel.upsert(
        'request_options_mode',
        showAllOptions.toString(),
        'Controls which request options are shown to users'
      );

      res.json({ showAllOptions });
    } catch (error) {
      console.error("Failed to update request options mode:", error);
      res.status(500).json({
        message: "Failed to update request options mode",
        error: error.message
      });
    }
  }
};

module.exports = SettingsController;
