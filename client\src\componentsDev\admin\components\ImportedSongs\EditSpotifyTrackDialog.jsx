// EditSpotifyTrackDialog.jsx
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Stack,
  Typography,
  IconButton,
  Snackbar,
  Alert,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Paper,
} from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import AddIcon from "@mui/icons-material/Add";
import axios from "axios";
import AddUrlModal from "./AddUrlModal";

const EditSpotifyTrackDialog = ({ track, open, onClose, onSave, onSongUpdate, onUrlAdded }) => {
  const [form, setForm] = useState({});
  const [youtubeResults, setYoutubeResults] = useState([]);
  const [loadingYouTube, setLoadingYouTube] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [attachedUrls, setAttachedUrls] = useState([]);
  const [urlTypes, setUrlTypes] = useState([]);
  const [urlModalOpen, setUrlModalOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState(null);

  // Search terms state
  const [searchTerms, setSearchTerms] = useState({});

  useEffect(() => {
    if (track) {
      setForm(track);
      fetchUrlTypes();
      if (track.id) {
        fetchAttachedUrls(track.id);
      }
      setYoutubeResults([]);
    }
  }, [track]);

  useEffect(() => {
    if (urlTypes.length > 0) {
      // Initialize searchTerms state with all urlTypes, default 'karaoke' to true
      const initial = {};
      urlTypes.forEach(type => {
        initial[type.url_type_name] = type.url_type_name.toLowerCase() === 'karaoke';
      });
      setSearchTerms(initial);
    }
  }, [urlTypes]);

  const fetchAttachedUrls = async (trackID) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/import/track-urls/${trackID}`
      );
      setAttachedUrls(res.data);
    } catch (err) {
      console.error("Failed to fetch attached URLs", err);
    }
  };

  const fetchUrlTypes = async () => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/lookups/url`
      );
      setUrlTypes(res.data.filter((t) => t.status === 1));
    } catch (err) {
      console.error("Failed to fetch url_type_lookup:", err);
    }
  };

  const handleChange = (field) => (e) => {
    setForm((prev) => ({ ...prev, [field]: e.target.value }));
  };

  const handleSubmit = () => {
    onSave(form);
    // Notify parent component to refresh data
    if (onSongUpdate) {
      setTimeout(() => {
        onSongUpdate();
      }, 100);
    }
  };

  const handleSearchTermChange = (term) => (event) => {
    setSearchTerms(prev => ({
      ...prev,
      [term]: event.target.checked
    }));
  };

  const getSelectedSearchTerms = () => {
    return Object.entries(searchTerms)
      .filter(([_, isSelected]) => isSelected)
      .map(([term, _]) => term);
  };

  const handleFetchYouTube = async () => {
    if (!form.name || !form.artist) return;

    const selectedTerms = getSelectedSearchTerms();
    if (selectedTerms.length === 0) {
      setSnackbar({
        open: true,
        message: "❌ Please select at least one search term",
        severity: "error",
      });
      return;
    }

    setLoadingYouTube(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/youtube/search`,
        {
          params: { 
            song: form.name, 
            artist: form.artist,
            searchTerms: selectedTerms.join(',') // Send selected terms to backend
          },
        }
      );
      setYoutubeResults(res.data);
    } catch (err) {
      console.error("YouTube fetch error", err);
      setSnackbar({
        open: true,
        message: "❌ Failed to fetch YouTube videos",
        severity: "error",
      });
    } finally {
      setLoadingYouTube(false);
    }
  };

  const handleAddYouTubeUrlWithType = async (video, typeId) => {
    if (!track?.id) return;

    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL}/api/import/track-urls`,
        {
          track_id: track.id,
          song_url: video.url,
          url_type_lookup_id: typeId,
        }
      );

      await fetchAttachedUrls(track.id);
      setSnackbar({ open: true, message: "✅ URL added", severity: "success" });
      // Notify parent component to refresh data
      if (onSongUpdate) {
        setTimeout(() => {
          onSongUpdate();
        }, 100); // Small delay to ensure the URL is saved
      }
      // Trigger URL refresh in the table
      if (onUrlAdded) {
        setTimeout(() => {
          onUrlAdded();
        }, 200); // Slightly longer delay to ensure URL is saved
      }
    } catch (err) {
      console.error("Failed to save URL", err);
      setSnackbar({
        open: true,
        message: "❌ Failed to save URL",
        severity: "error",
      });
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
        <DialogTitle>Edit Spotify Track</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2} mt={1}>
            <TextField
              label="Name"
              value={form.name || ""}
              onChange={handleChange("name")}
              fullWidth
            />
            <TextField
              label="Artist"
              value={form.artist || ""}
              onChange={handleChange("artist")}
              fullWidth
            />
            <TextField
              label="Album"
              value={form.album || ""}
              onChange={handleChange("album")}
              fullWidth
            />
            <TextField
              label="Year"
              value={form.release_year || ""}
              onChange={handleChange("release_year")}
              fullWidth
            />
            <TextField
              label="Duration"
              value={form.duration || ""}
              onChange={handleChange("duration")}
              fullWidth
            />

            <Typography variant="h6" mt={3}>
              🔗 Attached URLs
            </Typography>
            {attachedUrls.length > 0 ? (
              <Stack spacing={1}>
                {attachedUrls.map((url, idx) => (
                  <Typography key={idx} sx={{ fontSize: "14px" }}>
                    {idx + 1}. {url.url_type_name}:{" "}
                    <a
                      href={url.song_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {url.song_url}
                    </a>
                  </Typography>
                ))}
              </Stack>
            ) : (
              <Typography color="text.secondary" fontSize="14px">
                No URLs attached.
              </Typography>
            )}

            <Typography variant="h6" mt={2}>
              🔎 Fetch YouTube URL
            </Typography>
            
            {/* Search Terms Selection */}
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" mb={1} color="text.secondary">
                Select search terms (karaoke selected by default):
              </Typography>
              <FormGroup row>
                {urlTypes.map(type => (
                  <FormControlLabel
                    key={type.url_type_lookup_id}
                    control={
                      <Checkbox
                        checked={!!searchTerms[type.url_type_name]}
                        onChange={handleSearchTermChange(type.url_type_name)}
                        size="small"
                      />
                    }
                    label={type.url_type_name}
                  />
                ))}
              </FormGroup>
            </Paper>

            <Button
              variant="outlined"
              onClick={handleFetchYouTube}
              disabled={loadingYouTube}
              sx={{ mt: 1 }}
            >
              {loadingYouTube ? "Searching..." : `Search on YouTube (${getSelectedSearchTerms().length} terms)`}
            </Button>

            {youtubeResults.length > 0 && (
              <Stack spacing={2} mt={2}>
                {youtubeResults.map((video) => {
                  const isAttached = attachedUrls.some(
                    (item) => item.song_url === video.url
                  );
                  const formattedViews = Number(video.views).toLocaleString();
                  const formattedLikes = Number(video.likes).toLocaleString();
                  const publishedDate = new Date(
                    video.publishedAt
                  ).toLocaleDateString();

                  return (
                    <Stack
                      key={video.videoId}
                      direction={{ xs: "column", sm: "row" }}
                      alignItems={{ xs: "flex-start", sm: "center" }}
                      spacing={2}
                      sx={{
                        border: "1px solid #ccc",
                        borderRadius: 2,
                        p: 1,
                        backgroundColor: isAttached ? "#333" : "#000",
                        opacity: isAttached ? 0.5 : 1,
                        transition: "all 0.3s ease",
                      }}
                    >
                      <iframe
                        width="100%"
                        height="200"
                        src={`https://www.youtube.com/embed/${video.videoId}`}
                        title={video.title}
                        allowFullScreen
                        style={{ borderRadius: 4, maxWidth: 300 }}
                      />
                      <Stack flexGrow={1} spacing={0.5} sx={{ width: "100%" }}>
                        <Typography
                          color="white"
                          fontWeight="bold"
                          variant="subtitle2"
                          sx={{ wordBreak: "break-word" }}
                        >
                          {video.title}
                        </Typography>
                        <Typography
                          color="lightgray"
                          variant="caption"
                          sx={{ wordBreak: "break-word" }}
                        >
                          <a
                            href={video.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: "#90caf9",
                              wordBreak: "break-word",
                            }}
                          >
                            {video.url}
                          </a>
                        </Typography>
                        <Typography color="lightgray" variant="caption">
                          👁️ {formattedViews} views • 👍 {formattedLikes} likes
                          • 📅 {publishedDate}
                        </Typography>
                      </Stack>

                      <IconButton
                        color={isAttached ? "success" : "primary"}
                        disabled={isAttached}
                        onClick={() => {
                          setSelectedVideo(video);
                          setUrlModalOpen(true);
                        }}
                        sx={{
                          alignSelf: { xs: "flex-end", sm: "center" },
                          opacity: isAttached ? 1 : 0.7,
                          "&:hover": { opacity: 1 },
                        }}
                      >
                        {isAttached ? <CheckIcon /> : <AddIcon />}
                      </IconButton>
                    </Stack>
                  );
                })}
              </Stack>
            )}
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} variant="filled">
          {snackbar.message}
        </Alert>
      </Snackbar>

      <AddUrlModal
        open={urlModalOpen}
        onClose={() => setUrlModalOpen(false)}
        video={selectedVideo}
        urlTypes={urlTypes}
        onAdd={handleAddYouTubeUrlWithType}
      />
    </>
  );
};

export default EditSpotifyTrackDialog;
