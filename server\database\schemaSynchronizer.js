const fs = require('fs');
const path = require('path');
const MigrationManager = require('./migrationManager');

class SchemaSynchronizer {
  constructor(dbPath, schemaPath) {
    this.dbPath = dbPath;
    this.schemaPath = schemaPath;
    this.migrationManager = new MigrationManager(dbPath, schemaPath);
    this.migrationLog = [];
  }

  async initialize() {
    await this.migrationManager.initialize();
  }

  async checkAndSync() {
    try {
      console.log('🔍 Checking schema synchronization...');
      
      // Read current schema file
      if (!fs.existsSync(this.schemaPath)) {
        throw new Error(`Schema file not found: ${this.schemaPath}`);
      }

      const schemaContent = fs.readFileSync(this.schemaPath, 'utf8');
      const newSchemaHash = this.migrationManager.calculateSchemaHash(schemaContent);
      const currentSchemaHash = await this.migrationManager.getCurrentSchemaHash();

      console.log(`📋 Current schema hash: ${currentSchemaHash || 'none'}`);
      console.log(`📋 New schema hash: ${newSchemaHash}`);

      if (currentSchemaHash === newSchemaHash) {
        console.log('✅ Schema is up to date - no migration needed');
        return { migrationNeeded: false, changes: [] };
      }

      console.log('🔄 Schema changes detected - starting migration...');
      const migrationResult = await this.performMigration(schemaContent, newSchemaHash);
      
      return {
        migrationNeeded: true,
        changes: this.migrationLog,
        result: migrationResult
      };

    } catch (error) {
      console.error('❌ Schema synchronization failed:', error);
      throw error;
    }
  }

  async performMigration(schemaContent, newSchemaHash) {
    const migrationStartTime = new Date().toISOString();
    this.migrationLog = [];

    try {
      // Parse the new schema
      const { tables: newTables, indexes: newIndexes } = this.migrationManager.parseSchemaFile(schemaContent);
      
      // Get current database structure
      const existingTables = await this.migrationManager.getAllTables();
      
      this.log('🚀 Starting schema migration');
      this.log(`📊 Found ${Object.keys(newTables).length} tables in schema file`);
      this.log(`📊 Found ${existingTables.length} existing tables in database`);

      // Handle table changes
      await this.handleTableChanges(newTables, existingTables);
      
      // Handle index changes
      await this.handleIndexChanges(newIndexes);

      // Record successful migration
      const version = `v${Date.now()}`;
      try {
        await this.migrationManager.recordMigration(
          newSchemaHash,
          version,
          this.migrationLog.join('\n'),
          'completed'
        );
      } catch (recordError) {
        // If recording fails due to duplicate hash, that's okay - migration was successful
        if (recordError.message.includes('UNIQUE constraint failed')) {
          this.log('ℹ️  Migration record already exists - migration was successful');
        } else {
          this.log(`⚠️  Failed to record migration: ${recordError.message}`);
        }
      }

      this.log('✅ Schema migration completed successfully');
      console.log('✅ Schema migration completed successfully');

      return {
        success: true,
        version,
        migrationLog: this.migrationLog
      };

    } catch (error) {
      this.log(`❌ Migration failed: ${error.message}`);

      // Record failed migration (with error handling)
      try {
        await this.migrationManager.recordMigration(
          newSchemaHash,
          `failed_${Date.now()}`,
          this.migrationLog.join('\n'),
          'failed'
        );
      } catch (recordError) {
        // If recording fails, log but don't throw
        this.log(`⚠️  Failed to record failed migration: ${recordError.message}`);
      }

      throw error;
    }
  }

  async handleTableChanges(newTables, existingTables) {
    // Filter out system tables from existing tables
    const userTables = existingTables.filter(table =>
      !table.startsWith('sqlite_') &&
      table !== 'schema_migrations'
    );

    // Create new tables
    for (const [tableName, createSQL] of Object.entries(newTables)) {
      if (!userTables.includes(tableName)) {
        await this.createNewTable(tableName, createSQL);
      } else {
        await this.updateExistingTable(tableName, createSQL);
      }
    }

    // Note: We don't automatically drop tables that are no longer in schema
    // This is a safety measure to prevent data loss
    const removedTables = userTables.filter(table =>
      !Object.keys(newTables).includes(table) &&
      table !== 'schema_migrations'
    );

    if (removedTables.length > 0) {
      this.log(`⚠️  Tables in database but not in schema (not removed): ${removedTables.join(', ')}`);
    }
  }

  async createNewTable(tableName, createSQL) {
    try {
      // Skip system tables
      if (tableName === 'sqlite_sequence' || tableName === 'sqlite_master' || tableName.startsWith('sqlite_')) {
        this.log(`ℹ️  Skipping system table: ${tableName}`);
        return;
      }

      await this.migrationManager.executeSQLStatement(createSQL);
      this.log(`✅ Created new table: ${tableName}`);
    } catch (error) {
      if (error.message.includes('object name reserved for internal use')) {
        this.log(`ℹ️  Skipping reserved table: ${tableName}`);
        return; // Don't throw for system tables
      } else if (error.message.includes('table') && error.message.includes('already exists')) {
        this.log(`ℹ️  Table ${tableName} already exists`);
        return; // Don't throw if table already exists
      } else {
        this.log(`❌ Failed to create table ${tableName}: ${error.message}`);
        throw error;
      }
    }
  }

  async updateExistingTable(tableName, newCreateSQL) {
    try {
      // Get current table structure
      const currentColumns = await this.migrationManager.getTableColumns(tableName);
      
      // Parse new table structure (simplified - you might want to use a proper SQL parser)
      const newColumns = this.parseTableColumns(newCreateSQL);
      
      // Find new columns
      const currentColumnNames = currentColumns.map(col => col.name.toLowerCase());
      const newColumnNames = newColumns.map(col => col.name.toLowerCase());
      
      const columnsToAdd = newColumns.filter(col => 
        !currentColumnNames.includes(col.name.toLowerCase())
      );

      // Add new columns
      for (const column of columnsToAdd) {
        await this.addColumnToTable(tableName, column);
      }

      if (columnsToAdd.length === 0) {
        this.log(`ℹ️  Table ${tableName} - no changes needed`);
      }

    } catch (error) {
      this.log(`❌ Failed to update table ${tableName}: ${error.message}`);
      // Don't throw here - continue with other tables
    }
  }

  parseTableColumns(createSQL) {
    // Enhanced column parser - extracts column definitions more carefully
    const columns = [];
    const lines = createSQL.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed &&
          !trimmed.startsWith('CREATE') &&
          !trimmed.startsWith('(') &&
          !trimmed.startsWith(')') &&
          !trimmed.startsWith('FOREIGN') &&
          !trimmed.startsWith('PRIMARY') &&
          !trimmed.startsWith('UNIQUE') &&
          !trimmed.startsWith('CHECK') &&
          !trimmed.startsWith('CONSTRAINT') &&
          !trimmed.includes('--') &&
          trimmed !== '') {

        const columnMatch = trimmed.match(/^(\w+)\s+(.+?)(?:,\s*)?$/);
        if (columnMatch) {
          let definition = columnMatch[2].replace(/,$/, '').trim();

          // Handle NOT NULL columns by adding DEFAULT if missing
          if (definition.includes('NOT NULL') && !definition.includes('DEFAULT')) {
            // Add appropriate default based on data type
            if (definition.includes('INTEGER')) {
              definition = definition.replace('NOT NULL', 'DEFAULT 0 NOT NULL');
            } else if (definition.includes('TEXT')) {
              definition = definition.replace('NOT NULL', 'DEFAULT \'\' NOT NULL');
            } else if (definition.includes('REAL')) {
              definition = definition.replace('NOT NULL', 'DEFAULT 0.0 NOT NULL');
            } else if (definition.includes('DATETIME')) {
              definition = definition.replace('NOT NULL', 'DEFAULT CURRENT_TIMESTAMP NOT NULL');
            }
          }

          columns.push({
            name: columnMatch[1],
            definition: definition
          });
        }
      }
    }

    return columns;
  }

  async addColumnToTable(tableName, column) {
    try {
      const sql = `ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.definition}`;
      await this.migrationManager.executeSQLStatement(sql);
      this.log(`✅ Added column ${column.name} to table ${tableName}`);
    } catch (error) {
      // Handle specific SQLite errors gracefully
      if (error.message.includes('Cannot add a NOT NULL column with default value NULL')) {
        this.log(`⚠️  Skipping column ${column.name} in table ${tableName}: NOT NULL constraint issue`);
        return; // Don't throw, just skip this column
      } else if (error.message.includes('duplicate column name')) {
        this.log(`ℹ️  Column ${column.name} already exists in table ${tableName}`);
        return; // Don't throw, column already exists
      } else {
        this.log(`❌ Failed to add column ${column.name} to table ${tableName}: ${error.message}`);
        // Don't throw for column addition failures - continue with other operations
      }
    }
  }

  async handleIndexChanges(newIndexes) {
    // For simplicity, we'll recreate all indexes
    // In production, you might want to be more selective
    
    for (const indexSQL of newIndexes) {
      try {
        await this.migrationManager.executeSQLStatement(indexSQL);
        const indexMatch = indexSQL.match(/CREATE.*INDEX\s+(\w+)/i);
        const indexName = indexMatch ? indexMatch[1] : 'unknown';
        this.log(`✅ Created/updated index: ${indexName}`);
      } catch (error) {
        // Index might already exist - that's okay
        if (!error.message.includes('already exists')) {
          this.log(`⚠️  Index creation warning: ${error.message}`);
        }
      }
    }
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    this.migrationLog.push(logMessage);
    console.log(logMessage);
  }

  async close() {
    await this.migrationManager.close();
  }
}

module.exports = SchemaSynchronizer;
