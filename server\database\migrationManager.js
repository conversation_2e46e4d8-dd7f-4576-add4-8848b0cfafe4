const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const sqlite3 = require('sqlite3').verbose();

class MigrationManager {
  constructor(dbPath, schemaPath) {
    this.dbPath = dbPath;
    this.schemaPath = schemaPath;
    this.db = null;
    this.migrationTableName = 'schema_migrations';
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('✅ Connected to SQLite database for migration management');
          this.createMigrationTable().then(resolve).catch(reject);
        }
      });
    });
  }

  async createMigrationTable() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${this.migrationTableName} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        schema_hash TEXT UNIQUE NOT NULL,
        schema_version TEXT NOT NULL,
        migration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        migration_log TEXT,
        status TEXT DEFAULT 'completed'
      )
    `;

    return new Promise((resolve, reject) => {
      this.db.run(createTableSQL, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('✅ Migration tracking table ready');
          resolve();
        }
      });
    });
  }

  calculateSchemaHash(schemaContent) {
    // Remove comments and normalize whitespace for consistent hashing
    const normalizedSchema = schemaContent
      .replace(/--.*$/gm, '') // Remove SQL comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    return crypto.createHash('sha256').update(normalizedSchema).digest('hex');
  }

  async getCurrentSchemaHash() {
    return new Promise((resolve, reject) => {
      const query = `SELECT schema_hash FROM ${this.migrationTableName} ORDER BY id DESC LIMIT 1`;
      this.db.get(query, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row ? row.schema_hash : null);
        }
      });
    });
  }

  async getTableSchema(tableName) {
    return new Promise((resolve, reject) => {
      const query = `SELECT sql FROM sqlite_master WHERE type='table' AND name=?`;
      this.db.get(query, [tableName], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row ? row.sql : null);
        }
      });
    });
  }

  async getAllTables() {
    return new Promise((resolve, reject) => {
      const query = `SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'schema_migrations'`;
      this.db.all(query, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const tables = rows.map(row => row.name).filter(name =>
            name !== 'sqlite_sequence' &&
            name !== 'sqlite_master' &&
            !name.startsWith('sqlite_')
          );
          resolve(tables);
        }
      });
    });
  }

  async getTableColumns(tableName) {
    return new Promise((resolve, reject) => {
      const query = `PRAGMA table_info(${tableName})`;
      this.db.all(query, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  parseSchemaFile(schemaContent) {
    // Remove comments and normalize content
    const cleanContent = schemaContent
      .replace(/--.*$/gm, '') // Remove SQL comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .trim();

    // Split schema into individual CREATE statements
    const statements = cleanContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && stmt.toUpperCase().includes('CREATE'));

    const tables = {};
    const indexes = [];

    statements.forEach(stmt => {
      if (stmt.toUpperCase().includes('CREATE TABLE')) {
        // Extract table name more carefully
        const tableMatch = stmt.match(/CREATE TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
        if (tableMatch) {
          const tableName = tableMatch[1];

          // Skip system tables
          if (tableName === 'sqlite_sequence' || tableName === 'sqlite_master') {
            return;
          }

          tables[tableName] = stmt;
        }
      } else if (stmt.toUpperCase().includes('CREATE INDEX') || stmt.toUpperCase().includes('CREATE UNIQUE INDEX')) {
        indexes.push(stmt);
      }
    });

    return { tables, indexes };
  }

  async executeSQLStatement(sql) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes, lastID: this.lastID });
        }
      });
    });
  }

  async backupTable(tableName) {
    const backupTableName = `${tableName}_backup_${Date.now()}`;
    const sql = `CREATE TABLE ${backupTableName} AS SELECT * FROM ${tableName}`;
    
    try {
      await this.executeSQLStatement(sql);
      console.log(`✅ Created backup table: ${backupTableName}`);
      return backupTableName;
    } catch (error) {
      console.error(`❌ Failed to backup table ${tableName}:`, error);
      throw error;
    }
  }

  async recordMigration(schemaHash, version, log, status = 'completed') {
    // First check if this hash already exists
    const existingRecord = await new Promise((resolve, reject) => {
      const checkSql = `SELECT id FROM ${this.migrationTableName} WHERE schema_hash = ?`;
      this.db.get(checkSql, [schemaHash], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });

    if (existingRecord) {
      // Update existing record instead of inserting
      const updateSql = `UPDATE ${this.migrationTableName} SET schema_version = ?, migration_log = ?, status = ?, migration_date = CURRENT_TIMESTAMP WHERE schema_hash = ?`;

      return new Promise((resolve, reject) => {
        this.db.run(updateSql, [version, log, status, schemaHash], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(existingRecord.id);
          }
        });
      });
    } else {
      // Insert new record
      const insertSql = `INSERT INTO ${this.migrationTableName} (schema_hash, schema_version, migration_log, status) VALUES (?, ?, ?, ?)`;

      return new Promise((resolve, reject) => {
        this.db.run(insertSql, [schemaHash, version, log, status], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        });
      });
    }
  }

  async clearDuplicateMigrations() {
    return new Promise((resolve, reject) => {
      // Remove duplicate schema_hash entries, keeping only the latest one
      const sql = `
        DELETE FROM ${this.migrationTableName}
        WHERE id NOT IN (
          SELECT MAX(id)
          FROM ${this.migrationTableName}
          GROUP BY schema_hash
        )
      `;

      this.db.run(sql, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('✅ Cleared duplicate migration records');
          resolve();
        }
      });
    });
  }

  async resetMigrationState() {
    return new Promise((resolve, reject) => {
      // Clear all migration records (use with caution)
      const sql = `DELETE FROM ${this.migrationTableName}`;

      this.db.run(sql, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('✅ Reset migration state');
          resolve();
        }
      });
    });
  }

  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          } else {
            console.log('✅ Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

module.exports = MigrationManager;
